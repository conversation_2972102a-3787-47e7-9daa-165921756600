// Digital Freight Platform Database Schema
// This schema defines the database structure for the freight forwarding SaaS platform

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User management for freight forwarders
model User {
  id           String   @id @default(cuid())
  email        String   @unique
  passwordHash String   @map("password_hash")
  firstName    String   @map("first_name")
  lastName     String   @map("last_name")
  companyName  String?  @map("company_name")
  isActive     Boolean  @default(true) @map("is_active")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  searches Search[]

  @@map("users")
}

// Master data for ports worldwide
model Port {
  id          String  @id @default(cuid())
  code        String  @unique // e.g., "SHG", "RTM", "LAX"
  name        String  // e.g., "Shanghai", "Rotterdam", "Los Angeles"
  countryCode String  @map("country_code") // ISO country code
  countryName String  @map("country_name")
  latitude    Float?
  longitude   Float?
  isActive    Boolean @default(true) @map("is_active")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  originSearches      Search[]       @relation("OriginPort")
  destinationSearches Search[]       @relation("DestinationPort")
  portRotations       PortRotation[]

  @@map("ports")
}

// Master data for shipping carriers
model Carrier {
  id          String   @id @default(cuid())
  name        String   // e.g., "Maersk", "Hapag-Lloyd"
  code        String   @unique // e.g., "MAEU", "HLCU"
  apiEndpoint String?  @map("api_endpoint")
  apiKey      String?  @map("api_key") // Encrypted API key
  isActive    Boolean  @default(true) @map("is_active")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  searchResults SearchResult[]

  @@map("carriers")
}

// User search queries for analytics and caching
model Search {
  id                String   @id @default(cuid())
  userId            String   @map("user_id")
  originPortId      String   @map("origin_port_id")
  destinationPortId String   @map("destination_port_id")
  departureDate     DateTime @map("departure_date")
  searchTimestamp   DateTime @default(now()) @map("search_timestamp")
  resultsCount      Int      @default(0) @map("results_count")
  searchDurationMs  Int?     @map("search_duration_ms")

  // Relations
  user            User           @relation(fields: [userId], references: [id], onDelete: Cascade)
  originPort      Port           @relation("OriginPort", fields: [originPortId], references: [id])
  destinationPort Port           @relation("DestinationPort", fields: [destinationPortId], references: [id])
  searchResults   SearchResult[]

  @@map("searches")
}

// Shipping rotation results from carrier APIs
model SearchResult {
  id              String   @id @default(cuid())
  searchId        String   @map("search_id")
  carrierId       String   @map("carrier_id")
  vesselName      String   @map("vessel_name")
  voyageNumber    String?  @map("voyage_number")
  departureDate   DateTime @map("departure_date")
  arrivalDate     DateTime @map("arrival_date")
  transitDays     Int      @map("transit_days")
  rawApiResponse  Json?    @map("raw_api_response") // Store original API response for debugging
  createdAt       DateTime @default(now()) @map("created_at")

  // Relations
  search        Search         @relation(fields: [searchId], references: [id], onDelete: Cascade)
  carrier       Carrier        @relation(fields: [carrierId], references: [id])
  portRotations PortRotation[]

  @@map("search_results")
}

// Port rotation sequence for each shipping route
model PortRotation {
  id             String @id @default(cuid())
  searchResultId String @map("search_result_id")
  portId         String @map("port_id")
  sequenceOrder  Int    @map("sequence_order") // Order in the rotation (1, 2, 3, etc.)
  arrivalDate    DateTime? @map("arrival_date")
  departureDate  DateTime? @map("departure_date")

  // Relations
  searchResult SearchResult @relation(fields: [searchResultId], references: [id], onDelete: Cascade)
  port         Port         @relation(fields: [portId], references: [id])

  @@unique([searchResultId, sequenceOrder])
  @@map("port_rotations")
}

// Indexes for performance optimization
// These will be added as separate migration files for better control
