import { PrismaClient } from '../src/generated/prisma';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Seed Carriers
  console.log('📦 Seeding carriers...');
  const carriers = await Promise.all([
    prisma.carrier.upsert({
      where: { code: 'MAEU' },
      update: {},
      create: {
        name: 'Maersk Line',
        code: 'MAEU',
        apiEndpoint: 'https://api.maersk.com',
        isActive: true,
      },
    }),
    prisma.carrier.upsert({
      where: { code: 'HLCU' },
      update: {},
      create: {
        name: '<PERSON>pa<PERSON>-<PERSON>',
        code: 'HLCU',
        apiEndpoint: 'https://api.hapag-lloyd.com',
        isActive: true,
      },
    }),
    prisma.carrier.upsert({
      where: { code: 'CMDU' },
      update: {},
      create: {
        name: 'CMA CGM',
        code: 'CMDU',
        apiEndpoint: 'https://api.cma-cgm.com',
        isActive: true,
      },
    }),
    prisma.carrier.upsert({
      where: { code: 'MSCU' },
      update: {},
      create: {
        name: 'MS<PERSON>',
        code: 'MSCU',
        apiEndpoint: 'https://api.msc.com',
        isActive: true,
      },
    }),
  ]);

  console.log(`✅ Created ${carriers.length} carriers`);

  // Seed Major Ports
  console.log('🏗️ Seeding ports...');
  const ports = await Promise.all([
    // Asian Ports
    prisma.port.upsert({
      where: { code: 'SHG' },
      update: {},
      create: {
        code: 'SHG',
        name: 'Shanghai',
        countryCode: 'CN',
        countryName: 'China',
        latitude: 31.2304,
        longitude: 121.4737,
      },
    }),
    prisma.port.upsert({
      where: { code: 'SZX' },
      update: {},
      create: {
        code: 'SZX',
        name: 'Shenzhen',
        countryCode: 'CN',
        countryName: 'China',
        latitude: 22.5431,
        longitude: 114.0579,
      },
    }),
    prisma.port.upsert({
      where: { code: 'SIN' },
      update: {},
      create: {
        code: 'SIN',
        name: 'Singapore',
        countryCode: 'SG',
        countryName: 'Singapore',
        latitude: 1.2966,
        longitude: 103.7764,
      },
    }),
    prisma.port.upsert({
      where: { code: 'HKG' },
      update: {},
      create: {
        code: 'HKG',
        name: 'Hong Kong',
        countryCode: 'HK',
        countryName: 'Hong Kong',
        latitude: 22.3193,
        longitude: 114.1694,
      },
    }),
    // European Ports
    prisma.port.upsert({
      where: { code: 'RTM' },
      update: {},
      create: {
        code: 'RTM',
        name: 'Rotterdam',
        countryCode: 'NL',
        countryName: 'Netherlands',
        latitude: 51.9225,
        longitude: 4.47917,
      },
    }),
    prisma.port.upsert({
      where: { code: 'HAM' },
      update: {},
      create: {
        code: 'HAM',
        name: 'Hamburg',
        countryCode: 'DE',
        countryName: 'Germany',
        latitude: 53.5511,
        longitude: 9.9937,
      },
    }),
    prisma.port.upsert({
      where: { code: 'ANR' },
      update: {},
      create: {
        code: 'ANR',
        name: 'Antwerp',
        countryCode: 'BE',
        countryName: 'Belgium',
        latitude: 51.2194,
        longitude: 4.4025,
      },
    }),
    // North American Ports
    prisma.port.upsert({
      where: { code: 'LAX' },
      update: {},
      create: {
        code: 'LAX',
        name: 'Los Angeles',
        countryCode: 'US',
        countryName: 'United States',
        latitude: 33.7701,
        longitude: -118.1937,
      },
    }),
    prisma.port.upsert({
      where: { code: 'NYC' },
      update: {},
      create: {
        code: 'NYC',
        name: 'New York',
        countryCode: 'US',
        countryName: 'United States',
        latitude: 40.6892,
        longitude: -74.0445,
      },
    }),
    prisma.port.upsert({
      where: { code: 'VAN' },
      update: {},
      create: {
        code: 'VAN',
        name: 'Vancouver',
        countryCode: 'CA',
        countryName: 'Canada',
        latitude: 49.2827,
        longitude: -123.1207,
      },
    }),
  ]);

  console.log(`✅ Created ${ports.length} ports`);

  console.log('🎉 Database seeding completed successfully!');
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
