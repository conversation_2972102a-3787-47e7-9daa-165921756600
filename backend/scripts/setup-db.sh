#!/bin/bash

# Digital Freight Platform - Database Setup Script
# This script helps set up the PostgreSQL database for development

echo "🚀 Digital Freight Platform - Database Setup"
echo "============================================="

# Check if PostgreSQL is installed
if ! command -v psql &> /dev/null; then
    echo "❌ PostgreSQL is not installed."
    echo "Please install PostgreSQL first:"
    echo "  - macOS: brew install postgresql"
    echo "  - Ubuntu: sudo apt-get install postgresql postgresql-contrib"
    echo "  - Windows: Download from https://www.postgresql.org/download/"
    exit 1
fi

echo "✅ PostgreSQL is installed"

# Check if PostgreSQL service is running
if ! pg_isready -q; then
    echo "⚠️  PostgreSQL service is not running."
    echo "Please start PostgreSQL service:"
    echo "  - macOS: brew services start postgresql"
    echo "  - Ubuntu: sudo systemctl start postgresql"
    echo "  - Windows: Start PostgreSQL service from Services"
    exit 1
fi

echo "✅ PostgreSQL service is running"

# Database configuration
DB_NAME="digital_freight"
DB_USER="postgres"
DB_HOST="localhost"
DB_PORT="5432"

echo "📊 Creating database: $DB_NAME"

# Create database if it doesn't exist
createdb -h $DB_HOST -p $DB_PORT -U $DB_USER $DB_NAME 2>/dev/null || echo "Database $DB_NAME already exists"

echo "✅ Database setup complete"
echo ""
echo "Next steps:"
echo "1. Run: npm run db:migrate"
echo "2. Run: npm run db:seed"
echo "3. Run: npm run dev"
echo ""
echo "🎉 Ready to start development!"
