import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import { DatabaseService } from './services/database.service';
import authRoutes from './routes/auth.routes';
import portRoutes from './routes/port.routes';
import searchRoutes from './routes/search.routes';
import { config, isProduction, isDevelopment } from './config/environment';
import { generalRateLimiter } from './middleware/rateLimiter.middleware';

const app = express();

// Security middleware
app.use(helmet({
  contentSecurityPolicy: isProduction() ? undefined : false, // Disable CSP in development
  crossOriginEmbedderPolicy: false, // Allow embedding for development
}));

// Trust proxy in production (for rate limiting and IP detection)
if (isProduction()) {
  app.set('trust proxy', 1);
}

// Global rate limiting (replaced with our custom rate limiter)
app.use(generalRateLimiter.middleware);

// CORS configuration
app.use(cors({
  origin: config.CORS_ORIGIN,
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
}));

// Body parsing middleware with production limits
app.use(express.json({
  limit: isProduction() ? '1mb' : '10mb',
  strict: true,
}));
app.use(express.urlencoded({
  extended: true,
  limit: isProduction() ? '1mb' : '10mb',
}));

// Logging middleware
app.use(morgan(isProduction() ? 'combined' : 'dev'));

// Health check endpoint
app.get('/health', async (req, res) => {
  const dbHealth = await DatabaseService.getHealthInfo();
  const dbStats = await DatabaseService.getStats();

  res.status(200).json({
    status: 'OK',
    message: 'Digital Freight API is running',
    timestamp: new Date().toISOString(),
    database: dbHealth,
    stats: dbStats,
  });
});

// API routes
app.use('/api/auth', authRoutes);
app.use('/api/ports', portRoutes);
app.use('/api/search', searchRoutes);

// API info endpoint
app.get('/api', (req, res) => {
  res.json({
    message: 'Digital Freight API',
    version: '1.0.0',
    endpoints: {
      health: '/health',
      auth: '/api/auth',
      ports: '/api/ports',
      search: '/api/search',
    },
  });
});

// Error handling middleware
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  // Log error for monitoring
  console.error('UNHANDLED_ERROR', {
    error: err.message,
    stack: isDevelopment() ? err.stack : undefined,
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    timestamp: new Date().toISOString(),
  });

  res.status(500).json({
    error: 'Internal server error',
    message: isDevelopment() ? err.message : 'An unexpected error occurred',
    ...(isDevelopment() && { stack: err.stack }),
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Route not found',
    message: `Cannot ${req.method} ${req.originalUrl}`,
    availableEndpoints: {
      health: '/health',
      auth: '/api/auth',
      ports: '/api/ports',
      search: '/api/search',
    },
  });
});

// Graceful shutdown handling
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  process.exit(0);
});

// Start server
const server = app.listen(config.PORT, () => {
  console.log(`🚀 Digital Freight API server running on port ${config.PORT}`);
  console.log(`📊 Health check: http://localhost:${config.PORT}/health`);
  console.log(`🌍 Environment: ${config.NODE_ENV}`);
  console.log(`🔒 Security: ${isProduction() ? 'Production mode' : 'Development mode'}`);
  console.log(`🌐 CORS Origin: ${config.CORS_ORIGIN}`);
  console.log(`⚡ Cache TTL: ${config.CACHE_TTL_MINUTES} minutes`);

  if (isDevelopment()) {
    console.log(`🔧 Development features enabled`);
  }
});

export default app;
