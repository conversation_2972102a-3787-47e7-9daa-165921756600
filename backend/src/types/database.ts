// Database types for the Digital Freight Platform
// These types will be auto-generated by Prisma, but we define them here for reference

export interface CreateUserInput {
  email: string;
  passwordHash: string;
  firstName: string;
  lastName: string;
  companyName?: string;
}

export interface CreatePortInput {
  code: string;
  name: string;
  countryCode: string;
  countryName: string;
  latitude?: number;
  longitude?: number;
}

export interface CreateCarrierInput {
  name: string;
  code: string;
  apiEndpoint?: string;
  apiKey?: string;
}

export interface CreateSearchInput {
  userId: string;
  originPortId: string;
  destinationPortId: string;
  departureDate: Date;
}

export interface CreateSearchResultInput {
  searchId: string;
  carrierId: string;
  vesselName: string;
  voyageNumber?: string;
  departureDate: Date;
  arrivalDate: Date;
  transitDays: number;
  rawApiResponse?: any;
}

export interface CreatePortRotationInput {
  searchResultId: string;
  portId: string;
  sequenceOrder: number;
  arrivalDate?: Date;
  departureDate?: Date;
}

// Search query interface for API endpoints
export interface SearchQuery {
  originPortCode: string;
  destinationPortCode: string;
  departureDate: string; // ISO date string
}

// Search result response interface
export interface SearchResultResponse {
  id: string;
  carrier: {
    name: string;
    code: string;
  };
  vesselName: string;
  voyageNumber?: string;
  departureDate: string;
  arrivalDate: string;
  transitDays: number;
  portRotation: Array<{
    portCode: string;
    portName: string;
    sequenceOrder: number;
    arrivalDate?: string;
    departureDate?: string;
  }>;
}

// Pagination interface
export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Filter interface for search results
export interface SearchResultFilters {
  carrierIds?: string[];
  minTransitDays?: number;
  maxTransitDays?: number;
  departureDateFrom?: Date;
  departureDateTo?: Date;
}
