import { Request, Response, NextFunction } from 'express';
import { AuthService } from '../services/auth.service';

// Extend Express Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        email: string;
        firstName: string;
        lastName: string;
        companyName?: string | null;
        createdAt?: Date;
      };
    }
  }
}

/**
 * Authentication middleware to protect routes
 */
export const authenticateToken = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      res.status(401).json({
        error: 'Access denied',
        message: 'No token provided',
      });
      return;
    }

    // Verify token
    const decoded = AuthService.verifyToken(token);
    
    // Get user details
    const user = await AuthService.getUserById(decoded.userId);
    
    // Add user to request object
    req.user = {
      ...user,
      companyName: user.companyName || undefined,
    };
    
    next();
  } catch (error) {
    console.error('Authentication error:', error);
    res.status(403).json({
      error: 'Access denied',
      message: error instanceof Error ? error.message : 'Invalid token',
    });
  }
};

/**
 * Optional authentication middleware - doesn't fail if no token
 */
export const optionalAuth = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (token) {
      const decoded = AuthService.verifyToken(token);
      const user = await AuthService.getUserById(decoded.userId);
      req.user = {
        ...user,
        companyName: user.companyName || undefined,
      };
    }
    
    next();
  } catch (error) {
    // Continue without authentication if token is invalid
    next();
  }
};

/**
 * Middleware to check if user is authenticated (for route protection)
 */
export const requireAuth = (req: Request, res: Response, next: NextFunction): void => {
  if (!req.user) {
    res.status(401).json({
      error: 'Authentication required',
      message: 'Please log in to access this resource',
    });
    return;
  }
  next();
};

export default {
  authenticateToken,
  optionalAuth,
  requireAuth,
};
