import { prisma } from '../config/database';
import { MaerskService, NormalizedSearchResult } from './maersk.service';

export interface SearchRequest {
  originPortId: string;
  destinationPortId: string;
  departureDate: string; // YYYY-MM-DD format
  userId?: string;
  carriers?: string[]; // Optional filter by carrier codes
}

export interface SearchResponse {
  searchId: string;
  results: NormalizedSearchResult[];
  totalResults: number;
  searchDurationMs: number;
  carriers: string[];
  metadata: {
    originPort: {
      id: string;
      code: string;
      name: string;
      country: string;
    };
    destinationPort: {
      id: string;
      code: string;
      name: string;
      country: string;
    };
    departureDate: string;
    searchTimestamp: string;
  };
}

export interface SearchFilters {
  maxTransitDays?: number;
  minTransitDays?: number;
  carriers?: string[];
  sortBy?: 'departureDate' | 'arrivalDate' | 'transitDays' | 'carrier';
  sortOrder?: 'asc' | 'desc';
}

export class SearchService {
  private maerskService: MaerskService;

  constructor() {
    this.maerskService = new MaerskService();
  }

  /**
   * Perform comprehensive search across multiple carriers
   */
  async searchRoutes(request: SearchRequest): Promise<SearchResponse> {
    const startTime = Date.now();
    
    try {
      // Validate and get port information
      const [originPort, destinationPort] = await Promise.all([
        this.getPortById(request.originPortId),
        this.getPortById(request.destinationPortId),
      ]);

      if (!originPort || !destinationPort) {
        throw new Error('Invalid port IDs provided');
      }

      // Create search record
      const searchRecord = await this.createSearchRecord(request, originPort, destinationPort);

      // Search across carriers
      const allResults: NormalizedSearchResult[] = [];
      const searchPromises: Promise<NormalizedSearchResult[]>[] = [];

      // Add Maersk search
      if (!request.carriers || request.carriers.includes('MAEU')) {
        searchPromises.push(
          this.maerskService.searchSchedules({
            originPortCode: originPort.code,
            destinationPortCode: destinationPort.code,
            departureDate: request.departureDate,
          })
        );
      }

      // Execute all searches in parallel
      const searchResults = await Promise.allSettled(searchPromises);
      
      // Collect results from successful searches
      searchResults.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          allResults.push(...result.value);
        } else {
          console.error(`Carrier search ${index} failed:`, result.reason);
        }
      });

      // Sort results by departure date by default
      allResults.sort((a, b) => new Date(a.departureDate).getTime() - new Date(b.departureDate).getTime());

      const searchDurationMs = Date.now() - startTime;

      // Update search record with results
      await this.updateSearchRecord(searchRecord.id, allResults.length, searchDurationMs);

      // Store search results
      await this.storeSearchResults(searchRecord.id, allResults);

      const response: SearchResponse = {
        searchId: searchRecord.id,
        results: allResults,
        totalResults: allResults.length,
        searchDurationMs,
        carriers: [...new Set(allResults.map(r => r.carrier.code))],
        metadata: {
          originPort: {
            id: originPort.id,
            code: originPort.code,
            name: originPort.name,
            country: originPort.countryName,
          },
          destinationPort: {
            id: destinationPort.id,
            code: destinationPort.code,
            name: destinationPort.name,
            country: destinationPort.countryName,
          },
          departureDate: request.departureDate,
          searchTimestamp: new Date().toISOString(),
        },
      };

      return response;
    } catch (error) {
      console.error('Search service error:', error);
      throw error;
    }
  }

  /**
   * Apply filters to search results
   */
  applyFilters(results: NormalizedSearchResult[], filters: SearchFilters): NormalizedSearchResult[] {
    let filteredResults = [...results];

    // Filter by transit days
    if (filters.minTransitDays !== undefined) {
      filteredResults = filteredResults.filter(r => r.transitDays >= filters.minTransitDays!);
    }
    if (filters.maxTransitDays !== undefined) {
      filteredResults = filteredResults.filter(r => r.transitDays <= filters.maxTransitDays!);
    }

    // Filter by carriers
    if (filters.carriers && filters.carriers.length > 0) {
      filteredResults = filteredResults.filter(r => filters.carriers!.includes(r.carrier.code));
    }

    // Sort results
    if (filters.sortBy) {
      filteredResults.sort((a, b) => {
        let aValue: any, bValue: any;

        switch (filters.sortBy) {
          case 'departureDate':
            aValue = new Date(a.departureDate).getTime();
            bValue = new Date(b.departureDate).getTime();
            break;
          case 'arrivalDate':
            aValue = new Date(a.arrivalDate).getTime();
            bValue = new Date(b.arrivalDate).getTime();
            break;
          case 'transitDays':
            aValue = a.transitDays;
            bValue = b.transitDays;
            break;
          case 'carrier':
            aValue = a.carrier.name;
            bValue = b.carrier.name;
            break;
          default:
            return 0;
        }

        if (filters.sortOrder === 'desc') {
          return bValue > aValue ? 1 : bValue < aValue ? -1 : 0;
        } else {
          return aValue > bValue ? 1 : aValue < bValue ? -1 : 0;
        }
      });
    }

    return filteredResults;
  }

  /**
   * Get search history for a user
   */
  async getSearchHistory(userId: string, limit = 10): Promise<any[]> {
    const searches = await prisma.search.findMany({
      where: { userId },
      include: {
        originPort: true,
        destinationPort: true,
        searchResults: {
          include: {
            carrier: true,
          },
        },
      },
      orderBy: { searchTimestamp: 'desc' },
      take: limit,
    });

    return searches.map(search => ({
      id: search.id,
      originPort: {
        code: search.originPort.code,
        name: search.originPort.name,
        country: search.originPort.countryName,
      },
      destinationPort: {
        code: search.destinationPort.code,
        name: search.destinationPort.name,
        country: search.destinationPort.countryName,
      },
      departureDate: search.departureDate.toISOString().split('T')[0],
      searchTimestamp: search.searchTimestamp,
      resultsCount: search.resultsCount,
      searchDurationMs: search.searchDurationMs,
      carriers: [...new Set(search.searchResults.map(r => r.carrier.code))],
    }));
  }

  /**
   * Get detailed search results by search ID
   */
  async getSearchResults(searchId: string): Promise<NormalizedSearchResult[]> {
    const searchResults = await prisma.searchResult.findMany({
      where: { searchId },
      include: {
        carrier: true,
        portRotations: {
          include: {
            port: true,
          },
          orderBy: { sequenceOrder: 'asc' },
        },
      },
    });

    return searchResults.map(result => ({
      id: result.id,
      carrier: {
        name: result.carrier.name,
        code: result.carrier.code,
      },
      vesselName: result.vesselName,
      voyageNumber: result.voyageNumber || '',
      serviceName: undefined,
      departureDate: result.departureDate.toISOString().split('T')[0],
      arrivalDate: result.arrivalDate.toISOString().split('T')[0],
      transitDays: result.transitDays,
      portRotation: result.portRotations.map(pr => ({
        portCode: pr.port.code,
        portName: pr.port.name,
        country: pr.port.countryName,
        sequenceOrder: pr.sequenceOrder,
        arrivalDate: pr.arrivalDate?.toISOString().split('T')[0],
        departureDate: pr.departureDate?.toISOString().split('T')[0],
      })),
      rawApiResponse: result.rawApiResponse,
    }));
  }

  /**
   * Helper methods
   */
  private async getPortById(portId: string) {
    return await prisma.port.findUnique({
      where: { id: portId, isActive: true },
    });
  }

  private async createSearchRecord(request: SearchRequest, originPort: any, destinationPort: any) {
    // For anonymous users, we'll skip creating a search record for now
    // In production, you might want to create anonymous search records
    if (!request.userId) {
      return {
        id: `anonymous-${Date.now()}`,
        userId: 'anonymous',
        originPortId: originPort.id,
        destinationPortId: destinationPort.id,
        departureDate: new Date(request.departureDate),
        searchTimestamp: new Date(),
        resultsCount: 0,
        searchDurationMs: 0,
      };
    }

    return await prisma.search.create({
      data: {
        userId: request.userId,
        originPortId: originPort.id,
        destinationPortId: destinationPort.id,
        departureDate: new Date(request.departureDate),
        searchTimestamp: new Date(),
        resultsCount: 0,
        searchDurationMs: 0,
      },
    });
  }

  private async updateSearchRecord(searchId: string, resultsCount: number, durationMs: number) {
    // Skip updating for anonymous searches
    if (searchId.startsWith('anonymous-')) {
      return;
    }

    await prisma.search.update({
      where: { id: searchId },
      data: {
        resultsCount,
        searchDurationMs: durationMs,
      },
    });
  }

  private async storeSearchResults(searchId: string, results: NormalizedSearchResult[]) {
    // Skip storing for anonymous searches
    if (searchId.startsWith('anonymous-')) {
      return;
    }

    for (const result of results) {
      // Find or create carrier
      const carrier = await prisma.carrier.findUnique({
        where: { code: result.carrier.code },
      });

      if (!carrier) {
        console.warn(`Carrier ${result.carrier.code} not found in database`);
        continue;
      }

      // Create search result
      const searchResult = await prisma.searchResult.create({
        data: {
          searchId,
          carrierId: carrier.id,
          vesselName: result.vesselName,
          voyageNumber: result.voyageNumber,
          departureDate: new Date(result.departureDate),
          arrivalDate: new Date(result.arrivalDate),
          transitDays: result.transitDays,
          rawApiResponse: result.rawApiResponse || {},
        },
      });

      // Create port rotations
      for (const portRotation of result.portRotation) {
        const port = await prisma.port.findUnique({
          where: { code: portRotation.portCode },
        });

        if (port) {
          await prisma.portRotation.create({
            data: {
              searchResultId: searchResult.id,
              portId: port.id,
              sequenceOrder: portRotation.sequenceOrder,
              arrivalDate: portRotation.arrivalDate ? new Date(portRotation.arrivalDate) : null,
              departureDate: portRotation.departureDate ? new Date(portRotation.departureDate) : null,
            },
          });
        }
      }
    }
  }
}

export default SearchService;
