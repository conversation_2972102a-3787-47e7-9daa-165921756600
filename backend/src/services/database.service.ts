import { prisma } from '../config/database';

export class DatabaseService {
  /**
   * Test database connection
   */
  static async testConnection(): Promise<boolean> {
    try {
      await prisma.$queryRaw`SELECT 1`;
      return true;
    } catch (error) {
      console.error('Database connection failed:', error);
      return false;
    }
  }

  /**
   * Get database health information
   */
  static async getHealthInfo() {
    try {
      const result = await prisma.$queryRaw`
        SELECT 
          current_database() as database_name,
          version() as version,
          current_timestamp as current_time
      `;
      return {
        connected: true,
        info: result,
      };
    } catch (error) {
      return {
        connected: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Get basic statistics about the database
   */
  static async getStats() {
    try {
      const [userCount, portCount, carrierCount, searchCount] = await Promise.all([
        prisma.user.count(),
        prisma.port.count(),
        prisma.carrier.count(),
        prisma.search.count(),
      ]);

      return {
        users: userCount,
        ports: portCount,
        carriers: carrierCount,
        searches: searchCount,
      };
    } catch (error) {
      console.error('Error getting database stats:', error);
      return null;
    }
  }
}

export default DatabaseService;
