import axios, { AxiosInstance } from 'axios';
import { prisma } from '../config/database';

export interface MaerskSearchParams {
  originPortCode: string;
  destinationPortCode: string;
  departureDate: string; // YYYY-MM-DD format
}

export interface MaerskSchedule {
  vesselName: string;
  voyageNumber: string;
  serviceName: string;
  departureDate: string;
  arrivalDate: string;
  transitTime: number;
  portRotation: Array<{
    portCode: string;
    portName: string;
    country: string;
    arrivalDate?: string;
    departureDate?: string;
    sequenceNumber: number;
  }>;
}

export interface NormalizedSearchResult {
  id: string;
  carrier: {
    name: string;
    code: string;
  };
  vesselName: string;
  voyageNumber: string;
  serviceName?: string;
  departureDate: string;
  arrivalDate: string;
  transitDays: number;
  portRotation: Array<{
    portCode: string;
    portName: string;
    country: string;
    sequenceOrder: number;
    arrivalDate?: string;
    departureDate?: string;
  }>;
  rawApiResponse?: any;
}

export class MaerskService {
  private apiClient: AxiosInstance;
  private readonly baseUrl = 'https://api.maersk.com';
  private readonly apiKey: string;

  constructor() {
    this.apiKey = process.env.MAERSK_API_KEY || '';
    
    this.apiClient = axios.create({
      baseURL: this.baseUrl,
      timeout: 30000,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'Consumer-Key': this.apiKey,
      },
    });

    // Add request interceptor for logging
    this.apiClient.interceptors.request.use(
      (config) => {
        console.log(`Maersk API Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('Maersk API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Add response interceptor for logging and error handling
    this.apiClient.interceptors.response.use(
      (response) => {
        console.log(`Maersk API Response: ${response.status} ${response.config.url}`);
        return response;
      },
      (error) => {
        console.error('Maersk API Response Error:', error.response?.status, error.response?.data);
        return Promise.reject(error);
      }
    );
  }

  /**
   * Search for shipping schedules using Maersk API
   */
  async searchSchedules(params: MaerskSearchParams): Promise<NormalizedSearchResult[]> {
    try {
      if (!this.apiKey) {
        console.warn('Maersk API key not configured, returning mock data');
        return this.getMockSchedules(params);
      }

      // Maersk API endpoint for schedule search
      const response = await this.apiClient.get('/products/ocean-products/schedules/v2', {
        params: {
          originGeoId: params.originPortCode,
          destinationGeoId: params.destinationPortCode,
          departureDate: params.departureDate,
          vesselOperatorCarrierCode: 'MAEU',
        },
      });

      const schedules = this.normalizeScheduleResponse(response.data);
      
      // Log the search for analytics
      await this.logSearch(params, schedules.length);
      
      return schedules;
    } catch (error: any) {
      console.error('Error searching Maersk schedules:', error);
      
      // Return mock data as fallback
      console.log('Falling back to mock data due to API error');
      return this.getMockSchedules(params);
    }
  }

  /**
   * Normalize Maersk API response to our standard format
   */
  private normalizeScheduleResponse(apiResponse: any): NormalizedSearchResult[] {
    if (!apiResponse || !apiResponse.schedules) {
      return [];
    }

    return apiResponse.schedules.map((schedule: any, index: number) => {
      const departureDate = schedule.departureDate || schedule.firstDepartureDate;
      const arrivalDate = schedule.arrivalDate || schedule.lastArrivalDate;
      
      // Calculate transit days
      const transitDays = this.calculateTransitDays(departureDate, arrivalDate);

      // Normalize port rotation
      const portRotation = this.normalizePortRotation(schedule.transportPlan || schedule.routeDetails || []);

      return {
        id: `maersk-${schedule.scheduleId || index}`,
        carrier: {
          name: 'Maersk Line',
          code: 'MAEU',
        },
        vesselName: schedule.vesselName || schedule.vessel?.name || 'Unknown Vessel',
        voyageNumber: schedule.voyageNumber || schedule.voyage || `V${index + 1}`,
        serviceName: schedule.serviceName || schedule.service?.name,
        departureDate,
        arrivalDate,
        transitDays,
        portRotation,
        rawApiResponse: schedule,
      };
    });
  }

  /**
   * Normalize port rotation data
   */
  private normalizePortRotation(transportPlan: any[]): Array<{
    portCode: string;
    portName: string;
    country: string;
    sequenceOrder: number;
    arrivalDate?: string;
    departureDate?: string;
  }> {
    if (!Array.isArray(transportPlan)) {
      return [];
    }

    return transportPlan.map((leg: any, index: number) => ({
      portCode: leg.fromLocation?.UNLocationCode || leg.portCode || `PORT${index + 1}`,
      portName: leg.fromLocation?.cityName || leg.portName || 'Unknown Port',
      country: leg.fromLocation?.countryName || leg.country || 'Unknown Country',
      sequenceOrder: index + 1,
      arrivalDate: leg.arrivalDate || leg.eta,
      departureDate: leg.departureDate || leg.etd,
    }));
  }

  /**
   * Calculate transit days between two dates
   */
  private calculateTransitDays(departureDate: string, arrivalDate: string): number {
    try {
      const departure = new Date(departureDate);
      const arrival = new Date(arrivalDate);
      const diffTime = Math.abs(arrival.getTime() - departure.getTime());
      return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    } catch (error) {
      return 0;
    }
  }

  /**
   * Log search for analytics
   */
  private async logSearch(params: MaerskSearchParams, resultsCount: number): Promise<void> {
    try {
      console.log(`Maersk search logged: ${params.originPortCode} -> ${params.destinationPortCode}, ${resultsCount} results`);
      // Additional logging can be added here
    } catch (error) {
      console.error('Error logging search:', error);
    }
  }

  /**
   * Get mock schedules for testing/fallback
   */
  private getMockSchedules(params: MaerskSearchParams): NormalizedSearchResult[] {
    const departureDate = new Date(params.departureDate);
    const arrivalDate = new Date(departureDate);
    arrivalDate.setDate(arrivalDate.getDate() + 28); // 28 days transit

    return [
      {
        id: 'maersk-mock-1',
        carrier: {
          name: 'Maersk Line',
          code: 'MAEU',
        },
        vesselName: 'Maersk Shanghai',
        voyageNumber: 'MS2401E',
        serviceName: 'AE1/Shogun',
        departureDate: departureDate.toISOString().split('T')[0],
        arrivalDate: arrivalDate.toISOString().split('T')[0],
        transitDays: 28,
        portRotation: [
          {
            portCode: params.originPortCode,
            portName: 'Origin Port',
            country: 'Origin Country',
            sequenceOrder: 1,
            departureDate: departureDate.toISOString().split('T')[0],
          },
          {
            portCode: 'SIN',
            portName: 'Singapore',
            country: 'Singapore',
            sequenceOrder: 2,
            arrivalDate: new Date(departureDate.getTime() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            departureDate: new Date(departureDate.getTime() + 8 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          },
          {
            portCode: params.destinationPortCode,
            portName: 'Destination Port',
            country: 'Destination Country',
            sequenceOrder: 3,
            arrivalDate: arrivalDate.toISOString().split('T')[0],
          },
        ],
        rawApiResponse: {
          source: 'mock',
          timestamp: new Date().toISOString(),
        },
      },
    ];
  }

  /**
   * Test API connectivity
   */
  async testConnection(): Promise<boolean> {
    try {
      if (!this.apiKey) {
        console.log('Maersk API key not configured');
        return false;
      }

      // Test with a simple API call
      await this.apiClient.get('/products/ocean-products/schedules/v2', {
        params: {
          originGeoId: 'USNYC',
          destinationGeoId: 'NLRTM',
          departureDate: new Date().toISOString().split('T')[0],
        },
      });

      return true;
    } catch (error) {
      console.error('Maersk API connection test failed:', error);
      return false;
    }
  }
}

export default MaerskService;
