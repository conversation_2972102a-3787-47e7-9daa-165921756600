import axios, { AxiosInstance } from 'axios';
import { prisma } from '../config/database';

export interface MaerskSearchParams {
  originPortCode: string;
  destinationPortCode: string;
  departureDate: string; // YYYY-MM-DD format
}

export interface MaerskSchedule {
  vesselName: string;
  voyageNumber: string;
  serviceName: string;
  departureDate: string;
  arrivalDate: string;
  transitTime: number;
  portRotation: Array<{
    portCode: string;
    portName: string;
    country: string;
    arrivalDate?: string;
    departureDate?: string;
    sequenceNumber: number;
  }>;
}

export interface NormalizedSearchResult {
  id: string;
  carrier: {
    name: string;
    code: string;
  };
  vesselName: string;
  voyageNumber: string;
  serviceName?: string;
  departureDate: string;
  arrivalDate: string;
  transitDays: number;
  portRotation: Array<{
    portCode: string;
    portName: string;
    country: string;
    sequenceOrder: number;
    arrivalDate?: string;
    departureDate?: string;
  }>;
  rawApiResponse?: any;
}

export class MaerskService {
  private apiClient: AxiosInstance;
  private readonly baseUrl = 'https://api.maersk.com/products';
  private readonly apiKey: string;

  constructor() {
    this.apiKey = process.env.MAERSK_API_KEY || '';

    this.apiClient = axios.create({
      baseURL: this.baseUrl,
      timeout: 30000,
      headers: {
        'Accept': 'application/json',
        'Consumer-Key': this.apiKey,
      },
    });

    // Add request interceptor for logging
    this.apiClient.interceptors.request.use(
      (config) => {
        console.log(`Maersk API Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('Maersk API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Add response interceptor for logging and error handling
    this.apiClient.interceptors.response.use(
      (response) => {
        console.log(`Maersk API Response: ${response.status} ${response.config.url}`);
        return response;
      },
      (error) => {
        console.error('Maersk API Response Error:', error.response?.status, error.response?.data);
        return Promise.reject(error);
      }
    );
  }

  /**
   * Search for shipping schedules using Maersk API
   */
  async searchSchedules(params: MaerskSearchParams): Promise<NormalizedSearchResult[]> {
    try {
      if (!this.apiKey) {
        console.warn('Maersk API key not configured, returning mock data');
        return this.getMockSchedules(params);
      }

      // Convert port codes to UN Location codes (5-letter format)
      const originUNCode = this.convertToUNLocationCode(params.originPortCode);
      const destinationUNCode = this.convertToUNLocationCode(params.destinationPortCode);

      console.log(`Searching Maersk API: ${originUNCode} -> ${destinationUNCode} on ${params.departureDate}`);

      // Get port details for country and city names
      const portDetails = await this.getPortDetails(params.originPortCode, params.destinationPortCode);

      // Maersk API endpoint for ocean products
      const response = await this.apiClient.get('/ocean-products', {
        params: {
          collectionOriginCountryCode: portDetails.origin.countryCode,
          collectionOriginCityName: portDetails.origin.cityName,
          collectionOriginUNLocationCode: originUNCode,
          deliveryDestinationCountryCode: portDetails.destination.countryCode,
          deliveryDestinationCityName: portDetails.destination.cityName,
          deliveryDestinationUNLocationCode: destinationUNCode,
          startDate: params.departureDate,
          startDateType: 'D', // Earliest Departure Date
          dateRange: 'P4W', // 4 weeks
          vesselOperatorCarrierCode: 'MAEU',
          cargoType: 'DRY',
          ISOEquipmentCode: '42G1', // 40ft dry container
          exportServiceMode: 'CY',
          importServiceMode: 'CY',
        },
      });

      console.log('Maersk API Response Status:', response.status);
      console.log('Maersk API Response Data:', JSON.stringify(response.data, null, 2));

      const schedules = this.normalizeScheduleResponse(response.data);

      // Log the search for analytics
      await this.logSearch(params, schedules.length);

      return schedules;
    } catch (error: any) {
      console.error('Error searching Maersk schedules:', error);

      if (error.response) {
        console.error('Maersk API Error Response:', error.response.status, error.response.data);
      }

      // Return mock data as fallback
      console.log('Falling back to mock data due to API error');
      return this.getMockSchedules(params);
    }
  }

  /**
   * Get port details including country and city information
   */
  private async getPortDetails(originPortCode: string, destinationPortCode: string) {
    // Port details mapping
    const portDetailsMap: Record<string, { countryCode: string; cityName: string; unLocationCode: string }> = {
      'SHG': { countryCode: 'CN', cityName: 'Shanghai', unLocationCode: 'CNSHA' },
      'RTM': { countryCode: 'NL', cityName: 'Rotterdam', unLocationCode: 'NLRTM' },
      'LAX': { countryCode: 'US', cityName: 'Los Angeles', unLocationCode: 'USLAX' },
      'NYC': { countryCode: 'US', cityName: 'New York', unLocationCode: 'USNYC' },
      'HKG': { countryCode: 'HK', cityName: 'Hong Kong', unLocationCode: 'HKHKG' },
      'SIN': { countryCode: 'SG', cityName: 'Singapore', unLocationCode: 'SGSIN' },
      'HAM': { countryCode: 'DE', cityName: 'Hamburg', unLocationCode: 'DEHAM' },
      'FXT': { countryCode: 'GB', cityName: 'Felixstowe', unLocationCode: 'GBFXT' },
      'LGB': { countryCode: 'US', cityName: 'Long Beach', unLocationCode: 'USLGB' },
      'ANR': { countryCode: 'BE', cityName: 'Antwerp', unLocationCode: 'BEANR' },
    };

    const origin = portDetailsMap[originPortCode] || {
      countryCode: 'US',
      cityName: originPortCode,
      unLocationCode: originPortCode
    };

    const destination = portDetailsMap[destinationPortCode] || {
      countryCode: 'NL',
      cityName: destinationPortCode,
      unLocationCode: destinationPortCode
    };

    return { origin, destination };
  }

  /**
   * Convert port code to UN Location code format
   */
  private convertToUNLocationCode(portCode: string): string {
    // Map common port codes to UN Location codes
    const portCodeMap: Record<string, string> = {
      'SHG': 'CNSHA', // Shanghai
      'RTM': 'NLRTM', // Rotterdam
      'LAX': 'USLAX', // Los Angeles
      'NYC': 'USNYC', // New York
      'HKG': 'HKHKG', // Hong Kong
      'SIN': 'SGSIN', // Singapore
      'HAM': 'DEHAM', // Hamburg
      'FXT': 'GBFXT', // Felixstowe
      'LGB': 'USLGB', // Long Beach
      'ANR': 'BEANR', // Antwerp
    };

    // If we have a mapping, use it
    if (portCodeMap[portCode]) {
      return portCodeMap[portCode];
    }

    // If it's already 5 characters, assume it's a UN Location code
    if (portCode.length === 5) {
      return portCode.toUpperCase();
    }

    // Default fallback - this might not work for all ports
    console.warn(`No UN Location code mapping found for port: ${portCode}`);
    return portCode;
  }

  /**
   * Normalize Maersk API response to our standard format
   */
  private normalizeScheduleResponse(apiResponse: any): NormalizedSearchResult[] {
    if (!apiResponse || !apiResponse.oceanProducts) {
      console.log('No ocean products found in API response');
      return [];
    }

    const results: NormalizedSearchResult[] = [];

    apiResponse.oceanProducts.forEach((product: any, productIndex: number) => {
      if (!product.transportSchedules || !Array.isArray(product.transportSchedules)) {
        return;
      }

      product.transportSchedules.forEach((schedule: any, scheduleIndex: number) => {
        const departureDate = schedule.departureDateTime?.split('T')[0] || schedule.firstDepartureDate;
        const arrivalDate = schedule.arrivalDateTime?.split('T')[0] || schedule.lastArrivalDate;

        // Calculate transit days from transit time (in minutes)
        let transitDays = 0;
        if (schedule.transitTime) {
          transitDays = Math.ceil(parseInt(schedule.transitTime) / (60 * 24)); // Convert minutes to days
        } else if (departureDate && arrivalDate) {
          transitDays = this.calculateTransitDays(departureDate, arrivalDate);
        }

        // Get vessel information
        const vessel = schedule.firstDepartureVessel || schedule.vessel;
        const vesselName = vessel?.vesselName || 'Unknown Vessel';

        // Normalize port rotation from transport legs
        const portRotation = this.normalizePortRotation(schedule.transportLegs || []);

        // Get service information from first transport leg
        const firstLeg = schedule.transportLegs?.[0];
        const serviceName = firstLeg?.transport?.carrierServiceName ||
                           firstLeg?.transport?.carrierServiceCode;
        const voyageNumber = firstLeg?.transport?.carrierDepartureVoyageNumber ||
                            `${productIndex + 1}-${scheduleIndex + 1}`;

        results.push({
          id: `maersk-${product.carrierProductId}-${schedule.departureDateTime || scheduleIndex}`,
          carrier: {
            name: 'Maersk Line',
            code: product.vesselOperatorCarrierCode || 'MAEU',
          },
          vesselName,
          voyageNumber,
          serviceName,
          departureDate,
          arrivalDate,
          transitDays,
          portRotation,
          rawApiResponse: {
            product,
            schedule,
          },
        });
      });
    });

    console.log(`Normalized ${results.length} schedules from Maersk API`);
    return results;
  }

  /**
   * Normalize port rotation data from transport legs
   */
  private normalizePortRotation(transportLegs: any[]): Array<{
    portCode: string;
    portName: string;
    country: string;
    sequenceOrder: number;
    arrivalDate?: string;
    departureDate?: string;
  }> {
    if (!Array.isArray(transportLegs)) {
      return [];
    }

    const portRotation: Array<{
      portCode: string;
      portName: string;
      country: string;
      sequenceOrder: number;
      arrivalDate?: string;
      departureDate?: string;
    }> = [];

    transportLegs.forEach((leg: any, index: number) => {
      // Add start location
      if (leg.facilities?.startLocation) {
        const startLoc = leg.facilities.startLocation;
        portRotation.push({
          portCode: startLoc.cityUNLocationCode || startLoc.UNLocationCode || startLoc.carrierCityGeoID || `PORT${index + 1}`,
          portName: startLoc.cityName || startLoc.locationName || 'Unknown Port',
          country: startLoc.countryCode || 'Unknown Country',
          sequenceOrder: portRotation.length + 1,
          departureDate: leg.departureDateTime?.split('T')[0],
        });
      }

      // Add end location (only if it's the last leg or different from next start)
      if (leg.facilities?.endLocation &&
          (index === transportLegs.length - 1 ||
           leg.facilities.endLocation.UNLocationCode !== transportLegs[index + 1]?.facilities?.startLocation?.UNLocationCode)) {
        const endLoc = leg.facilities.endLocation;
        portRotation.push({
          portCode: endLoc.cityUNLocationCode || endLoc.UNLocationCode || endLoc.carrierCityGeoID || `PORT${index + 2}`,
          portName: endLoc.cityName || endLoc.locationName || 'Unknown Port',
          country: endLoc.countryCode || 'Unknown Country',
          sequenceOrder: portRotation.length + 1,
          arrivalDate: leg.arrivalDateTime?.split('T')[0],
        });
      }
    });

    return portRotation;
  }

  /**
   * Calculate transit days between two dates
   */
  private calculateTransitDays(departureDate: string, arrivalDate: string): number {
    try {
      const departure = new Date(departureDate);
      const arrival = new Date(arrivalDate);
      const diffTime = Math.abs(arrival.getTime() - departure.getTime());
      return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    } catch (error) {
      return 0;
    }
  }

  /**
   * Log search for analytics
   */
  private async logSearch(params: MaerskSearchParams, resultsCount: number): Promise<void> {
    try {
      console.log(`Maersk search logged: ${params.originPortCode} -> ${params.destinationPortCode}, ${resultsCount} results`);
      // Additional logging can be added here
    } catch (error) {
      console.error('Error logging search:', error);
    }
  }

  /**
   * Get mock schedules for testing/fallback
   */
  private getMockSchedules(params: MaerskSearchParams): NormalizedSearchResult[] {
    const departureDate = new Date(params.departureDate);
    const arrivalDate = new Date(departureDate);
    arrivalDate.setDate(arrivalDate.getDate() + 28); // 28 days transit

    return [
      {
        id: 'maersk-mock-1',
        carrier: {
          name: 'Maersk Line',
          code: 'MAEU',
        },
        vesselName: 'Maersk Shanghai',
        voyageNumber: 'MS2401E',
        serviceName: 'AE1/Shogun',
        departureDate: departureDate.toISOString().split('T')[0],
        arrivalDate: arrivalDate.toISOString().split('T')[0],
        transitDays: 28,
        portRotation: [
          {
            portCode: params.originPortCode,
            portName: 'Origin Port',
            country: 'Origin Country',
            sequenceOrder: 1,
            departureDate: departureDate.toISOString().split('T')[0],
          },
          {
            portCode: 'SIN',
            portName: 'Singapore',
            country: 'Singapore',
            sequenceOrder: 2,
            arrivalDate: new Date(departureDate.getTime() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            departureDate: new Date(departureDate.getTime() + 8 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          },
          {
            portCode: params.destinationPortCode,
            portName: 'Destination Port',
            country: 'Destination Country',
            sequenceOrder: 3,
            arrivalDate: arrivalDate.toISOString().split('T')[0],
          },
        ],
        rawApiResponse: {
          source: 'mock',
          timestamp: new Date().toISOString(),
        },
      },
    ];
  }

  /**
   * Test API connectivity
   */
  async testConnection(): Promise<boolean> {
    try {
      if (!this.apiKey) {
        console.log('Maersk API key not configured');
        return false;
      }

      // Test with a simple API call using known UN Location codes
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);

      await this.apiClient.get('/ocean-products', {
        params: {
          collectionOriginCountryCode: 'US',
          collectionOriginCityName: 'New York',
          collectionOriginUNLocationCode: 'USNYC',
          deliveryDestinationCountryCode: 'NL',
          deliveryDestinationCityName: 'Rotterdam',
          deliveryDestinationUNLocationCode: 'NLRTM',
          startDate: tomorrow.toISOString().split('T')[0],
          vesselOperatorCarrierCode: 'MAEU',
          dateRange: 'P1W', // 1 week for testing
        },
      });

      console.log('Maersk API connection test successful');
      return true;
    } catch (error: any) {
      console.error('Maersk API connection test failed:', error.response?.status, error.response?.data || error.message);
      return false;
    }
  }
}

export default MaerskService;
