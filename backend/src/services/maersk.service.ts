import axios, { AxiosInstance } from 'axios';
import { prisma } from '../config/database';

export interface MaerskSearchParams {
  originPortCode: string;
  destinationPortCode: string;
  departureDate: string; // YYYY-MM-DD format
}

export interface MaerskSchedule {
  vesselName: string;
  voyageNumber: string;
  serviceName: string;
  departureDate: string;
  arrivalDate: string;
  transitTime: number;
  portRotation: Array<{
    portCode: string;
    portName: string;
    country: string;
    arrivalDate?: string;
    departureDate?: string;
    sequenceNumber: number;
  }>;
}

export interface NormalizedSearchResult {
  id: string;
  carrier: {
    name: string;
    code: string;
  };
  vesselName: string;
  voyageNumber: string;
  serviceName?: string;
  departureDate: string;
  arrivalDate: string;
  transitDays: number;
  portRotation: Array<{
    portCode: string;
    portName: string;
    country: string;
    sequenceOrder: number;
    arrivalDate?: string;
    departureDate?: string;
  }>;
  rawApiResponse?: any;
}

export class MaerskService {
  private apiClient: AxiosInstance;
  private readonly baseUrl = 'https://api.maersk.com/products';
  private readonly apiKey: string;

  constructor() {
    this.apiKey = process.env.MAERSK_API_KEY || '';

    this.apiClient = axios.create({
      baseURL: this.baseUrl,
      timeout: 30000,
      headers: {
        'Accept': 'application/json',
        'Consumer-Key': this.apiKey,
      },
    });

    // Add response interceptor for error handling
    this.apiClient.interceptors.response.use(
      (response) => response,
      (error) => {
        // Log errors for monitoring in production
        if (error.response) {
          console.error('MAERSK_API_ERROR', {
            status: error.response.status,
            url: error.config?.url,
            timestamp: new Date().toISOString(),
          });
        }
        return Promise.reject(error);
      }
    );
  }

  /**
   * Search for shipping schedules using Maersk API
   */
  async searchSchedules(params: MaerskSearchParams): Promise<NormalizedSearchResult[]> {
    if (!this.apiKey) {
      throw new Error('Maersk API key not configured. Please contact system administrator.');
    }

    // Validate input parameters
    this.validateSearchParams(params);

    try {
      // Convert port codes to UN Location codes
      const originUNCode = this.convertToUNLocationCode(params.originPortCode);
      const destinationUNCode = this.convertToUNLocationCode(params.destinationPortCode);

      // Get port details for country and city names
      const portDetails = await this.getPortDetails(params.originPortCode, params.destinationPortCode);

      // Validate date is not too far in the future (max 6 months)
      const maxDate = new Date();
      maxDate.setMonth(maxDate.getMonth() + 6);
      const searchDate = new Date(params.departureDate);

      if (searchDate > maxDate) {
        throw new Error('Search date cannot be more than 6 months in the future');
      }

      // Maersk API endpoint for ocean products
      const response = await this.apiClient.get('/ocean-products', {
        params: {
          collectionOriginCountryCode: portDetails.origin.countryCode,
          collectionOriginCityName: portDetails.origin.cityName,
          collectionOriginUNLocationCode: originUNCode,
          deliveryDestinationCountryCode: portDetails.destination.countryCode,
          deliveryDestinationCityName: portDetails.destination.cityName,
          deliveryDestinationUNLocationCode: destinationUNCode,
          startDate: params.departureDate,
          startDateType: 'D', // Earliest Departure Date
          dateRange: 'P4W', // 4 weeks search window
          vesselOperatorCarrierCode: 'MAEU',
          cargoType: 'DRY',
          ISOEquipmentCode: '42G1', // 40ft dry container
          exportServiceMode: 'CY', // Container Yard
          importServiceMode: 'CY', // Container Yard
        },
        timeout: 30000, // 30 second timeout
      });

      const schedules = this.normalizeScheduleResponse(response.data);

      // Log successful search for analytics
      await this.logSearch(params, schedules.length);

      return schedules;
    } catch (error: any) {
      // Enhanced error handling for production
      if (error.response) {
        const status = error.response.status;
        const errorData = error.response.data;

        switch (status) {
          case 400:
            throw new Error(`Invalid search parameters: ${errorData.debugMessage || errorData.message || 'Bad request'}`);
          case 401:
            throw new Error('API authentication failed. Please contact system administrator.');
          case 403:
            throw new Error('API access forbidden. Please contact system administrator.');
          case 404:
            throw new Error('No shipping schedules found for the selected route and date. Please try different ports or dates.');
          case 429:
            throw new Error('Too many requests. Please wait a moment and try again.');
          case 500:
          case 502:
          case 503:
          case 504:
            throw new Error('Maersk API service temporarily unavailable. Please try again later.');
          default:
            throw new Error(`Maersk API error (${status}): ${errorData.message || 'Unknown error'}`);
        }
      } else if (error.code === 'ECONNABORTED') {
        throw new Error('Search request timed out. Please try again.');
      } else if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
        throw new Error('Unable to connect to Maersk API. Please check your internet connection.');
      } else {
        throw new Error(`Search failed: ${error.message || 'Unknown error occurred'}`);
      }
    }
  }

  /**
   * Validate search parameters
   */
  private validateSearchParams(params: MaerskSearchParams): void {
    if (!params.originPortCode || params.originPortCode.trim().length === 0) {
      throw new Error('Origin port code is required');
    }

    if (!params.destinationPortCode || params.destinationPortCode.trim().length === 0) {
      throw new Error('Destination port code is required');
    }

    if (params.originPortCode === params.destinationPortCode) {
      throw new Error('Origin and destination ports cannot be the same');
    }

    if (!params.departureDate) {
      throw new Error('Departure date is required');
    }

    // Validate date format (YYYY-MM-DD)
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(params.departureDate)) {
      throw new Error('Departure date must be in YYYY-MM-DD format');
    }

    // Validate date is not in the past
    const searchDate = new Date(params.departureDate);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (searchDate < today) {
      throw new Error('Departure date cannot be in the past');
    }

    // Validate date is a valid date
    if (isNaN(searchDate.getTime())) {
      throw new Error('Invalid departure date');
    }
  }

  /**
   * Get port details including country and city information
   */
  private async getPortDetails(originPortCode: string, destinationPortCode: string) {
    // Port details mapping
    const portDetailsMap: Record<string, { countryCode: string; cityName: string; unLocationCode: string }> = {
      'SHG': { countryCode: 'CN', cityName: 'Shanghai', unLocationCode: 'CNSHA' },
      'RTM': { countryCode: 'NL', cityName: 'Rotterdam', unLocationCode: 'NLRTM' },
      'LAX': { countryCode: 'US', cityName: 'Los Angeles', unLocationCode: 'USLAX' },
      'NYC': { countryCode: 'US', cityName: 'New York', unLocationCode: 'USNYC' },
      'HKG': { countryCode: 'HK', cityName: 'Hong Kong', unLocationCode: 'HKHKG' },
      'SIN': { countryCode: 'SG', cityName: 'Singapore', unLocationCode: 'SGSIN' },
      'HAM': { countryCode: 'DE', cityName: 'Hamburg', unLocationCode: 'DEHAM' },
      'FXT': { countryCode: 'GB', cityName: 'Felixstowe', unLocationCode: 'GBFXT' },
      'LGB': { countryCode: 'US', cityName: 'Long Beach', unLocationCode: 'USLGB' },
      'ANR': { countryCode: 'BE', cityName: 'Antwerp', unLocationCode: 'BEANR' },
    };

    const origin = portDetailsMap[originPortCode] || {
      countryCode: 'US',
      cityName: originPortCode,
      unLocationCode: originPortCode
    };

    const destination = portDetailsMap[destinationPortCode] || {
      countryCode: 'NL',
      cityName: destinationPortCode,
      unLocationCode: destinationPortCode
    };

    return { origin, destination };
  }

  /**
   * Convert port code to UN Location code format
   */
  private convertToUNLocationCode(portCode: string): string {
    // Map common port codes to UN Location codes
    const portCodeMap: Record<string, string> = {
      'SHG': 'CNSHA', // Shanghai
      'RTM': 'NLRTM', // Rotterdam
      'LAX': 'USLAX', // Los Angeles
      'NYC': 'USNYC', // New York
      'HKG': 'HKHKG', // Hong Kong
      'SIN': 'SGSIN', // Singapore
      'HAM': 'DEHAM', // Hamburg
      'FXT': 'GBFXT', // Felixstowe
      'LGB': 'USLGB', // Long Beach
      'ANR': 'BEANR', // Antwerp
    };

    // If we have a mapping, use it
    if (portCodeMap[portCode]) {
      return portCodeMap[portCode];
    }

    // If it's already 5 characters, assume it's a UN Location code
    if (portCode.length === 5) {
      return portCode.toUpperCase();
    }

    // Default fallback - this might not work for all ports
    console.warn(`No UN Location code mapping found for port: ${portCode}`);
    return portCode;
  }

  /**
   * Normalize Maersk API response to our standard format
   */
  private normalizeScheduleResponse(apiResponse: any): NormalizedSearchResult[] {
    if (!apiResponse || !apiResponse.oceanProducts) {
      console.log('No ocean products found in API response');
      return [];
    }

    const results: NormalizedSearchResult[] = [];

    apiResponse.oceanProducts.forEach((product: any, productIndex: number) => {
      if (!product.transportSchedules || !Array.isArray(product.transportSchedules)) {
        return;
      }

      product.transportSchedules.forEach((schedule: any, scheduleIndex: number) => {
        const departureDate = schedule.departureDateTime?.split('T')[0] || schedule.firstDepartureDate;
        const arrivalDate = schedule.arrivalDateTime?.split('T')[0] || schedule.lastArrivalDate;

        // Calculate transit days from transit time (in minutes)
        let transitDays = 0;
        if (schedule.transitTime) {
          transitDays = Math.ceil(parseInt(schedule.transitTime) / (60 * 24)); // Convert minutes to days
        } else if (departureDate && arrivalDate) {
          transitDays = this.calculateTransitDays(departureDate, arrivalDate);
        }

        // Get vessel information
        const vessel = schedule.firstDepartureVessel || schedule.vessel;
        const vesselName = vessel?.vesselName || 'Unknown Vessel';

        // Normalize port rotation from transport legs
        const portRotation = this.normalizePortRotation(schedule.transportLegs || []);

        // Get service information from first transport leg
        const firstLeg = schedule.transportLegs?.[0];
        const serviceName = firstLeg?.transport?.carrierServiceName ||
                           firstLeg?.transport?.carrierServiceCode;
        const voyageNumber = firstLeg?.transport?.carrierDepartureVoyageNumber ||
                            `${productIndex + 1}-${scheduleIndex + 1}`;

        results.push({
          id: `maersk-${product.carrierProductId}-${schedule.departureDateTime || scheduleIndex}`,
          carrier: {
            name: 'Maersk Line',
            code: product.vesselOperatorCarrierCode || 'MAEU',
          },
          vesselName,
          voyageNumber,
          serviceName,
          departureDate,
          arrivalDate,
          transitDays,
          portRotation,
          rawApiResponse: {
            product,
            schedule,
          },
        });
      });
    });

    return results;
  }

  /**
   * Normalize port rotation data from transport legs
   */
  private normalizePortRotation(transportLegs: any[]): Array<{
    portCode: string;
    portName: string;
    country: string;
    sequenceOrder: number;
    arrivalDate?: string;
    departureDate?: string;
  }> {
    if (!Array.isArray(transportLegs)) {
      return [];
    }

    const portRotation: Array<{
      portCode: string;
      portName: string;
      country: string;
      sequenceOrder: number;
      arrivalDate?: string;
      departureDate?: string;
    }> = [];

    transportLegs.forEach((leg: any, index: number) => {
      // Add start location
      if (leg.facilities?.startLocation) {
        const startLoc = leg.facilities.startLocation;
        portRotation.push({
          portCode: startLoc.cityUNLocationCode || startLoc.UNLocationCode || startLoc.carrierCityGeoID || `PORT${index + 1}`,
          portName: startLoc.cityName || startLoc.locationName || 'Unknown Port',
          country: startLoc.countryCode || 'Unknown Country',
          sequenceOrder: portRotation.length + 1,
          departureDate: leg.departureDateTime?.split('T')[0],
        });
      }

      // Add end location (only if it's the last leg or different from next start)
      if (leg.facilities?.endLocation &&
          (index === transportLegs.length - 1 ||
           leg.facilities.endLocation.UNLocationCode !== transportLegs[index + 1]?.facilities?.startLocation?.UNLocationCode)) {
        const endLoc = leg.facilities.endLocation;
        portRotation.push({
          portCode: endLoc.cityUNLocationCode || endLoc.UNLocationCode || endLoc.carrierCityGeoID || `PORT${index + 2}`,
          portName: endLoc.cityName || endLoc.locationName || 'Unknown Port',
          country: endLoc.countryCode || 'Unknown Country',
          sequenceOrder: portRotation.length + 1,
          arrivalDate: leg.arrivalDateTime?.split('T')[0],
        });
      }
    });

    return portRotation;
  }

  /**
   * Calculate transit days between two dates
   */
  private calculateTransitDays(departureDate: string, arrivalDate: string): number {
    try {
      const departure = new Date(departureDate);
      const arrival = new Date(arrivalDate);
      const diffTime = Math.abs(arrival.getTime() - departure.getTime());
      return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    } catch (error) {
      return 0;
    }
  }

  /**
   * Log search for analytics (production logging)
   */
  private async logSearch(params: MaerskSearchParams, resultsCount: number): Promise<void> {
    try {
      // Production logging - could integrate with monitoring services
      const logData = {
        timestamp: new Date().toISOString(),
        origin: params.originPortCode,
        destination: params.destinationPortCode,
        departureDate: params.departureDate,
        resultsCount,
        carrier: 'MAEU',
      };

      // In production, this could send to logging service like CloudWatch, DataDog, etc.
      // For now, we'll use structured logging
      console.log('SEARCH_ANALYTICS', JSON.stringify(logData));
    } catch (error) {
      // Don't throw errors for logging failures
      console.error('Analytics logging failed:', error);
    }
  }

  /**
   * Test API connectivity for health checks
   */
  async testConnection(): Promise<{ connected: boolean; error?: string }> {
    if (!this.apiKey) {
      return {
        connected: false,
        error: 'API key not configured'
      };
    }

    try {
      // Minimal API call to test connectivity
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);

      const response = await this.apiClient.get('/ocean-products', {
        params: {
          collectionOriginCountryCode: 'US',
          collectionOriginCityName: 'New York',
          collectionOriginUNLocationCode: 'USNYC',
          deliveryDestinationCountryCode: 'NL',
          deliveryDestinationCityName: 'Rotterdam',
          deliveryDestinationUNLocationCode: 'NLRTM',
          startDate: tomorrow.toISOString().split('T')[0],
          vesselOperatorCarrierCode: 'MAEU',
          dateRange: 'P1D', // 1 day for minimal test
        },
        timeout: 10000, // 10 second timeout for health check
      });

      return { connected: true };
    } catch (error: any) {
      let errorMessage = 'Unknown error';

      if (error.response) {
        const status = error.response.status;
        switch (status) {
          case 401:
            errorMessage = 'Authentication failed';
            break;
          case 403:
            errorMessage = 'Access forbidden';
            break;
          case 404:
            errorMessage = 'No data found (API accessible)';
            return { connected: true }; // 404 means API is accessible
          case 429:
            errorMessage = 'Rate limit exceeded';
            break;
          case 500:
          case 502:
          case 503:
          case 504:
            errorMessage = 'API service unavailable';
            break;
          default:
            errorMessage = `API error (${status})`;
        }
      } else if (error.code === 'ECONNABORTED') {
        errorMessage = 'Connection timeout';
      } else if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
        errorMessage = 'Cannot reach API endpoint';
      }

      return {
        connected: false,
        error: errorMessage
      };
    }
  }
}

export default MaerskService;
