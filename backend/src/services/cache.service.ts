import { NormalizedSearchResult } from './maersk.service';

interface CacheEntry<T> {
  data: T;
  expiresAt: number;
  createdAt: number;
}

interface SearchCacheKey {
  originPortCode: string;
  destinationPortCode: string;
  departureDate: string;
  carriers: string[];
}

export class CacheService {
  private cache = new Map<string, CacheEntry<any>>();
  private defaultTTL: number;

  constructor(defaultTTLMinutes = 30) {
    this.defaultTTL = defaultTTLMinutes * 60 * 1000; // Convert to milliseconds
    
    // Clean up expired entries every 5 minutes
    setInterval(() => this.cleanup(), 5 * 60 * 1000);
  }

  /**
   * Generate cache key for search results
   */
  private generateSearchKey(params: SearchCacheKey): string {
    const sortedCarriers = [...params.carriers].sort();
    return `search:${params.originPortCode}:${params.destinationPortCode}:${params.departureDate}:${sortedCarriers.join(',')}`;
  }

  /**
   * Cache search results
   */
  cacheSearchResults(
    params: SearchCacheKey,
    results: NormalizedSearchResult[],
    ttlMinutes?: number
  ): void {
    const key = this.generateSearchKey(params);
    const ttl = ttlMinutes ? ttlMinutes * 60 * 1000 : this.defaultTTL;
    const now = Date.now();

    this.cache.set(key, {
      data: results,
      expiresAt: now + ttl,
      createdAt: now,
    });

    console.log('CACHE_SET', {
      key,
      resultsCount: results.length,
      ttlMinutes: ttl / (60 * 1000),
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Get cached search results
   */
  getCachedSearchResults(params: SearchCacheKey): NormalizedSearchResult[] | null {
    const key = this.generateSearchKey(params);
    const entry = this.cache.get(key);

    if (!entry) {
      return null;
    }

    if (Date.now() > entry.expiresAt) {
      this.cache.delete(key);
      console.log('CACHE_EXPIRED', {
        key,
        timestamp: new Date().toISOString(),
      });
      return null;
    }

    console.log('CACHE_HIT', {
      key,
      resultsCount: entry.data.length,
      ageMinutes: Math.round((Date.now() - entry.createdAt) / (60 * 1000)),
      timestamp: new Date().toISOString(),
    });

    return entry.data;
  }

  /**
   * Cache port search results
   */
  cachePortSearch(query: string, results: any[], ttlMinutes = 60): void {
    const key = `ports:${query.toLowerCase().trim()}`;
    const ttl = ttlMinutes * 60 * 1000;
    const now = Date.now();

    this.cache.set(key, {
      data: results,
      expiresAt: now + ttl,
      createdAt: now,
    });
  }

  /**
   * Get cached port search results
   */
  getCachedPortSearch(query: string): any[] | null {
    const key = `ports:${query.toLowerCase().trim()}`;
    const entry = this.cache.get(key);

    if (!entry || Date.now() > entry.expiresAt) {
      if (entry) {
        this.cache.delete(key);
      }
      return null;
    }

    return entry.data;
  }

  /**
   * Cache API health status
   */
  cacheHealthStatus(carrier: string, status: any, ttlMinutes = 5): void {
    const key = `health:${carrier}`;
    const ttl = ttlMinutes * 60 * 1000;
    const now = Date.now();

    this.cache.set(key, {
      data: status,
      expiresAt: now + ttl,
      createdAt: now,
    });
  }

  /**
   * Get cached health status
   */
  getCachedHealthStatus(carrier: string): any | null {
    const key = `health:${carrier}`;
    const entry = this.cache.get(key);

    if (!entry || Date.now() > entry.expiresAt) {
      if (entry) {
        this.cache.delete(key);
      }
      return null;
    }

    return entry.data;
  }

  /**
   * Invalidate cache entries by pattern
   */
  invalidatePattern(pattern: string): number {
    let deletedCount = 0;
    const regex = new RegExp(pattern);

    for (const key of this.cache.keys()) {
      if (regex.test(key)) {
        this.cache.delete(key);
        deletedCount++;
      }
    }

    console.log('CACHE_INVALIDATED', {
      pattern,
      deletedCount,
      timestamp: new Date().toISOString(),
    });

    return deletedCount;
  }

  /**
   * Clear all cache entries
   */
  clear(): void {
    const size = this.cache.size;
    this.cache.clear();
    
    console.log('CACHE_CLEARED', {
      entriesCleared: size,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Get cache statistics
   */
  getStats(): {
    totalEntries: number;
    memoryUsage: string;
    oldestEntry?: string;
    newestEntry?: string;
  } {
    const entries = Array.from(this.cache.entries());
    let oldestTime = Infinity;
    let newestTime = 0;

    entries.forEach(([, entry]) => {
      if (entry.createdAt < oldestTime) oldestTime = entry.createdAt;
      if (entry.createdAt > newestTime) newestTime = entry.createdAt;
    });

    return {
      totalEntries: this.cache.size,
      memoryUsage: `${Math.round(JSON.stringify(entries).length / 1024)} KB`,
      oldestEntry: oldestTime !== Infinity ? new Date(oldestTime).toISOString() : undefined,
      newestEntry: newestTime > 0 ? new Date(newestTime).toISOString() : undefined,
    };
  }

  /**
   * Clean up expired entries
   */
  private cleanup(): void {
    const now = Date.now();
    let deletedCount = 0;

    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.expiresAt) {
        this.cache.delete(key);
        deletedCount++;
      }
    }

    if (deletedCount > 0) {
      console.log('CACHE_CLEANUP', {
        deletedCount,
        remainingEntries: this.cache.size,
        timestamp: new Date().toISOString(),
      });
    }
  }
}

// Global cache instance
export const cacheService = new CacheService(30); // 30 minutes default TTL
