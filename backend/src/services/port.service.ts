import { prisma } from '../config/database';
import { CreatePortInput } from '../types/database';

export interface PortSearchQuery {
  search?: string;
  countryCode?: string;
  limit?: number;
  offset?: number;
}

export interface PortAutocompleteResult {
  id: string;
  code: string;
  name: string;
  countryName: string;
  displayName: string; // "SHG - Shanghai, China"
}

export class PortService {
  /**
   * Get all ports with optional filtering and pagination
   */
  static async getAllPorts(query: PortSearchQuery = {}) {
    const { search, countryCode, limit = 50, offset = 0 } = query;

    const where: any = {
      isActive: true,
    };

    // Add search filter
    if (search) {
      where.OR = [
        { code: { contains: search.toUpperCase(), mode: 'insensitive' } },
        { name: { contains: search, mode: 'insensitive' } },
        { countryName: { contains: search, mode: 'insensitive' } },
      ];
    }

    // Add country filter
    if (countryCode) {
      where.countryCode = countryCode.toUpperCase();
    }

    const [ports, total] = await Promise.all([
      prisma.port.findMany({
        where,
        orderBy: [
          { code: 'asc' },
          { name: 'asc' },
        ],
        take: limit,
        skip: offset,
        select: {
          id: true,
          code: true,
          name: true,
          countryCode: true,
          countryName: true,
          latitude: true,
          longitude: true,
          createdAt: true,
        },
      }),
      prisma.port.count({ where }),
    ]);

    return {
      ports,
      pagination: {
        total,
        limit,
        offset,
        hasMore: offset + limit < total,
      },
    };
  }

  /**
   * Get port by ID
   */
  static async getPortById(portId: string) {
    const port = await prisma.port.findUnique({
      where: { 
        id: portId,
        isActive: true,
      },
    });

    if (!port) {
      throw new Error('Port not found');
    }

    return port;
  }

  /**
   * Get port by code
   */
  static async getPortByCode(portCode: string) {
    const port = await prisma.port.findUnique({
      where: { 
        code: portCode.toUpperCase(),
        isActive: true,
      },
    });

    if (!port) {
      throw new Error('Port not found');
    }

    return port;
  }

  /**
   * Search ports for autocomplete functionality
   */
  static async searchPortsForAutocomplete(query: string, limit = 10): Promise<PortAutocompleteResult[]> {
    if (!query || query.length < 2) {
      return [];
    }

    const ports = await prisma.port.findMany({
      where: {
        isActive: true,
        OR: [
          { code: { contains: query.toUpperCase(), mode: 'insensitive' } },
          { name: { contains: query, mode: 'insensitive' } },
          { countryName: { contains: query, mode: 'insensitive' } },
        ],
      },
      orderBy: [
        // Prioritize exact code matches
        { code: 'asc' },
        { name: 'asc' },
      ],
      take: limit,
      select: {
        id: true,
        code: true,
        name: true,
        countryName: true,
      },
    });

    return ports.map(port => ({
      ...port,
      displayName: `${port.code} - ${port.name}, ${port.countryName}`,
    }));
  }

  /**
   * Get popular ports (most searched)
   */
  static async getPopularPorts(limit = 10) {
    // This would typically be based on search frequency
    // For now, we'll return major ports
    const ports = await prisma.port.findMany({
      where: {
        isActive: true,
        code: {
          in: ['SHG', 'SIN', 'RTM', 'LAX', 'NYC', 'HKG', 'HAM', 'ANR', 'SZX', 'VAN'],
        },
      },
      orderBy: { name: 'asc' },
      take: limit,
      select: {
        id: true,
        code: true,
        name: true,
        countryName: true,
      },
    });

    return ports.map(port => ({
      ...port,
      displayName: `${port.code} - ${port.name}, ${port.countryName}`,
    }));
  }

  /**
   * Get ports by country
   */
  static async getPortsByCountry(countryCode: string) {
    const ports = await prisma.port.findMany({
      where: {
        countryCode: countryCode.toUpperCase(),
        isActive: true,
      },
      orderBy: { name: 'asc' },
      select: {
        id: true,
        code: true,
        name: true,
        countryName: true,
        latitude: true,
        longitude: true,
      },
    });

    return ports;
  }

  /**
   * Validate port codes
   */
  static async validatePortCodes(portCodes: string[]): Promise<{
    valid: string[];
    invalid: string[];
  }> {
    const upperCaseCodes = portCodes.map(code => code.toUpperCase());
    
    const existingPorts = await prisma.port.findMany({
      where: {
        code: { in: upperCaseCodes },
        isActive: true,
      },
      select: { code: true },
    });

    const validCodes = existingPorts.map(port => port.code);
    const invalidCodes = upperCaseCodes.filter(code => !validCodes.includes(code));

    return {
      valid: validCodes,
      invalid: invalidCodes,
    };
  }

  /**
   * Create a new port (admin function)
   */
  static async createPort(portData: CreatePortInput) {
    // Check if port code already exists
    const existingPort = await prisma.port.findUnique({
      where: { code: portData.code.toUpperCase() },
    });

    if (existingPort) {
      throw new Error('Port with this code already exists');
    }

    const port = await prisma.port.create({
      data: {
        ...portData,
        code: portData.code.toUpperCase(),
      },
    });

    return port;
  }

  /**
   * Get port statistics
   */
  static async getPortStats() {
    const [totalPorts, portsByCountry] = await Promise.all([
      prisma.port.count({ where: { isActive: true } }),
      prisma.port.groupBy({
        by: ['countryCode', 'countryName'],
        where: { isActive: true },
        _count: { id: true },
        orderBy: { _count: { id: 'desc' } },
        take: 10,
      }),
    ]);

    return {
      totalPorts,
      topCountries: portsByCountry.map(country => ({
        countryCode: country.countryCode,
        countryName: country.countryName,
        portCount: country._count.id,
      })),
    };
  }
}

export default PortService;
