import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

interface EnvironmentConfig {
  // Server Configuration
  PORT: number;
  NODE_ENV: 'development' | 'production' | 'test';
  
  // Database Configuration
  DATABASE_URL: string;
  
  // JWT Configuration
  JWT_SECRET: string;
  JWT_EXPIRES_IN: string;
  
  // API Keys
  MAERSK_API_KEY: string;
  
  // CORS Configuration
  CORS_ORIGIN: string;
  
  // Rate Limiting
  RATE_LIMIT_WINDOW_MS: number;
  RATE_LIMIT_MAX_REQUESTS: number;
  
  // Cache Configuration
  CACHE_TTL_MINUTES: number;
  
  // Logging
  LOG_LEVEL: 'error' | 'warn' | 'info' | 'debug';
}

class EnvironmentValidator {
  private static requiredVars = [
    'DATABASE_URL',
    'JWT_SECRET',
    'MAERSK_API_KEY',
  ];

  private static validateRequired(): void {
    const missing = this.requiredVars.filter(varName => !process.env[varName]);
    
    if (missing.length > 0) {
      throw new Error(
        `Missing required environment variables: ${missing.join(', ')}\n` +
        'Please check your .env file and ensure all required variables are set.'
      );
    }
  }

  private static validateValues(): void {
    // Validate NODE_ENV
    const validEnvs = ['development', 'production', 'test'];
    if (process.env.NODE_ENV && !validEnvs.includes(process.env.NODE_ENV)) {
      throw new Error(`Invalid NODE_ENV: ${process.env.NODE_ENV}. Must be one of: ${validEnvs.join(', ')}`);
    }

    // Validate PORT
    const port = parseInt(process.env.PORT || '5001');
    if (isNaN(port) || port < 1 || port > 65535) {
      throw new Error(`Invalid PORT: ${process.env.PORT}. Must be a number between 1 and 65535.`);
    }

    // Validate JWT_SECRET length
    const jwtSecret = process.env.JWT_SECRET;
    if (jwtSecret && jwtSecret.length < 32) {
      throw new Error('JWT_SECRET must be at least 32 characters long for security.');
    }

    // Validate Maersk API key format (basic check)
    const maerskKey = process.env.MAERSK_API_KEY;
    if (maerskKey && maerskKey.length < 10) {
      throw new Error('MAERSK_API_KEY appears to be invalid (too short).');
    }

    // Validate database URL format
    const dbUrl = process.env.DATABASE_URL;
    if (dbUrl && !dbUrl.startsWith('postgresql://') && !dbUrl.startsWith('postgres://')) {
      throw new Error('DATABASE_URL must be a valid PostgreSQL connection string.');
    }
  }

  static validate(): void {
    this.validateRequired();
    this.validateValues();
  }
}

// Validate environment on module load
EnvironmentValidator.validate();

// Export validated configuration
export const config: EnvironmentConfig = {
  // Server Configuration
  PORT: parseInt(process.env.PORT || '5001'),
  NODE_ENV: (process.env.NODE_ENV as any) || 'development',
  
  // Database Configuration
  DATABASE_URL: process.env.DATABASE_URL!,
  
  // JWT Configuration
  JWT_SECRET: process.env.JWT_SECRET!,
  JWT_EXPIRES_IN: process.env.JWT_EXPIRES_IN || '24h',
  
  // API Keys
  MAERSK_API_KEY: process.env.MAERSK_API_KEY!,
  
  // CORS Configuration
  CORS_ORIGIN: process.env.CORS_ORIGIN || 'http://localhost:3000',
  
  // Rate Limiting
  RATE_LIMIT_WINDOW_MS: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15 minutes
  RATE_LIMIT_MAX_REQUESTS: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'),
  
  // Cache Configuration
  CACHE_TTL_MINUTES: parseInt(process.env.CACHE_TTL_MINUTES || '30'),
  
  // Logging
  LOG_LEVEL: (process.env.LOG_LEVEL as any) || 'info',
};

// Production-specific validations
if (config.NODE_ENV === 'production') {
  // Ensure secure configurations in production
  if (config.JWT_SECRET.length < 64) {
    console.warn('WARNING: JWT_SECRET should be at least 64 characters in production');
  }
  
  if (config.CORS_ORIGIN === 'http://localhost:3000') {
    console.warn('WARNING: CORS_ORIGIN should be set to your production domain');
  }
  
  if (config.DATABASE_URL && !config.DATABASE_URL.includes('ssl=true') && !config.DATABASE_URL.includes('sslmode=require')) {
    console.warn('WARNING: Database connection should use SSL in production');
  }
}

// Helper functions
export const isProduction = (): boolean => config.NODE_ENV === 'production';
export const isDevelopment = (): boolean => config.NODE_ENV === 'development';
export const isTest = (): boolean => config.NODE_ENV === 'test';

// Export individual configs for convenience
export const {
  PORT,
  NODE_ENV,
  DATABASE_URL,
  JWT_SECRET,
  JWT_EXPIRES_IN,
  MAERSK_API_KEY,
  CORS_ORIGIN,
  RATE_LIMIT_WINDOW_MS,
  RATE_LIMIT_MAX_REQUESTS,
  CACHE_TTL_MINUTES,
  LOG_LEVEL,
} = config;
