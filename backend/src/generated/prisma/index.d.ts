
/**
 * Client
**/

import * as runtime from './runtime/library.js';
import $Types = runtime.Types // general types
import $Public = runtime.Types.Public
import $Utils = runtime.Types.Utils
import $Extensions = runtime.Types.Extensions
import $Result = runtime.Types.Result

export type PrismaPromise<T> = $Public.PrismaPromise<T>


/**
 * Model User
 * 
 */
export type User = $Result.DefaultSelection<Prisma.$UserPayload>
/**
 * Model Port
 * 
 */
export type Port = $Result.DefaultSelection<Prisma.$PortPayload>
/**
 * Model Carrier
 * 
 */
export type Carrier = $Result.DefaultSelection<Prisma.$CarrierPayload>
/**
 * Model Search
 * 
 */
export type Search = $Result.DefaultSelection<Prisma.$SearchPayload>
/**
 * Model SearchResult
 * 
 */
export type SearchResult = $Result.DefaultSelection<Prisma.$SearchResultPayload>
/**
 * Model PortRotation
 * 
 */
export type PortRotation = $Result.DefaultSelection<Prisma.$PortRotationPayload>

/**
 * ##  Prisma Client ʲˢ
 *
 * Type-safe database client for TypeScript & Node.js
 * @example
 * ```
 * const prisma = new PrismaClient()
 * // Fetch zero or more Users
 * const users = await prisma.user.findMany()
 * ```
 *
 *
 * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
 */
export class PrismaClient<
  ClientOptions extends Prisma.PrismaClientOptions = Prisma.PrismaClientOptions,
  U = 'log' extends keyof ClientOptions ? ClientOptions['log'] extends Array<Prisma.LogLevel | Prisma.LogDefinition> ? Prisma.GetEvents<ClientOptions['log']> : never : never,
  ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs
> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['other'] }

    /**
   * ##  Prisma Client ʲˢ
   *
   * Type-safe database client for TypeScript & Node.js
   * @example
   * ```
   * const prisma = new PrismaClient()
   * // Fetch zero or more Users
   * const users = await prisma.user.findMany()
   * ```
   *
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
   */

  constructor(optionsArg ?: Prisma.Subset<ClientOptions, Prisma.PrismaClientOptions>);
  $on<V extends U>(eventType: V, callback: (event: V extends 'query' ? Prisma.QueryEvent : Prisma.LogEvent) => void): PrismaClient;

  /**
   * Connect with the database
   */
  $connect(): $Utils.JsPromise<void>;

  /**
   * Disconnect from the database
   */
  $disconnect(): $Utils.JsPromise<void>;

  /**
   * Add a middleware
   * @deprecated since 4.16.0. For new code, prefer client extensions instead.
   * @see https://pris.ly/d/extensions
   */
  $use(cb: Prisma.Middleware): void

/**
   * Executes a prepared raw query and returns the number of affected rows.
   * @example
   * ```
   * const result = await prisma.$executeRaw`UPDATE User SET cool = ${true} WHERE email = ${'<EMAIL>'};`
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $executeRaw<T = unknown>(query: TemplateStringsArray | Prisma.Sql, ...values: any[]): Prisma.PrismaPromise<number>;

  /**
   * Executes a raw query and returns the number of affected rows.
   * Susceptible to SQL injections, see documentation.
   * @example
   * ```
   * const result = await prisma.$executeRawUnsafe('UPDATE User SET cool = $1 WHERE email = $2 ;', true, '<EMAIL>')
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $executeRawUnsafe<T = unknown>(query: string, ...values: any[]): Prisma.PrismaPromise<number>;

  /**
   * Performs a prepared raw query and returns the `SELECT` data.
   * @example
   * ```
   * const result = await prisma.$queryRaw`SELECT * FROM User WHERE id = ${1} OR email = ${'<EMAIL>'};`
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $queryRaw<T = unknown>(query: TemplateStringsArray | Prisma.Sql, ...values: any[]): Prisma.PrismaPromise<T>;

  /**
   * Performs a raw query and returns the `SELECT` data.
   * Susceptible to SQL injections, see documentation.
   * @example
   * ```
   * const result = await prisma.$queryRawUnsafe('SELECT * FROM User WHERE id = $1 OR email = $2;', 1, '<EMAIL>')
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $queryRawUnsafe<T = unknown>(query: string, ...values: any[]): Prisma.PrismaPromise<T>;


  /**
   * Allows the running of a sequence of read/write operations that are guaranteed to either succeed or fail as a whole.
   * @example
   * ```
   * const [george, bob, alice] = await prisma.$transaction([
   *   prisma.user.create({ data: { name: 'George' } }),
   *   prisma.user.create({ data: { name: 'Bob' } }),
   *   prisma.user.create({ data: { name: 'Alice' } }),
   * ])
   * ```
   * 
   * Read more in our [docs](https://www.prisma.io/docs/concepts/components/prisma-client/transactions).
   */
  $transaction<P extends Prisma.PrismaPromise<any>[]>(arg: [...P], options?: { isolationLevel?: Prisma.TransactionIsolationLevel }): $Utils.JsPromise<runtime.Types.Utils.UnwrapTuple<P>>

  $transaction<R>(fn: (prisma: Omit<PrismaClient, runtime.ITXClientDenyList>) => $Utils.JsPromise<R>, options?: { maxWait?: number, timeout?: number, isolationLevel?: Prisma.TransactionIsolationLevel }): $Utils.JsPromise<R>


  $extends: $Extensions.ExtendsHook<"extends", Prisma.TypeMapCb<ClientOptions>, ExtArgs, $Utils.Call<Prisma.TypeMapCb<ClientOptions>, {
    extArgs: ExtArgs
  }>>

      /**
   * `prisma.user`: Exposes CRUD operations for the **User** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Users
    * const users = await prisma.user.findMany()
    * ```
    */
  get user(): Prisma.UserDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.port`: Exposes CRUD operations for the **Port** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Ports
    * const ports = await prisma.port.findMany()
    * ```
    */
  get port(): Prisma.PortDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.carrier`: Exposes CRUD operations for the **Carrier** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Carriers
    * const carriers = await prisma.carrier.findMany()
    * ```
    */
  get carrier(): Prisma.CarrierDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.search`: Exposes CRUD operations for the **Search** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Searches
    * const searches = await prisma.search.findMany()
    * ```
    */
  get search(): Prisma.SearchDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.searchResult`: Exposes CRUD operations for the **SearchResult** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more SearchResults
    * const searchResults = await prisma.searchResult.findMany()
    * ```
    */
  get searchResult(): Prisma.SearchResultDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.portRotation`: Exposes CRUD operations for the **PortRotation** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more PortRotations
    * const portRotations = await prisma.portRotation.findMany()
    * ```
    */
  get portRotation(): Prisma.PortRotationDelegate<ExtArgs, ClientOptions>;
}

export namespace Prisma {
  export import DMMF = runtime.DMMF

  export type PrismaPromise<T> = $Public.PrismaPromise<T>

  /**
   * Validator
   */
  export import validator = runtime.Public.validator

  /**
   * Prisma Errors
   */
  export import PrismaClientKnownRequestError = runtime.PrismaClientKnownRequestError
  export import PrismaClientUnknownRequestError = runtime.PrismaClientUnknownRequestError
  export import PrismaClientRustPanicError = runtime.PrismaClientRustPanicError
  export import PrismaClientInitializationError = runtime.PrismaClientInitializationError
  export import PrismaClientValidationError = runtime.PrismaClientValidationError

  /**
   * Re-export of sql-template-tag
   */
  export import sql = runtime.sqltag
  export import empty = runtime.empty
  export import join = runtime.join
  export import raw = runtime.raw
  export import Sql = runtime.Sql



  /**
   * Decimal.js
   */
  export import Decimal = runtime.Decimal

  export type DecimalJsLike = runtime.DecimalJsLike

  /**
   * Metrics
   */
  export type Metrics = runtime.Metrics
  export type Metric<T> = runtime.Metric<T>
  export type MetricHistogram = runtime.MetricHistogram
  export type MetricHistogramBucket = runtime.MetricHistogramBucket

  /**
  * Extensions
  */
  export import Extension = $Extensions.UserArgs
  export import getExtensionContext = runtime.Extensions.getExtensionContext
  export import Args = $Public.Args
  export import Payload = $Public.Payload
  export import Result = $Public.Result
  export import Exact = $Public.Exact

  /**
   * Prisma Client JS version: 6.10.1
   * Query Engine version: 9b628578b3b7cae625e8c927178f15a170e74a9c
   */
  export type PrismaVersion = {
    client: string
  }

  export const prismaVersion: PrismaVersion

  /**
   * Utility Types
   */


  export import JsonObject = runtime.JsonObject
  export import JsonArray = runtime.JsonArray
  export import JsonValue = runtime.JsonValue
  export import InputJsonObject = runtime.InputJsonObject
  export import InputJsonArray = runtime.InputJsonArray
  export import InputJsonValue = runtime.InputJsonValue

  /**
   * Types of the values used to represent different kinds of `null` values when working with JSON fields.
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  namespace NullTypes {
    /**
    * Type of `Prisma.DbNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.DbNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class DbNull {
      private DbNull: never
      private constructor()
    }

    /**
    * Type of `Prisma.JsonNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.JsonNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class JsonNull {
      private JsonNull: never
      private constructor()
    }

    /**
    * Type of `Prisma.AnyNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.AnyNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class AnyNull {
      private AnyNull: never
      private constructor()
    }
  }

  /**
   * Helper for filtering JSON entries that have `null` on the database (empty on the db)
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const DbNull: NullTypes.DbNull

  /**
   * Helper for filtering JSON entries that have JSON `null` values (not empty on the db)
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const JsonNull: NullTypes.JsonNull

  /**
   * Helper for filtering JSON entries that are `Prisma.DbNull` or `Prisma.JsonNull`
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const AnyNull: NullTypes.AnyNull

  type SelectAndInclude = {
    select: any
    include: any
  }

  type SelectAndOmit = {
    select: any
    omit: any
  }

  /**
   * Get the type of the value, that the Promise holds.
   */
  export type PromiseType<T extends PromiseLike<any>> = T extends PromiseLike<infer U> ? U : T;

  /**
   * Get the return type of a function which returns a Promise.
   */
  export type PromiseReturnType<T extends (...args: any) => $Utils.JsPromise<any>> = PromiseType<ReturnType<T>>

  /**
   * From T, pick a set of properties whose keys are in the union K
   */
  type Prisma__Pick<T, K extends keyof T> = {
      [P in K]: T[P];
  };


  export type Enumerable<T> = T | Array<T>;

  export type RequiredKeys<T> = {
    [K in keyof T]-?: {} extends Prisma__Pick<T, K> ? never : K
  }[keyof T]

  export type TruthyKeys<T> = keyof {
    [K in keyof T as T[K] extends false | undefined | null ? never : K]: K
  }

  export type TrueKeys<T> = TruthyKeys<Prisma__Pick<T, RequiredKeys<T>>>

  /**
   * Subset
   * @desc From `T` pick properties that exist in `U`. Simple version of Intersection
   */
  export type Subset<T, U> = {
    [key in keyof T]: key extends keyof U ? T[key] : never;
  };

  /**
   * SelectSubset
   * @desc From `T` pick properties that exist in `U`. Simple version of Intersection.
   * Additionally, it validates, if both select and include are present. If the case, it errors.
   */
  export type SelectSubset<T, U> = {
    [key in keyof T]: key extends keyof U ? T[key] : never
  } &
    (T extends SelectAndInclude
      ? 'Please either choose `select` or `include`.'
      : T extends SelectAndOmit
        ? 'Please either choose `select` or `omit`.'
        : {})

  /**
   * Subset + Intersection
   * @desc From `T` pick properties that exist in `U` and intersect `K`
   */
  export type SubsetIntersection<T, U, K> = {
    [key in keyof T]: key extends keyof U ? T[key] : never
  } &
    K

  type Without<T, U> = { [P in Exclude<keyof T, keyof U>]?: never };

  /**
   * XOR is needed to have a real mutually exclusive union type
   * https://stackoverflow.com/questions/42123407/does-typescript-support-mutually-exclusive-types
   */
  type XOR<T, U> =
    T extends object ?
    U extends object ?
      (Without<T, U> & U) | (Without<U, T> & T)
    : U : T


  /**
   * Is T a Record?
   */
  type IsObject<T extends any> = T extends Array<any>
  ? False
  : T extends Date
  ? False
  : T extends Uint8Array
  ? False
  : T extends BigInt
  ? False
  : T extends object
  ? True
  : False


  /**
   * If it's T[], return T
   */
  export type UnEnumerate<T extends unknown> = T extends Array<infer U> ? U : T

  /**
   * From ts-toolbelt
   */

  type __Either<O extends object, K extends Key> = Omit<O, K> &
    {
      // Merge all but K
      [P in K]: Prisma__Pick<O, P & keyof O> // With K possibilities
    }[K]

  type EitherStrict<O extends object, K extends Key> = Strict<__Either<O, K>>

  type EitherLoose<O extends object, K extends Key> = ComputeRaw<__Either<O, K>>

  type _Either<
    O extends object,
    K extends Key,
    strict extends Boolean
  > = {
    1: EitherStrict<O, K>
    0: EitherLoose<O, K>
  }[strict]

  type Either<
    O extends object,
    K extends Key,
    strict extends Boolean = 1
  > = O extends unknown ? _Either<O, K, strict> : never

  export type Union = any

  type PatchUndefined<O extends object, O1 extends object> = {
    [K in keyof O]: O[K] extends undefined ? At<O1, K> : O[K]
  } & {}

  /** Helper Types for "Merge" **/
  export type IntersectOf<U extends Union> = (
    U extends unknown ? (k: U) => void : never
  ) extends (k: infer I) => void
    ? I
    : never

  export type Overwrite<O extends object, O1 extends object> = {
      [K in keyof O]: K extends keyof O1 ? O1[K] : O[K];
  } & {};

  type _Merge<U extends object> = IntersectOf<Overwrite<U, {
      [K in keyof U]-?: At<U, K>;
  }>>;

  type Key = string | number | symbol;
  type AtBasic<O extends object, K extends Key> = K extends keyof O ? O[K] : never;
  type AtStrict<O extends object, K extends Key> = O[K & keyof O];
  type AtLoose<O extends object, K extends Key> = O extends unknown ? AtStrict<O, K> : never;
  export type At<O extends object, K extends Key, strict extends Boolean = 1> = {
      1: AtStrict<O, K>;
      0: AtLoose<O, K>;
  }[strict];

  export type ComputeRaw<A extends any> = A extends Function ? A : {
    [K in keyof A]: A[K];
  } & {};

  export type OptionalFlat<O> = {
    [K in keyof O]?: O[K];
  } & {};

  type _Record<K extends keyof any, T> = {
    [P in K]: T;
  };

  // cause typescript not to expand types and preserve names
  type NoExpand<T> = T extends unknown ? T : never;

  // this type assumes the passed object is entirely optional
  type AtLeast<O extends object, K extends string> = NoExpand<
    O extends unknown
    ? | (K extends keyof O ? { [P in K]: O[P] } & O : O)
      | {[P in keyof O as P extends K ? P : never]-?: O[P]} & O
    : never>;

  type _Strict<U, _U = U> = U extends unknown ? U & OptionalFlat<_Record<Exclude<Keys<_U>, keyof U>, never>> : never;

  export type Strict<U extends object> = ComputeRaw<_Strict<U>>;
  /** End Helper Types for "Merge" **/

  export type Merge<U extends object> = ComputeRaw<_Merge<Strict<U>>>;

  /**
  A [[Boolean]]
  */
  export type Boolean = True | False

  // /**
  // 1
  // */
  export type True = 1

  /**
  0
  */
  export type False = 0

  export type Not<B extends Boolean> = {
    0: 1
    1: 0
  }[B]

  export type Extends<A1 extends any, A2 extends any> = [A1] extends [never]
    ? 0 // anything `never` is false
    : A1 extends A2
    ? 1
    : 0

  export type Has<U extends Union, U1 extends Union> = Not<
    Extends<Exclude<U1, U>, U1>
  >

  export type Or<B1 extends Boolean, B2 extends Boolean> = {
    0: {
      0: 0
      1: 1
    }
    1: {
      0: 1
      1: 1
    }
  }[B1][B2]

  export type Keys<U extends Union> = U extends unknown ? keyof U : never

  type Cast<A, B> = A extends B ? A : B;

  export const type: unique symbol;



  /**
   * Used by group by
   */

  export type GetScalarType<T, O> = O extends object ? {
    [P in keyof T]: P extends keyof O
      ? O[P]
      : never
  } : never

  type FieldPaths<
    T,
    U = Omit<T, '_avg' | '_sum' | '_count' | '_min' | '_max'>
  > = IsObject<T> extends True ? U : T

  type GetHavingFields<T> = {
    [K in keyof T]: Or<
      Or<Extends<'OR', K>, Extends<'AND', K>>,
      Extends<'NOT', K>
    > extends True
      ? // infer is only needed to not hit TS limit
        // based on the brilliant idea of Pierre-Antoine Mills
        // https://github.com/microsoft/TypeScript/issues/30188#issuecomment-478938437
        T[K] extends infer TK
        ? GetHavingFields<UnEnumerate<TK> extends object ? Merge<UnEnumerate<TK>> : never>
        : never
      : {} extends FieldPaths<T[K]>
      ? never
      : K
  }[keyof T]

  /**
   * Convert tuple to union
   */
  type _TupleToUnion<T> = T extends (infer E)[] ? E : never
  type TupleToUnion<K extends readonly any[]> = _TupleToUnion<K>
  type MaybeTupleToUnion<T> = T extends any[] ? TupleToUnion<T> : T

  /**
   * Like `Pick`, but additionally can also accept an array of keys
   */
  type PickEnumerable<T, K extends Enumerable<keyof T> | keyof T> = Prisma__Pick<T, MaybeTupleToUnion<K>>

  /**
   * Exclude all keys with underscores
   */
  type ExcludeUnderscoreKeys<T extends string> = T extends `_${string}` ? never : T


  export type FieldRef<Model, FieldType> = runtime.FieldRef<Model, FieldType>

  type FieldRefInputType<Model, FieldType> = Model extends never ? never : FieldRef<Model, FieldType>


  export const ModelName: {
    User: 'User',
    Port: 'Port',
    Carrier: 'Carrier',
    Search: 'Search',
    SearchResult: 'SearchResult',
    PortRotation: 'PortRotation'
  };

  export type ModelName = (typeof ModelName)[keyof typeof ModelName]


  export type Datasources = {
    db?: Datasource
  }

  interface TypeMapCb<ClientOptions = {}> extends $Utils.Fn<{extArgs: $Extensions.InternalArgs }, $Utils.Record<string, any>> {
    returns: Prisma.TypeMap<this['params']['extArgs'], ClientOptions extends { omit: infer OmitOptions } ? OmitOptions : {}>
  }

  export type TypeMap<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> = {
    globalOmitOptions: {
      omit: GlobalOmitOptions
    }
    meta: {
      modelProps: "user" | "port" | "carrier" | "search" | "searchResult" | "portRotation"
      txIsolationLevel: Prisma.TransactionIsolationLevel
    }
    model: {
      User: {
        payload: Prisma.$UserPayload<ExtArgs>
        fields: Prisma.UserFieldRefs
        operations: {
          findUnique: {
            args: Prisma.UserFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.UserFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          findFirst: {
            args: Prisma.UserFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.UserFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          findMany: {
            args: Prisma.UserFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>[]
          }
          create: {
            args: Prisma.UserCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          createMany: {
            args: Prisma.UserCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.UserCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>[]
          }
          delete: {
            args: Prisma.UserDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          update: {
            args: Prisma.UserUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          deleteMany: {
            args: Prisma.UserDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.UserUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.UserUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>[]
          }
          upsert: {
            args: Prisma.UserUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          aggregate: {
            args: Prisma.UserAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateUser>
          }
          groupBy: {
            args: Prisma.UserGroupByArgs<ExtArgs>
            result: $Utils.Optional<UserGroupByOutputType>[]
          }
          count: {
            args: Prisma.UserCountArgs<ExtArgs>
            result: $Utils.Optional<UserCountAggregateOutputType> | number
          }
        }
      }
      Port: {
        payload: Prisma.$PortPayload<ExtArgs>
        fields: Prisma.PortFieldRefs
        operations: {
          findUnique: {
            args: Prisma.PortFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PortPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.PortFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PortPayload>
          }
          findFirst: {
            args: Prisma.PortFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PortPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.PortFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PortPayload>
          }
          findMany: {
            args: Prisma.PortFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PortPayload>[]
          }
          create: {
            args: Prisma.PortCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PortPayload>
          }
          createMany: {
            args: Prisma.PortCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.PortCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PortPayload>[]
          }
          delete: {
            args: Prisma.PortDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PortPayload>
          }
          update: {
            args: Prisma.PortUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PortPayload>
          }
          deleteMany: {
            args: Prisma.PortDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.PortUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.PortUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PortPayload>[]
          }
          upsert: {
            args: Prisma.PortUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PortPayload>
          }
          aggregate: {
            args: Prisma.PortAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregatePort>
          }
          groupBy: {
            args: Prisma.PortGroupByArgs<ExtArgs>
            result: $Utils.Optional<PortGroupByOutputType>[]
          }
          count: {
            args: Prisma.PortCountArgs<ExtArgs>
            result: $Utils.Optional<PortCountAggregateOutputType> | number
          }
        }
      }
      Carrier: {
        payload: Prisma.$CarrierPayload<ExtArgs>
        fields: Prisma.CarrierFieldRefs
        operations: {
          findUnique: {
            args: Prisma.CarrierFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CarrierPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.CarrierFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CarrierPayload>
          }
          findFirst: {
            args: Prisma.CarrierFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CarrierPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.CarrierFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CarrierPayload>
          }
          findMany: {
            args: Prisma.CarrierFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CarrierPayload>[]
          }
          create: {
            args: Prisma.CarrierCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CarrierPayload>
          }
          createMany: {
            args: Prisma.CarrierCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.CarrierCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CarrierPayload>[]
          }
          delete: {
            args: Prisma.CarrierDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CarrierPayload>
          }
          update: {
            args: Prisma.CarrierUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CarrierPayload>
          }
          deleteMany: {
            args: Prisma.CarrierDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.CarrierUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.CarrierUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CarrierPayload>[]
          }
          upsert: {
            args: Prisma.CarrierUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CarrierPayload>
          }
          aggregate: {
            args: Prisma.CarrierAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateCarrier>
          }
          groupBy: {
            args: Prisma.CarrierGroupByArgs<ExtArgs>
            result: $Utils.Optional<CarrierGroupByOutputType>[]
          }
          count: {
            args: Prisma.CarrierCountArgs<ExtArgs>
            result: $Utils.Optional<CarrierCountAggregateOutputType> | number
          }
        }
      }
      Search: {
        payload: Prisma.$SearchPayload<ExtArgs>
        fields: Prisma.SearchFieldRefs
        operations: {
          findUnique: {
            args: Prisma.SearchFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SearchPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.SearchFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SearchPayload>
          }
          findFirst: {
            args: Prisma.SearchFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SearchPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.SearchFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SearchPayload>
          }
          findMany: {
            args: Prisma.SearchFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SearchPayload>[]
          }
          create: {
            args: Prisma.SearchCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SearchPayload>
          }
          createMany: {
            args: Prisma.SearchCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.SearchCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SearchPayload>[]
          }
          delete: {
            args: Prisma.SearchDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SearchPayload>
          }
          update: {
            args: Prisma.SearchUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SearchPayload>
          }
          deleteMany: {
            args: Prisma.SearchDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.SearchUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.SearchUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SearchPayload>[]
          }
          upsert: {
            args: Prisma.SearchUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SearchPayload>
          }
          aggregate: {
            args: Prisma.SearchAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateSearch>
          }
          groupBy: {
            args: Prisma.SearchGroupByArgs<ExtArgs>
            result: $Utils.Optional<SearchGroupByOutputType>[]
          }
          count: {
            args: Prisma.SearchCountArgs<ExtArgs>
            result: $Utils.Optional<SearchCountAggregateOutputType> | number
          }
        }
      }
      SearchResult: {
        payload: Prisma.$SearchResultPayload<ExtArgs>
        fields: Prisma.SearchResultFieldRefs
        operations: {
          findUnique: {
            args: Prisma.SearchResultFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SearchResultPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.SearchResultFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SearchResultPayload>
          }
          findFirst: {
            args: Prisma.SearchResultFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SearchResultPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.SearchResultFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SearchResultPayload>
          }
          findMany: {
            args: Prisma.SearchResultFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SearchResultPayload>[]
          }
          create: {
            args: Prisma.SearchResultCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SearchResultPayload>
          }
          createMany: {
            args: Prisma.SearchResultCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.SearchResultCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SearchResultPayload>[]
          }
          delete: {
            args: Prisma.SearchResultDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SearchResultPayload>
          }
          update: {
            args: Prisma.SearchResultUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SearchResultPayload>
          }
          deleteMany: {
            args: Prisma.SearchResultDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.SearchResultUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.SearchResultUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SearchResultPayload>[]
          }
          upsert: {
            args: Prisma.SearchResultUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$SearchResultPayload>
          }
          aggregate: {
            args: Prisma.SearchResultAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateSearchResult>
          }
          groupBy: {
            args: Prisma.SearchResultGroupByArgs<ExtArgs>
            result: $Utils.Optional<SearchResultGroupByOutputType>[]
          }
          count: {
            args: Prisma.SearchResultCountArgs<ExtArgs>
            result: $Utils.Optional<SearchResultCountAggregateOutputType> | number
          }
        }
      }
      PortRotation: {
        payload: Prisma.$PortRotationPayload<ExtArgs>
        fields: Prisma.PortRotationFieldRefs
        operations: {
          findUnique: {
            args: Prisma.PortRotationFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PortRotationPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.PortRotationFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PortRotationPayload>
          }
          findFirst: {
            args: Prisma.PortRotationFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PortRotationPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.PortRotationFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PortRotationPayload>
          }
          findMany: {
            args: Prisma.PortRotationFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PortRotationPayload>[]
          }
          create: {
            args: Prisma.PortRotationCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PortRotationPayload>
          }
          createMany: {
            args: Prisma.PortRotationCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.PortRotationCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PortRotationPayload>[]
          }
          delete: {
            args: Prisma.PortRotationDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PortRotationPayload>
          }
          update: {
            args: Prisma.PortRotationUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PortRotationPayload>
          }
          deleteMany: {
            args: Prisma.PortRotationDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.PortRotationUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.PortRotationUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PortRotationPayload>[]
          }
          upsert: {
            args: Prisma.PortRotationUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PortRotationPayload>
          }
          aggregate: {
            args: Prisma.PortRotationAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregatePortRotation>
          }
          groupBy: {
            args: Prisma.PortRotationGroupByArgs<ExtArgs>
            result: $Utils.Optional<PortRotationGroupByOutputType>[]
          }
          count: {
            args: Prisma.PortRotationCountArgs<ExtArgs>
            result: $Utils.Optional<PortRotationCountAggregateOutputType> | number
          }
        }
      }
    }
  } & {
    other: {
      payload: any
      operations: {
        $executeRaw: {
          args: [query: TemplateStringsArray | Prisma.Sql, ...values: any[]],
          result: any
        }
        $executeRawUnsafe: {
          args: [query: string, ...values: any[]],
          result: any
        }
        $queryRaw: {
          args: [query: TemplateStringsArray | Prisma.Sql, ...values: any[]],
          result: any
        }
        $queryRawUnsafe: {
          args: [query: string, ...values: any[]],
          result: any
        }
      }
    }
  }
  export const defineExtension: $Extensions.ExtendsHook<"define", Prisma.TypeMapCb, $Extensions.DefaultArgs>
  export type DefaultPrismaClient = PrismaClient
  export type ErrorFormat = 'pretty' | 'colorless' | 'minimal'
  export interface PrismaClientOptions {
    /**
     * Overwrites the datasource url from your schema.prisma file
     */
    datasources?: Datasources
    /**
     * Overwrites the datasource url from your schema.prisma file
     */
    datasourceUrl?: string
    /**
     * @default "colorless"
     */
    errorFormat?: ErrorFormat
    /**
     * @example
     * ```
     * // Defaults to stdout
     * log: ['query', 'info', 'warn', 'error']
     * 
     * // Emit as events
     * log: [
     *   { emit: 'stdout', level: 'query' },
     *   { emit: 'stdout', level: 'info' },
     *   { emit: 'stdout', level: 'warn' }
     *   { emit: 'stdout', level: 'error' }
     * ]
     * ```
     * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/logging#the-log-option).
     */
    log?: (LogLevel | LogDefinition)[]
    /**
     * The default values for transactionOptions
     * maxWait ?= 2000
     * timeout ?= 5000
     */
    transactionOptions?: {
      maxWait?: number
      timeout?: number
      isolationLevel?: Prisma.TransactionIsolationLevel
    }
    /**
     * Global configuration for omitting model fields by default.
     * 
     * @example
     * ```
     * const prisma = new PrismaClient({
     *   omit: {
     *     user: {
     *       password: true
     *     }
     *   }
     * })
     * ```
     */
    omit?: Prisma.GlobalOmitConfig
  }
  export type GlobalOmitConfig = {
    user?: UserOmit
    port?: PortOmit
    carrier?: CarrierOmit
    search?: SearchOmit
    searchResult?: SearchResultOmit
    portRotation?: PortRotationOmit
  }

  /* Types for Logging */
  export type LogLevel = 'info' | 'query' | 'warn' | 'error'
  export type LogDefinition = {
    level: LogLevel
    emit: 'stdout' | 'event'
  }

  export type GetLogType<T extends LogLevel | LogDefinition> = T extends LogDefinition ? T['emit'] extends 'event' ? T['level'] : never : never
  export type GetEvents<T extends any> = T extends Array<LogLevel | LogDefinition> ?
    GetLogType<T[0]> | GetLogType<T[1]> | GetLogType<T[2]> | GetLogType<T[3]>
    : never

  export type QueryEvent = {
    timestamp: Date
    query: string
    params: string
    duration: number
    target: string
  }

  export type LogEvent = {
    timestamp: Date
    message: string
    target: string
  }
  /* End Types for Logging */


  export type PrismaAction =
    | 'findUnique'
    | 'findUniqueOrThrow'
    | 'findMany'
    | 'findFirst'
    | 'findFirstOrThrow'
    | 'create'
    | 'createMany'
    | 'createManyAndReturn'
    | 'update'
    | 'updateMany'
    | 'updateManyAndReturn'
    | 'upsert'
    | 'delete'
    | 'deleteMany'
    | 'executeRaw'
    | 'queryRaw'
    | 'aggregate'
    | 'count'
    | 'runCommandRaw'
    | 'findRaw'
    | 'groupBy'

  /**
   * These options are being passed into the middleware as "params"
   */
  export type MiddlewareParams = {
    model?: ModelName
    action: PrismaAction
    args: any
    dataPath: string[]
    runInTransaction: boolean
  }

  /**
   * The `T` type makes sure, that the `return proceed` is not forgotten in the middleware implementation
   */
  export type Middleware<T = any> = (
    params: MiddlewareParams,
    next: (params: MiddlewareParams) => $Utils.JsPromise<T>,
  ) => $Utils.JsPromise<T>

  // tested in getLogLevel.test.ts
  export function getLogLevel(log: Array<LogLevel | LogDefinition>): LogLevel | undefined;

  /**
   * `PrismaClient` proxy available in interactive transactions.
   */
  export type TransactionClient = Omit<Prisma.DefaultPrismaClient, runtime.ITXClientDenyList>

  export type Datasource = {
    url?: string
  }

  /**
   * Count Types
   */


  /**
   * Count Type UserCountOutputType
   */

  export type UserCountOutputType = {
    searches: number
  }

  export type UserCountOutputTypeSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    searches?: boolean | UserCountOutputTypeCountSearchesArgs
  }

  // Custom InputTypes
  /**
   * UserCountOutputType without action
   */
  export type UserCountOutputTypeDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserCountOutputType
     */
    select?: UserCountOutputTypeSelect<ExtArgs> | null
  }

  /**
   * UserCountOutputType without action
   */
  export type UserCountOutputTypeCountSearchesArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: SearchWhereInput
  }


  /**
   * Count Type PortCountOutputType
   */

  export type PortCountOutputType = {
    originSearches: number
    destinationSearches: number
    portRotations: number
  }

  export type PortCountOutputTypeSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    originSearches?: boolean | PortCountOutputTypeCountOriginSearchesArgs
    destinationSearches?: boolean | PortCountOutputTypeCountDestinationSearchesArgs
    portRotations?: boolean | PortCountOutputTypeCountPortRotationsArgs
  }

  // Custom InputTypes
  /**
   * PortCountOutputType without action
   */
  export type PortCountOutputTypeDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the PortCountOutputType
     */
    select?: PortCountOutputTypeSelect<ExtArgs> | null
  }

  /**
   * PortCountOutputType without action
   */
  export type PortCountOutputTypeCountOriginSearchesArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: SearchWhereInput
  }

  /**
   * PortCountOutputType without action
   */
  export type PortCountOutputTypeCountDestinationSearchesArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: SearchWhereInput
  }

  /**
   * PortCountOutputType without action
   */
  export type PortCountOutputTypeCountPortRotationsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: PortRotationWhereInput
  }


  /**
   * Count Type CarrierCountOutputType
   */

  export type CarrierCountOutputType = {
    searchResults: number
  }

  export type CarrierCountOutputTypeSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    searchResults?: boolean | CarrierCountOutputTypeCountSearchResultsArgs
  }

  // Custom InputTypes
  /**
   * CarrierCountOutputType without action
   */
  export type CarrierCountOutputTypeDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CarrierCountOutputType
     */
    select?: CarrierCountOutputTypeSelect<ExtArgs> | null
  }

  /**
   * CarrierCountOutputType without action
   */
  export type CarrierCountOutputTypeCountSearchResultsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: SearchResultWhereInput
  }


  /**
   * Count Type SearchCountOutputType
   */

  export type SearchCountOutputType = {
    searchResults: number
  }

  export type SearchCountOutputTypeSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    searchResults?: boolean | SearchCountOutputTypeCountSearchResultsArgs
  }

  // Custom InputTypes
  /**
   * SearchCountOutputType without action
   */
  export type SearchCountOutputTypeDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the SearchCountOutputType
     */
    select?: SearchCountOutputTypeSelect<ExtArgs> | null
  }

  /**
   * SearchCountOutputType without action
   */
  export type SearchCountOutputTypeCountSearchResultsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: SearchResultWhereInput
  }


  /**
   * Count Type SearchResultCountOutputType
   */

  export type SearchResultCountOutputType = {
    portRotations: number
  }

  export type SearchResultCountOutputTypeSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    portRotations?: boolean | SearchResultCountOutputTypeCountPortRotationsArgs
  }

  // Custom InputTypes
  /**
   * SearchResultCountOutputType without action
   */
  export type SearchResultCountOutputTypeDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the SearchResultCountOutputType
     */
    select?: SearchResultCountOutputTypeSelect<ExtArgs> | null
  }

  /**
   * SearchResultCountOutputType without action
   */
  export type SearchResultCountOutputTypeCountPortRotationsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: PortRotationWhereInput
  }


  /**
   * Models
   */

  /**
   * Model User
   */

  export type AggregateUser = {
    _count: UserCountAggregateOutputType | null
    _min: UserMinAggregateOutputType | null
    _max: UserMaxAggregateOutputType | null
  }

  export type UserMinAggregateOutputType = {
    id: string | null
    email: string | null
    passwordHash: string | null
    firstName: string | null
    lastName: string | null
    companyName: string | null
    isActive: boolean | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type UserMaxAggregateOutputType = {
    id: string | null
    email: string | null
    passwordHash: string | null
    firstName: string | null
    lastName: string | null
    companyName: string | null
    isActive: boolean | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type UserCountAggregateOutputType = {
    id: number
    email: number
    passwordHash: number
    firstName: number
    lastName: number
    companyName: number
    isActive: number
    createdAt: number
    updatedAt: number
    _all: number
  }


  export type UserMinAggregateInputType = {
    id?: true
    email?: true
    passwordHash?: true
    firstName?: true
    lastName?: true
    companyName?: true
    isActive?: true
    createdAt?: true
    updatedAt?: true
  }

  export type UserMaxAggregateInputType = {
    id?: true
    email?: true
    passwordHash?: true
    firstName?: true
    lastName?: true
    companyName?: true
    isActive?: true
    createdAt?: true
    updatedAt?: true
  }

  export type UserCountAggregateInputType = {
    id?: true
    email?: true
    passwordHash?: true
    firstName?: true
    lastName?: true
    companyName?: true
    isActive?: true
    createdAt?: true
    updatedAt?: true
    _all?: true
  }

  export type UserAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which User to aggregate.
     */
    where?: UserWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Users to fetch.
     */
    orderBy?: UserOrderByWithRelationInput | UserOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: UserWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Users.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned Users
    **/
    _count?: true | UserCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: UserMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: UserMaxAggregateInputType
  }

  export type GetUserAggregateType<T extends UserAggregateArgs> = {
        [P in keyof T & keyof AggregateUser]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateUser[P]>
      : GetScalarType<T[P], AggregateUser[P]>
  }




  export type UserGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: UserWhereInput
    orderBy?: UserOrderByWithAggregationInput | UserOrderByWithAggregationInput[]
    by: UserScalarFieldEnum[] | UserScalarFieldEnum
    having?: UserScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: UserCountAggregateInputType | true
    _min?: UserMinAggregateInputType
    _max?: UserMaxAggregateInputType
  }

  export type UserGroupByOutputType = {
    id: string
    email: string
    passwordHash: string
    firstName: string
    lastName: string
    companyName: string | null
    isActive: boolean
    createdAt: Date
    updatedAt: Date
    _count: UserCountAggregateOutputType | null
    _min: UserMinAggregateOutputType | null
    _max: UserMaxAggregateOutputType | null
  }

  type GetUserGroupByPayload<T extends UserGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<UserGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof UserGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], UserGroupByOutputType[P]>
            : GetScalarType<T[P], UserGroupByOutputType[P]>
        }
      >
    >


  export type UserSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    email?: boolean
    passwordHash?: boolean
    firstName?: boolean
    lastName?: boolean
    companyName?: boolean
    isActive?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    searches?: boolean | User$searchesArgs<ExtArgs>
    _count?: boolean | UserCountOutputTypeDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["user"]>

  export type UserSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    email?: boolean
    passwordHash?: boolean
    firstName?: boolean
    lastName?: boolean
    companyName?: boolean
    isActive?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }, ExtArgs["result"]["user"]>

  export type UserSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    email?: boolean
    passwordHash?: boolean
    firstName?: boolean
    lastName?: boolean
    companyName?: boolean
    isActive?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }, ExtArgs["result"]["user"]>

  export type UserSelectScalar = {
    id?: boolean
    email?: boolean
    passwordHash?: boolean
    firstName?: boolean
    lastName?: boolean
    companyName?: boolean
    isActive?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }

  export type UserOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "email" | "passwordHash" | "firstName" | "lastName" | "companyName" | "isActive" | "createdAt" | "updatedAt", ExtArgs["result"]["user"]>
  export type UserInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    searches?: boolean | User$searchesArgs<ExtArgs>
    _count?: boolean | UserCountOutputTypeDefaultArgs<ExtArgs>
  }
  export type UserIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {}
  export type UserIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {}

  export type $UserPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "User"
    objects: {
      searches: Prisma.$SearchPayload<ExtArgs>[]
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      email: string
      passwordHash: string
      firstName: string
      lastName: string
      companyName: string | null
      isActive: boolean
      createdAt: Date
      updatedAt: Date
    }, ExtArgs["result"]["user"]>
    composites: {}
  }

  type UserGetPayload<S extends boolean | null | undefined | UserDefaultArgs> = $Result.GetResult<Prisma.$UserPayload, S>

  type UserCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<UserFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: UserCountAggregateInputType | true
    }

  export interface UserDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['User'], meta: { name: 'User' } }
    /**
     * Find zero or one User that matches the filter.
     * @param {UserFindUniqueArgs} args - Arguments to find a User
     * @example
     * // Get one User
     * const user = await prisma.user.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends UserFindUniqueArgs>(args: SelectSubset<T, UserFindUniqueArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one User that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {UserFindUniqueOrThrowArgs} args - Arguments to find a User
     * @example
     * // Get one User
     * const user = await prisma.user.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends UserFindUniqueOrThrowArgs>(args: SelectSubset<T, UserFindUniqueOrThrowArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first User that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserFindFirstArgs} args - Arguments to find a User
     * @example
     * // Get one User
     * const user = await prisma.user.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends UserFindFirstArgs>(args?: SelectSubset<T, UserFindFirstArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first User that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserFindFirstOrThrowArgs} args - Arguments to find a User
     * @example
     * // Get one User
     * const user = await prisma.user.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends UserFindFirstOrThrowArgs>(args?: SelectSubset<T, UserFindFirstOrThrowArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more Users that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all Users
     * const users = await prisma.user.findMany()
     * 
     * // Get first 10 Users
     * const users = await prisma.user.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const userWithIdOnly = await prisma.user.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends UserFindManyArgs>(args?: SelectSubset<T, UserFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a User.
     * @param {UserCreateArgs} args - Arguments to create a User.
     * @example
     * // Create one User
     * const User = await prisma.user.create({
     *   data: {
     *     // ... data to create a User
     *   }
     * })
     * 
     */
    create<T extends UserCreateArgs>(args: SelectSubset<T, UserCreateArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many Users.
     * @param {UserCreateManyArgs} args - Arguments to create many Users.
     * @example
     * // Create many Users
     * const user = await prisma.user.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends UserCreateManyArgs>(args?: SelectSubset<T, UserCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many Users and returns the data saved in the database.
     * @param {UserCreateManyAndReturnArgs} args - Arguments to create many Users.
     * @example
     * // Create many Users
     * const user = await prisma.user.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many Users and only return the `id`
     * const userWithIdOnly = await prisma.user.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends UserCreateManyAndReturnArgs>(args?: SelectSubset<T, UserCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a User.
     * @param {UserDeleteArgs} args - Arguments to delete one User.
     * @example
     * // Delete one User
     * const User = await prisma.user.delete({
     *   where: {
     *     // ... filter to delete one User
     *   }
     * })
     * 
     */
    delete<T extends UserDeleteArgs>(args: SelectSubset<T, UserDeleteArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one User.
     * @param {UserUpdateArgs} args - Arguments to update one User.
     * @example
     * // Update one User
     * const user = await prisma.user.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends UserUpdateArgs>(args: SelectSubset<T, UserUpdateArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more Users.
     * @param {UserDeleteManyArgs} args - Arguments to filter Users to delete.
     * @example
     * // Delete a few Users
     * const { count } = await prisma.user.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends UserDeleteManyArgs>(args?: SelectSubset<T, UserDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Users.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many Users
     * const user = await prisma.user.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends UserUpdateManyArgs>(args: SelectSubset<T, UserUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Users and returns the data updated in the database.
     * @param {UserUpdateManyAndReturnArgs} args - Arguments to update many Users.
     * @example
     * // Update many Users
     * const user = await prisma.user.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more Users and only return the `id`
     * const userWithIdOnly = await prisma.user.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends UserUpdateManyAndReturnArgs>(args: SelectSubset<T, UserUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one User.
     * @param {UserUpsertArgs} args - Arguments to update or create a User.
     * @example
     * // Update or create a User
     * const user = await prisma.user.upsert({
     *   create: {
     *     // ... data to create a User
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the User we want to update
     *   }
     * })
     */
    upsert<T extends UserUpsertArgs>(args: SelectSubset<T, UserUpsertArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of Users.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserCountArgs} args - Arguments to filter Users to count.
     * @example
     * // Count the number of Users
     * const count = await prisma.user.count({
     *   where: {
     *     // ... the filter for the Users we want to count
     *   }
     * })
    **/
    count<T extends UserCountArgs>(
      args?: Subset<T, UserCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], UserCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a User.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends UserAggregateArgs>(args: Subset<T, UserAggregateArgs>): Prisma.PrismaPromise<GetUserAggregateType<T>>

    /**
     * Group by User.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends UserGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: UserGroupByArgs['orderBy'] }
        : { orderBy?: UserGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, UserGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetUserGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the User model
   */
  readonly fields: UserFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for User.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__UserClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    searches<T extends User$searchesArgs<ExtArgs> = {}>(args?: Subset<T, User$searchesArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$SearchPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the User model
   */
  interface UserFieldRefs {
    readonly id: FieldRef<"User", 'String'>
    readonly email: FieldRef<"User", 'String'>
    readonly passwordHash: FieldRef<"User", 'String'>
    readonly firstName: FieldRef<"User", 'String'>
    readonly lastName: FieldRef<"User", 'String'>
    readonly companyName: FieldRef<"User", 'String'>
    readonly isActive: FieldRef<"User", 'Boolean'>
    readonly createdAt: FieldRef<"User", 'DateTime'>
    readonly updatedAt: FieldRef<"User", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * User findUnique
   */
  export type UserFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which User to fetch.
     */
    where: UserWhereUniqueInput
  }

  /**
   * User findUniqueOrThrow
   */
  export type UserFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which User to fetch.
     */
    where: UserWhereUniqueInput
  }

  /**
   * User findFirst
   */
  export type UserFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which User to fetch.
     */
    where?: UserWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Users to fetch.
     */
    orderBy?: UserOrderByWithRelationInput | UserOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Users.
     */
    cursor?: UserWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Users.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Users.
     */
    distinct?: UserScalarFieldEnum | UserScalarFieldEnum[]
  }

  /**
   * User findFirstOrThrow
   */
  export type UserFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which User to fetch.
     */
    where?: UserWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Users to fetch.
     */
    orderBy?: UserOrderByWithRelationInput | UserOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Users.
     */
    cursor?: UserWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Users.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Users.
     */
    distinct?: UserScalarFieldEnum | UserScalarFieldEnum[]
  }

  /**
   * User findMany
   */
  export type UserFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which Users to fetch.
     */
    where?: UserWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Users to fetch.
     */
    orderBy?: UserOrderByWithRelationInput | UserOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing Users.
     */
    cursor?: UserWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Users.
     */
    skip?: number
    distinct?: UserScalarFieldEnum | UserScalarFieldEnum[]
  }

  /**
   * User create
   */
  export type UserCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * The data needed to create a User.
     */
    data: XOR<UserCreateInput, UserUncheckedCreateInput>
  }

  /**
   * User createMany
   */
  export type UserCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many Users.
     */
    data: UserCreateManyInput | UserCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * User createManyAndReturn
   */
  export type UserCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * The data used to create many Users.
     */
    data: UserCreateManyInput | UserCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * User update
   */
  export type UserUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * The data needed to update a User.
     */
    data: XOR<UserUpdateInput, UserUncheckedUpdateInput>
    /**
     * Choose, which User to update.
     */
    where: UserWhereUniqueInput
  }

  /**
   * User updateMany
   */
  export type UserUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update Users.
     */
    data: XOR<UserUpdateManyMutationInput, UserUncheckedUpdateManyInput>
    /**
     * Filter which Users to update
     */
    where?: UserWhereInput
    /**
     * Limit how many Users to update.
     */
    limit?: number
  }

  /**
   * User updateManyAndReturn
   */
  export type UserUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * The data used to update Users.
     */
    data: XOR<UserUpdateManyMutationInput, UserUncheckedUpdateManyInput>
    /**
     * Filter which Users to update
     */
    where?: UserWhereInput
    /**
     * Limit how many Users to update.
     */
    limit?: number
  }

  /**
   * User upsert
   */
  export type UserUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * The filter to search for the User to update in case it exists.
     */
    where: UserWhereUniqueInput
    /**
     * In case the User found by the `where` argument doesn't exist, create a new User with this data.
     */
    create: XOR<UserCreateInput, UserUncheckedCreateInput>
    /**
     * In case the User was found with the provided `where` argument, update it with this data.
     */
    update: XOR<UserUpdateInput, UserUncheckedUpdateInput>
  }

  /**
   * User delete
   */
  export type UserDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter which User to delete.
     */
    where: UserWhereUniqueInput
  }

  /**
   * User deleteMany
   */
  export type UserDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Users to delete
     */
    where?: UserWhereInput
    /**
     * Limit how many Users to delete.
     */
    limit?: number
  }

  /**
   * User.searches
   */
  export type User$searchesArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Search
     */
    select?: SearchSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Search
     */
    omit?: SearchOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SearchInclude<ExtArgs> | null
    where?: SearchWhereInput
    orderBy?: SearchOrderByWithRelationInput | SearchOrderByWithRelationInput[]
    cursor?: SearchWhereUniqueInput
    take?: number
    skip?: number
    distinct?: SearchScalarFieldEnum | SearchScalarFieldEnum[]
  }

  /**
   * User without action
   */
  export type UserDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
  }


  /**
   * Model Port
   */

  export type AggregatePort = {
    _count: PortCountAggregateOutputType | null
    _avg: PortAvgAggregateOutputType | null
    _sum: PortSumAggregateOutputType | null
    _min: PortMinAggregateOutputType | null
    _max: PortMaxAggregateOutputType | null
  }

  export type PortAvgAggregateOutputType = {
    latitude: number | null
    longitude: number | null
  }

  export type PortSumAggregateOutputType = {
    latitude: number | null
    longitude: number | null
  }

  export type PortMinAggregateOutputType = {
    id: string | null
    code: string | null
    name: string | null
    countryCode: string | null
    countryName: string | null
    latitude: number | null
    longitude: number | null
    isActive: boolean | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type PortMaxAggregateOutputType = {
    id: string | null
    code: string | null
    name: string | null
    countryCode: string | null
    countryName: string | null
    latitude: number | null
    longitude: number | null
    isActive: boolean | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type PortCountAggregateOutputType = {
    id: number
    code: number
    name: number
    countryCode: number
    countryName: number
    latitude: number
    longitude: number
    isActive: number
    createdAt: number
    updatedAt: number
    _all: number
  }


  export type PortAvgAggregateInputType = {
    latitude?: true
    longitude?: true
  }

  export type PortSumAggregateInputType = {
    latitude?: true
    longitude?: true
  }

  export type PortMinAggregateInputType = {
    id?: true
    code?: true
    name?: true
    countryCode?: true
    countryName?: true
    latitude?: true
    longitude?: true
    isActive?: true
    createdAt?: true
    updatedAt?: true
  }

  export type PortMaxAggregateInputType = {
    id?: true
    code?: true
    name?: true
    countryCode?: true
    countryName?: true
    latitude?: true
    longitude?: true
    isActive?: true
    createdAt?: true
    updatedAt?: true
  }

  export type PortCountAggregateInputType = {
    id?: true
    code?: true
    name?: true
    countryCode?: true
    countryName?: true
    latitude?: true
    longitude?: true
    isActive?: true
    createdAt?: true
    updatedAt?: true
    _all?: true
  }

  export type PortAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Port to aggregate.
     */
    where?: PortWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Ports to fetch.
     */
    orderBy?: PortOrderByWithRelationInput | PortOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: PortWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Ports from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Ports.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned Ports
    **/
    _count?: true | PortCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: PortAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: PortSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: PortMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: PortMaxAggregateInputType
  }

  export type GetPortAggregateType<T extends PortAggregateArgs> = {
        [P in keyof T & keyof AggregatePort]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregatePort[P]>
      : GetScalarType<T[P], AggregatePort[P]>
  }




  export type PortGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: PortWhereInput
    orderBy?: PortOrderByWithAggregationInput | PortOrderByWithAggregationInput[]
    by: PortScalarFieldEnum[] | PortScalarFieldEnum
    having?: PortScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: PortCountAggregateInputType | true
    _avg?: PortAvgAggregateInputType
    _sum?: PortSumAggregateInputType
    _min?: PortMinAggregateInputType
    _max?: PortMaxAggregateInputType
  }

  export type PortGroupByOutputType = {
    id: string
    code: string
    name: string
    countryCode: string
    countryName: string
    latitude: number | null
    longitude: number | null
    isActive: boolean
    createdAt: Date
    updatedAt: Date
    _count: PortCountAggregateOutputType | null
    _avg: PortAvgAggregateOutputType | null
    _sum: PortSumAggregateOutputType | null
    _min: PortMinAggregateOutputType | null
    _max: PortMaxAggregateOutputType | null
  }

  type GetPortGroupByPayload<T extends PortGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<PortGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof PortGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], PortGroupByOutputType[P]>
            : GetScalarType<T[P], PortGroupByOutputType[P]>
        }
      >
    >


  export type PortSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    code?: boolean
    name?: boolean
    countryCode?: boolean
    countryName?: boolean
    latitude?: boolean
    longitude?: boolean
    isActive?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    originSearches?: boolean | Port$originSearchesArgs<ExtArgs>
    destinationSearches?: boolean | Port$destinationSearchesArgs<ExtArgs>
    portRotations?: boolean | Port$portRotationsArgs<ExtArgs>
    _count?: boolean | PortCountOutputTypeDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["port"]>

  export type PortSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    code?: boolean
    name?: boolean
    countryCode?: boolean
    countryName?: boolean
    latitude?: boolean
    longitude?: boolean
    isActive?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }, ExtArgs["result"]["port"]>

  export type PortSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    code?: boolean
    name?: boolean
    countryCode?: boolean
    countryName?: boolean
    latitude?: boolean
    longitude?: boolean
    isActive?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }, ExtArgs["result"]["port"]>

  export type PortSelectScalar = {
    id?: boolean
    code?: boolean
    name?: boolean
    countryCode?: boolean
    countryName?: boolean
    latitude?: boolean
    longitude?: boolean
    isActive?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }

  export type PortOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "code" | "name" | "countryCode" | "countryName" | "latitude" | "longitude" | "isActive" | "createdAt" | "updatedAt", ExtArgs["result"]["port"]>
  export type PortInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    originSearches?: boolean | Port$originSearchesArgs<ExtArgs>
    destinationSearches?: boolean | Port$destinationSearchesArgs<ExtArgs>
    portRotations?: boolean | Port$portRotationsArgs<ExtArgs>
    _count?: boolean | PortCountOutputTypeDefaultArgs<ExtArgs>
  }
  export type PortIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {}
  export type PortIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {}

  export type $PortPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "Port"
    objects: {
      originSearches: Prisma.$SearchPayload<ExtArgs>[]
      destinationSearches: Prisma.$SearchPayload<ExtArgs>[]
      portRotations: Prisma.$PortRotationPayload<ExtArgs>[]
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      code: string
      name: string
      countryCode: string
      countryName: string
      latitude: number | null
      longitude: number | null
      isActive: boolean
      createdAt: Date
      updatedAt: Date
    }, ExtArgs["result"]["port"]>
    composites: {}
  }

  type PortGetPayload<S extends boolean | null | undefined | PortDefaultArgs> = $Result.GetResult<Prisma.$PortPayload, S>

  type PortCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<PortFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: PortCountAggregateInputType | true
    }

  export interface PortDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Port'], meta: { name: 'Port' } }
    /**
     * Find zero or one Port that matches the filter.
     * @param {PortFindUniqueArgs} args - Arguments to find a Port
     * @example
     * // Get one Port
     * const port = await prisma.port.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends PortFindUniqueArgs>(args: SelectSubset<T, PortFindUniqueArgs<ExtArgs>>): Prisma__PortClient<$Result.GetResult<Prisma.$PortPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one Port that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {PortFindUniqueOrThrowArgs} args - Arguments to find a Port
     * @example
     * // Get one Port
     * const port = await prisma.port.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends PortFindUniqueOrThrowArgs>(args: SelectSubset<T, PortFindUniqueOrThrowArgs<ExtArgs>>): Prisma__PortClient<$Result.GetResult<Prisma.$PortPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Port that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {PortFindFirstArgs} args - Arguments to find a Port
     * @example
     * // Get one Port
     * const port = await prisma.port.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends PortFindFirstArgs>(args?: SelectSubset<T, PortFindFirstArgs<ExtArgs>>): Prisma__PortClient<$Result.GetResult<Prisma.$PortPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Port that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {PortFindFirstOrThrowArgs} args - Arguments to find a Port
     * @example
     * // Get one Port
     * const port = await prisma.port.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends PortFindFirstOrThrowArgs>(args?: SelectSubset<T, PortFindFirstOrThrowArgs<ExtArgs>>): Prisma__PortClient<$Result.GetResult<Prisma.$PortPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more Ports that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {PortFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all Ports
     * const ports = await prisma.port.findMany()
     * 
     * // Get first 10 Ports
     * const ports = await prisma.port.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const portWithIdOnly = await prisma.port.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends PortFindManyArgs>(args?: SelectSubset<T, PortFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$PortPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a Port.
     * @param {PortCreateArgs} args - Arguments to create a Port.
     * @example
     * // Create one Port
     * const Port = await prisma.port.create({
     *   data: {
     *     // ... data to create a Port
     *   }
     * })
     * 
     */
    create<T extends PortCreateArgs>(args: SelectSubset<T, PortCreateArgs<ExtArgs>>): Prisma__PortClient<$Result.GetResult<Prisma.$PortPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many Ports.
     * @param {PortCreateManyArgs} args - Arguments to create many Ports.
     * @example
     * // Create many Ports
     * const port = await prisma.port.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends PortCreateManyArgs>(args?: SelectSubset<T, PortCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many Ports and returns the data saved in the database.
     * @param {PortCreateManyAndReturnArgs} args - Arguments to create many Ports.
     * @example
     * // Create many Ports
     * const port = await prisma.port.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many Ports and only return the `id`
     * const portWithIdOnly = await prisma.port.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends PortCreateManyAndReturnArgs>(args?: SelectSubset<T, PortCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$PortPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a Port.
     * @param {PortDeleteArgs} args - Arguments to delete one Port.
     * @example
     * // Delete one Port
     * const Port = await prisma.port.delete({
     *   where: {
     *     // ... filter to delete one Port
     *   }
     * })
     * 
     */
    delete<T extends PortDeleteArgs>(args: SelectSubset<T, PortDeleteArgs<ExtArgs>>): Prisma__PortClient<$Result.GetResult<Prisma.$PortPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one Port.
     * @param {PortUpdateArgs} args - Arguments to update one Port.
     * @example
     * // Update one Port
     * const port = await prisma.port.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends PortUpdateArgs>(args: SelectSubset<T, PortUpdateArgs<ExtArgs>>): Prisma__PortClient<$Result.GetResult<Prisma.$PortPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more Ports.
     * @param {PortDeleteManyArgs} args - Arguments to filter Ports to delete.
     * @example
     * // Delete a few Ports
     * const { count } = await prisma.port.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends PortDeleteManyArgs>(args?: SelectSubset<T, PortDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Ports.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {PortUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many Ports
     * const port = await prisma.port.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends PortUpdateManyArgs>(args: SelectSubset<T, PortUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Ports and returns the data updated in the database.
     * @param {PortUpdateManyAndReturnArgs} args - Arguments to update many Ports.
     * @example
     * // Update many Ports
     * const port = await prisma.port.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more Ports and only return the `id`
     * const portWithIdOnly = await prisma.port.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends PortUpdateManyAndReturnArgs>(args: SelectSubset<T, PortUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$PortPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one Port.
     * @param {PortUpsertArgs} args - Arguments to update or create a Port.
     * @example
     * // Update or create a Port
     * const port = await prisma.port.upsert({
     *   create: {
     *     // ... data to create a Port
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the Port we want to update
     *   }
     * })
     */
    upsert<T extends PortUpsertArgs>(args: SelectSubset<T, PortUpsertArgs<ExtArgs>>): Prisma__PortClient<$Result.GetResult<Prisma.$PortPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of Ports.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {PortCountArgs} args - Arguments to filter Ports to count.
     * @example
     * // Count the number of Ports
     * const count = await prisma.port.count({
     *   where: {
     *     // ... the filter for the Ports we want to count
     *   }
     * })
    **/
    count<T extends PortCountArgs>(
      args?: Subset<T, PortCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], PortCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a Port.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {PortAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends PortAggregateArgs>(args: Subset<T, PortAggregateArgs>): Prisma.PrismaPromise<GetPortAggregateType<T>>

    /**
     * Group by Port.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {PortGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends PortGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: PortGroupByArgs['orderBy'] }
        : { orderBy?: PortGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, PortGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetPortGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the Port model
   */
  readonly fields: PortFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for Port.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__PortClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    originSearches<T extends Port$originSearchesArgs<ExtArgs> = {}>(args?: Subset<T, Port$originSearchesArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$SearchPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    destinationSearches<T extends Port$destinationSearchesArgs<ExtArgs> = {}>(args?: Subset<T, Port$destinationSearchesArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$SearchPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    portRotations<T extends Port$portRotationsArgs<ExtArgs> = {}>(args?: Subset<T, Port$portRotationsArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$PortRotationPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the Port model
   */
  interface PortFieldRefs {
    readonly id: FieldRef<"Port", 'String'>
    readonly code: FieldRef<"Port", 'String'>
    readonly name: FieldRef<"Port", 'String'>
    readonly countryCode: FieldRef<"Port", 'String'>
    readonly countryName: FieldRef<"Port", 'String'>
    readonly latitude: FieldRef<"Port", 'Float'>
    readonly longitude: FieldRef<"Port", 'Float'>
    readonly isActive: FieldRef<"Port", 'Boolean'>
    readonly createdAt: FieldRef<"Port", 'DateTime'>
    readonly updatedAt: FieldRef<"Port", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * Port findUnique
   */
  export type PortFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Port
     */
    select?: PortSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Port
     */
    omit?: PortOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PortInclude<ExtArgs> | null
    /**
     * Filter, which Port to fetch.
     */
    where: PortWhereUniqueInput
  }

  /**
   * Port findUniqueOrThrow
   */
  export type PortFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Port
     */
    select?: PortSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Port
     */
    omit?: PortOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PortInclude<ExtArgs> | null
    /**
     * Filter, which Port to fetch.
     */
    where: PortWhereUniqueInput
  }

  /**
   * Port findFirst
   */
  export type PortFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Port
     */
    select?: PortSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Port
     */
    omit?: PortOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PortInclude<ExtArgs> | null
    /**
     * Filter, which Port to fetch.
     */
    where?: PortWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Ports to fetch.
     */
    orderBy?: PortOrderByWithRelationInput | PortOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Ports.
     */
    cursor?: PortWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Ports from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Ports.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Ports.
     */
    distinct?: PortScalarFieldEnum | PortScalarFieldEnum[]
  }

  /**
   * Port findFirstOrThrow
   */
  export type PortFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Port
     */
    select?: PortSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Port
     */
    omit?: PortOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PortInclude<ExtArgs> | null
    /**
     * Filter, which Port to fetch.
     */
    where?: PortWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Ports to fetch.
     */
    orderBy?: PortOrderByWithRelationInput | PortOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Ports.
     */
    cursor?: PortWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Ports from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Ports.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Ports.
     */
    distinct?: PortScalarFieldEnum | PortScalarFieldEnum[]
  }

  /**
   * Port findMany
   */
  export type PortFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Port
     */
    select?: PortSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Port
     */
    omit?: PortOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PortInclude<ExtArgs> | null
    /**
     * Filter, which Ports to fetch.
     */
    where?: PortWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Ports to fetch.
     */
    orderBy?: PortOrderByWithRelationInput | PortOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing Ports.
     */
    cursor?: PortWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Ports from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Ports.
     */
    skip?: number
    distinct?: PortScalarFieldEnum | PortScalarFieldEnum[]
  }

  /**
   * Port create
   */
  export type PortCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Port
     */
    select?: PortSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Port
     */
    omit?: PortOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PortInclude<ExtArgs> | null
    /**
     * The data needed to create a Port.
     */
    data: XOR<PortCreateInput, PortUncheckedCreateInput>
  }

  /**
   * Port createMany
   */
  export type PortCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many Ports.
     */
    data: PortCreateManyInput | PortCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * Port createManyAndReturn
   */
  export type PortCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Port
     */
    select?: PortSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the Port
     */
    omit?: PortOmit<ExtArgs> | null
    /**
     * The data used to create many Ports.
     */
    data: PortCreateManyInput | PortCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * Port update
   */
  export type PortUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Port
     */
    select?: PortSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Port
     */
    omit?: PortOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PortInclude<ExtArgs> | null
    /**
     * The data needed to update a Port.
     */
    data: XOR<PortUpdateInput, PortUncheckedUpdateInput>
    /**
     * Choose, which Port to update.
     */
    where: PortWhereUniqueInput
  }

  /**
   * Port updateMany
   */
  export type PortUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update Ports.
     */
    data: XOR<PortUpdateManyMutationInput, PortUncheckedUpdateManyInput>
    /**
     * Filter which Ports to update
     */
    where?: PortWhereInput
    /**
     * Limit how many Ports to update.
     */
    limit?: number
  }

  /**
   * Port updateManyAndReturn
   */
  export type PortUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Port
     */
    select?: PortSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the Port
     */
    omit?: PortOmit<ExtArgs> | null
    /**
     * The data used to update Ports.
     */
    data: XOR<PortUpdateManyMutationInput, PortUncheckedUpdateManyInput>
    /**
     * Filter which Ports to update
     */
    where?: PortWhereInput
    /**
     * Limit how many Ports to update.
     */
    limit?: number
  }

  /**
   * Port upsert
   */
  export type PortUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Port
     */
    select?: PortSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Port
     */
    omit?: PortOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PortInclude<ExtArgs> | null
    /**
     * The filter to search for the Port to update in case it exists.
     */
    where: PortWhereUniqueInput
    /**
     * In case the Port found by the `where` argument doesn't exist, create a new Port with this data.
     */
    create: XOR<PortCreateInput, PortUncheckedCreateInput>
    /**
     * In case the Port was found with the provided `where` argument, update it with this data.
     */
    update: XOR<PortUpdateInput, PortUncheckedUpdateInput>
  }

  /**
   * Port delete
   */
  export type PortDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Port
     */
    select?: PortSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Port
     */
    omit?: PortOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PortInclude<ExtArgs> | null
    /**
     * Filter which Port to delete.
     */
    where: PortWhereUniqueInput
  }

  /**
   * Port deleteMany
   */
  export type PortDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Ports to delete
     */
    where?: PortWhereInput
    /**
     * Limit how many Ports to delete.
     */
    limit?: number
  }

  /**
   * Port.originSearches
   */
  export type Port$originSearchesArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Search
     */
    select?: SearchSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Search
     */
    omit?: SearchOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SearchInclude<ExtArgs> | null
    where?: SearchWhereInput
    orderBy?: SearchOrderByWithRelationInput | SearchOrderByWithRelationInput[]
    cursor?: SearchWhereUniqueInput
    take?: number
    skip?: number
    distinct?: SearchScalarFieldEnum | SearchScalarFieldEnum[]
  }

  /**
   * Port.destinationSearches
   */
  export type Port$destinationSearchesArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Search
     */
    select?: SearchSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Search
     */
    omit?: SearchOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SearchInclude<ExtArgs> | null
    where?: SearchWhereInput
    orderBy?: SearchOrderByWithRelationInput | SearchOrderByWithRelationInput[]
    cursor?: SearchWhereUniqueInput
    take?: number
    skip?: number
    distinct?: SearchScalarFieldEnum | SearchScalarFieldEnum[]
  }

  /**
   * Port.portRotations
   */
  export type Port$portRotationsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the PortRotation
     */
    select?: PortRotationSelect<ExtArgs> | null
    /**
     * Omit specific fields from the PortRotation
     */
    omit?: PortRotationOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PortRotationInclude<ExtArgs> | null
    where?: PortRotationWhereInput
    orderBy?: PortRotationOrderByWithRelationInput | PortRotationOrderByWithRelationInput[]
    cursor?: PortRotationWhereUniqueInput
    take?: number
    skip?: number
    distinct?: PortRotationScalarFieldEnum | PortRotationScalarFieldEnum[]
  }

  /**
   * Port without action
   */
  export type PortDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Port
     */
    select?: PortSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Port
     */
    omit?: PortOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PortInclude<ExtArgs> | null
  }


  /**
   * Model Carrier
   */

  export type AggregateCarrier = {
    _count: CarrierCountAggregateOutputType | null
    _min: CarrierMinAggregateOutputType | null
    _max: CarrierMaxAggregateOutputType | null
  }

  export type CarrierMinAggregateOutputType = {
    id: string | null
    name: string | null
    code: string | null
    apiEndpoint: string | null
    apiKey: string | null
    isActive: boolean | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type CarrierMaxAggregateOutputType = {
    id: string | null
    name: string | null
    code: string | null
    apiEndpoint: string | null
    apiKey: string | null
    isActive: boolean | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type CarrierCountAggregateOutputType = {
    id: number
    name: number
    code: number
    apiEndpoint: number
    apiKey: number
    isActive: number
    createdAt: number
    updatedAt: number
    _all: number
  }


  export type CarrierMinAggregateInputType = {
    id?: true
    name?: true
    code?: true
    apiEndpoint?: true
    apiKey?: true
    isActive?: true
    createdAt?: true
    updatedAt?: true
  }

  export type CarrierMaxAggregateInputType = {
    id?: true
    name?: true
    code?: true
    apiEndpoint?: true
    apiKey?: true
    isActive?: true
    createdAt?: true
    updatedAt?: true
  }

  export type CarrierCountAggregateInputType = {
    id?: true
    name?: true
    code?: true
    apiEndpoint?: true
    apiKey?: true
    isActive?: true
    createdAt?: true
    updatedAt?: true
    _all?: true
  }

  export type CarrierAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Carrier to aggregate.
     */
    where?: CarrierWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Carriers to fetch.
     */
    orderBy?: CarrierOrderByWithRelationInput | CarrierOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: CarrierWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Carriers from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Carriers.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned Carriers
    **/
    _count?: true | CarrierCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: CarrierMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: CarrierMaxAggregateInputType
  }

  export type GetCarrierAggregateType<T extends CarrierAggregateArgs> = {
        [P in keyof T & keyof AggregateCarrier]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateCarrier[P]>
      : GetScalarType<T[P], AggregateCarrier[P]>
  }




  export type CarrierGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: CarrierWhereInput
    orderBy?: CarrierOrderByWithAggregationInput | CarrierOrderByWithAggregationInput[]
    by: CarrierScalarFieldEnum[] | CarrierScalarFieldEnum
    having?: CarrierScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: CarrierCountAggregateInputType | true
    _min?: CarrierMinAggregateInputType
    _max?: CarrierMaxAggregateInputType
  }

  export type CarrierGroupByOutputType = {
    id: string
    name: string
    code: string
    apiEndpoint: string | null
    apiKey: string | null
    isActive: boolean
    createdAt: Date
    updatedAt: Date
    _count: CarrierCountAggregateOutputType | null
    _min: CarrierMinAggregateOutputType | null
    _max: CarrierMaxAggregateOutputType | null
  }

  type GetCarrierGroupByPayload<T extends CarrierGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<CarrierGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof CarrierGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], CarrierGroupByOutputType[P]>
            : GetScalarType<T[P], CarrierGroupByOutputType[P]>
        }
      >
    >


  export type CarrierSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    name?: boolean
    code?: boolean
    apiEndpoint?: boolean
    apiKey?: boolean
    isActive?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    searchResults?: boolean | Carrier$searchResultsArgs<ExtArgs>
    _count?: boolean | CarrierCountOutputTypeDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["carrier"]>

  export type CarrierSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    name?: boolean
    code?: boolean
    apiEndpoint?: boolean
    apiKey?: boolean
    isActive?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }, ExtArgs["result"]["carrier"]>

  export type CarrierSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    name?: boolean
    code?: boolean
    apiEndpoint?: boolean
    apiKey?: boolean
    isActive?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }, ExtArgs["result"]["carrier"]>

  export type CarrierSelectScalar = {
    id?: boolean
    name?: boolean
    code?: boolean
    apiEndpoint?: boolean
    apiKey?: boolean
    isActive?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }

  export type CarrierOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "name" | "code" | "apiEndpoint" | "apiKey" | "isActive" | "createdAt" | "updatedAt", ExtArgs["result"]["carrier"]>
  export type CarrierInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    searchResults?: boolean | Carrier$searchResultsArgs<ExtArgs>
    _count?: boolean | CarrierCountOutputTypeDefaultArgs<ExtArgs>
  }
  export type CarrierIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {}
  export type CarrierIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {}

  export type $CarrierPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "Carrier"
    objects: {
      searchResults: Prisma.$SearchResultPayload<ExtArgs>[]
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      name: string
      code: string
      apiEndpoint: string | null
      apiKey: string | null
      isActive: boolean
      createdAt: Date
      updatedAt: Date
    }, ExtArgs["result"]["carrier"]>
    composites: {}
  }

  type CarrierGetPayload<S extends boolean | null | undefined | CarrierDefaultArgs> = $Result.GetResult<Prisma.$CarrierPayload, S>

  type CarrierCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<CarrierFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: CarrierCountAggregateInputType | true
    }

  export interface CarrierDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Carrier'], meta: { name: 'Carrier' } }
    /**
     * Find zero or one Carrier that matches the filter.
     * @param {CarrierFindUniqueArgs} args - Arguments to find a Carrier
     * @example
     * // Get one Carrier
     * const carrier = await prisma.carrier.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends CarrierFindUniqueArgs>(args: SelectSubset<T, CarrierFindUniqueArgs<ExtArgs>>): Prisma__CarrierClient<$Result.GetResult<Prisma.$CarrierPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one Carrier that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {CarrierFindUniqueOrThrowArgs} args - Arguments to find a Carrier
     * @example
     * // Get one Carrier
     * const carrier = await prisma.carrier.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends CarrierFindUniqueOrThrowArgs>(args: SelectSubset<T, CarrierFindUniqueOrThrowArgs<ExtArgs>>): Prisma__CarrierClient<$Result.GetResult<Prisma.$CarrierPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Carrier that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CarrierFindFirstArgs} args - Arguments to find a Carrier
     * @example
     * // Get one Carrier
     * const carrier = await prisma.carrier.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends CarrierFindFirstArgs>(args?: SelectSubset<T, CarrierFindFirstArgs<ExtArgs>>): Prisma__CarrierClient<$Result.GetResult<Prisma.$CarrierPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Carrier that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CarrierFindFirstOrThrowArgs} args - Arguments to find a Carrier
     * @example
     * // Get one Carrier
     * const carrier = await prisma.carrier.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends CarrierFindFirstOrThrowArgs>(args?: SelectSubset<T, CarrierFindFirstOrThrowArgs<ExtArgs>>): Prisma__CarrierClient<$Result.GetResult<Prisma.$CarrierPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more Carriers that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CarrierFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all Carriers
     * const carriers = await prisma.carrier.findMany()
     * 
     * // Get first 10 Carriers
     * const carriers = await prisma.carrier.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const carrierWithIdOnly = await prisma.carrier.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends CarrierFindManyArgs>(args?: SelectSubset<T, CarrierFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$CarrierPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a Carrier.
     * @param {CarrierCreateArgs} args - Arguments to create a Carrier.
     * @example
     * // Create one Carrier
     * const Carrier = await prisma.carrier.create({
     *   data: {
     *     // ... data to create a Carrier
     *   }
     * })
     * 
     */
    create<T extends CarrierCreateArgs>(args: SelectSubset<T, CarrierCreateArgs<ExtArgs>>): Prisma__CarrierClient<$Result.GetResult<Prisma.$CarrierPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many Carriers.
     * @param {CarrierCreateManyArgs} args - Arguments to create many Carriers.
     * @example
     * // Create many Carriers
     * const carrier = await prisma.carrier.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends CarrierCreateManyArgs>(args?: SelectSubset<T, CarrierCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many Carriers and returns the data saved in the database.
     * @param {CarrierCreateManyAndReturnArgs} args - Arguments to create many Carriers.
     * @example
     * // Create many Carriers
     * const carrier = await prisma.carrier.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many Carriers and only return the `id`
     * const carrierWithIdOnly = await prisma.carrier.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends CarrierCreateManyAndReturnArgs>(args?: SelectSubset<T, CarrierCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$CarrierPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a Carrier.
     * @param {CarrierDeleteArgs} args - Arguments to delete one Carrier.
     * @example
     * // Delete one Carrier
     * const Carrier = await prisma.carrier.delete({
     *   where: {
     *     // ... filter to delete one Carrier
     *   }
     * })
     * 
     */
    delete<T extends CarrierDeleteArgs>(args: SelectSubset<T, CarrierDeleteArgs<ExtArgs>>): Prisma__CarrierClient<$Result.GetResult<Prisma.$CarrierPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one Carrier.
     * @param {CarrierUpdateArgs} args - Arguments to update one Carrier.
     * @example
     * // Update one Carrier
     * const carrier = await prisma.carrier.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends CarrierUpdateArgs>(args: SelectSubset<T, CarrierUpdateArgs<ExtArgs>>): Prisma__CarrierClient<$Result.GetResult<Prisma.$CarrierPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more Carriers.
     * @param {CarrierDeleteManyArgs} args - Arguments to filter Carriers to delete.
     * @example
     * // Delete a few Carriers
     * const { count } = await prisma.carrier.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends CarrierDeleteManyArgs>(args?: SelectSubset<T, CarrierDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Carriers.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CarrierUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many Carriers
     * const carrier = await prisma.carrier.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends CarrierUpdateManyArgs>(args: SelectSubset<T, CarrierUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Carriers and returns the data updated in the database.
     * @param {CarrierUpdateManyAndReturnArgs} args - Arguments to update many Carriers.
     * @example
     * // Update many Carriers
     * const carrier = await prisma.carrier.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more Carriers and only return the `id`
     * const carrierWithIdOnly = await prisma.carrier.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends CarrierUpdateManyAndReturnArgs>(args: SelectSubset<T, CarrierUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$CarrierPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one Carrier.
     * @param {CarrierUpsertArgs} args - Arguments to update or create a Carrier.
     * @example
     * // Update or create a Carrier
     * const carrier = await prisma.carrier.upsert({
     *   create: {
     *     // ... data to create a Carrier
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the Carrier we want to update
     *   }
     * })
     */
    upsert<T extends CarrierUpsertArgs>(args: SelectSubset<T, CarrierUpsertArgs<ExtArgs>>): Prisma__CarrierClient<$Result.GetResult<Prisma.$CarrierPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of Carriers.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CarrierCountArgs} args - Arguments to filter Carriers to count.
     * @example
     * // Count the number of Carriers
     * const count = await prisma.carrier.count({
     *   where: {
     *     // ... the filter for the Carriers we want to count
     *   }
     * })
    **/
    count<T extends CarrierCountArgs>(
      args?: Subset<T, CarrierCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], CarrierCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a Carrier.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CarrierAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends CarrierAggregateArgs>(args: Subset<T, CarrierAggregateArgs>): Prisma.PrismaPromise<GetCarrierAggregateType<T>>

    /**
     * Group by Carrier.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CarrierGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends CarrierGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: CarrierGroupByArgs['orderBy'] }
        : { orderBy?: CarrierGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, CarrierGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetCarrierGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the Carrier model
   */
  readonly fields: CarrierFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for Carrier.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__CarrierClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    searchResults<T extends Carrier$searchResultsArgs<ExtArgs> = {}>(args?: Subset<T, Carrier$searchResultsArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$SearchResultPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the Carrier model
   */
  interface CarrierFieldRefs {
    readonly id: FieldRef<"Carrier", 'String'>
    readonly name: FieldRef<"Carrier", 'String'>
    readonly code: FieldRef<"Carrier", 'String'>
    readonly apiEndpoint: FieldRef<"Carrier", 'String'>
    readonly apiKey: FieldRef<"Carrier", 'String'>
    readonly isActive: FieldRef<"Carrier", 'Boolean'>
    readonly createdAt: FieldRef<"Carrier", 'DateTime'>
    readonly updatedAt: FieldRef<"Carrier", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * Carrier findUnique
   */
  export type CarrierFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Carrier
     */
    select?: CarrierSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Carrier
     */
    omit?: CarrierOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CarrierInclude<ExtArgs> | null
    /**
     * Filter, which Carrier to fetch.
     */
    where: CarrierWhereUniqueInput
  }

  /**
   * Carrier findUniqueOrThrow
   */
  export type CarrierFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Carrier
     */
    select?: CarrierSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Carrier
     */
    omit?: CarrierOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CarrierInclude<ExtArgs> | null
    /**
     * Filter, which Carrier to fetch.
     */
    where: CarrierWhereUniqueInput
  }

  /**
   * Carrier findFirst
   */
  export type CarrierFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Carrier
     */
    select?: CarrierSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Carrier
     */
    omit?: CarrierOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CarrierInclude<ExtArgs> | null
    /**
     * Filter, which Carrier to fetch.
     */
    where?: CarrierWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Carriers to fetch.
     */
    orderBy?: CarrierOrderByWithRelationInput | CarrierOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Carriers.
     */
    cursor?: CarrierWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Carriers from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Carriers.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Carriers.
     */
    distinct?: CarrierScalarFieldEnum | CarrierScalarFieldEnum[]
  }

  /**
   * Carrier findFirstOrThrow
   */
  export type CarrierFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Carrier
     */
    select?: CarrierSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Carrier
     */
    omit?: CarrierOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CarrierInclude<ExtArgs> | null
    /**
     * Filter, which Carrier to fetch.
     */
    where?: CarrierWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Carriers to fetch.
     */
    orderBy?: CarrierOrderByWithRelationInput | CarrierOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Carriers.
     */
    cursor?: CarrierWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Carriers from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Carriers.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Carriers.
     */
    distinct?: CarrierScalarFieldEnum | CarrierScalarFieldEnum[]
  }

  /**
   * Carrier findMany
   */
  export type CarrierFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Carrier
     */
    select?: CarrierSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Carrier
     */
    omit?: CarrierOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CarrierInclude<ExtArgs> | null
    /**
     * Filter, which Carriers to fetch.
     */
    where?: CarrierWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Carriers to fetch.
     */
    orderBy?: CarrierOrderByWithRelationInput | CarrierOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing Carriers.
     */
    cursor?: CarrierWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Carriers from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Carriers.
     */
    skip?: number
    distinct?: CarrierScalarFieldEnum | CarrierScalarFieldEnum[]
  }

  /**
   * Carrier create
   */
  export type CarrierCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Carrier
     */
    select?: CarrierSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Carrier
     */
    omit?: CarrierOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CarrierInclude<ExtArgs> | null
    /**
     * The data needed to create a Carrier.
     */
    data: XOR<CarrierCreateInput, CarrierUncheckedCreateInput>
  }

  /**
   * Carrier createMany
   */
  export type CarrierCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many Carriers.
     */
    data: CarrierCreateManyInput | CarrierCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * Carrier createManyAndReturn
   */
  export type CarrierCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Carrier
     */
    select?: CarrierSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the Carrier
     */
    omit?: CarrierOmit<ExtArgs> | null
    /**
     * The data used to create many Carriers.
     */
    data: CarrierCreateManyInput | CarrierCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * Carrier update
   */
  export type CarrierUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Carrier
     */
    select?: CarrierSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Carrier
     */
    omit?: CarrierOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CarrierInclude<ExtArgs> | null
    /**
     * The data needed to update a Carrier.
     */
    data: XOR<CarrierUpdateInput, CarrierUncheckedUpdateInput>
    /**
     * Choose, which Carrier to update.
     */
    where: CarrierWhereUniqueInput
  }

  /**
   * Carrier updateMany
   */
  export type CarrierUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update Carriers.
     */
    data: XOR<CarrierUpdateManyMutationInput, CarrierUncheckedUpdateManyInput>
    /**
     * Filter which Carriers to update
     */
    where?: CarrierWhereInput
    /**
     * Limit how many Carriers to update.
     */
    limit?: number
  }

  /**
   * Carrier updateManyAndReturn
   */
  export type CarrierUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Carrier
     */
    select?: CarrierSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the Carrier
     */
    omit?: CarrierOmit<ExtArgs> | null
    /**
     * The data used to update Carriers.
     */
    data: XOR<CarrierUpdateManyMutationInput, CarrierUncheckedUpdateManyInput>
    /**
     * Filter which Carriers to update
     */
    where?: CarrierWhereInput
    /**
     * Limit how many Carriers to update.
     */
    limit?: number
  }

  /**
   * Carrier upsert
   */
  export type CarrierUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Carrier
     */
    select?: CarrierSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Carrier
     */
    omit?: CarrierOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CarrierInclude<ExtArgs> | null
    /**
     * The filter to search for the Carrier to update in case it exists.
     */
    where: CarrierWhereUniqueInput
    /**
     * In case the Carrier found by the `where` argument doesn't exist, create a new Carrier with this data.
     */
    create: XOR<CarrierCreateInput, CarrierUncheckedCreateInput>
    /**
     * In case the Carrier was found with the provided `where` argument, update it with this data.
     */
    update: XOR<CarrierUpdateInput, CarrierUncheckedUpdateInput>
  }

  /**
   * Carrier delete
   */
  export type CarrierDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Carrier
     */
    select?: CarrierSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Carrier
     */
    omit?: CarrierOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CarrierInclude<ExtArgs> | null
    /**
     * Filter which Carrier to delete.
     */
    where: CarrierWhereUniqueInput
  }

  /**
   * Carrier deleteMany
   */
  export type CarrierDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Carriers to delete
     */
    where?: CarrierWhereInput
    /**
     * Limit how many Carriers to delete.
     */
    limit?: number
  }

  /**
   * Carrier.searchResults
   */
  export type Carrier$searchResultsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the SearchResult
     */
    select?: SearchResultSelect<ExtArgs> | null
    /**
     * Omit specific fields from the SearchResult
     */
    omit?: SearchResultOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SearchResultInclude<ExtArgs> | null
    where?: SearchResultWhereInput
    orderBy?: SearchResultOrderByWithRelationInput | SearchResultOrderByWithRelationInput[]
    cursor?: SearchResultWhereUniqueInput
    take?: number
    skip?: number
    distinct?: SearchResultScalarFieldEnum | SearchResultScalarFieldEnum[]
  }

  /**
   * Carrier without action
   */
  export type CarrierDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Carrier
     */
    select?: CarrierSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Carrier
     */
    omit?: CarrierOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CarrierInclude<ExtArgs> | null
  }


  /**
   * Model Search
   */

  export type AggregateSearch = {
    _count: SearchCountAggregateOutputType | null
    _avg: SearchAvgAggregateOutputType | null
    _sum: SearchSumAggregateOutputType | null
    _min: SearchMinAggregateOutputType | null
    _max: SearchMaxAggregateOutputType | null
  }

  export type SearchAvgAggregateOutputType = {
    resultsCount: number | null
    searchDurationMs: number | null
  }

  export type SearchSumAggregateOutputType = {
    resultsCount: number | null
    searchDurationMs: number | null
  }

  export type SearchMinAggregateOutputType = {
    id: string | null
    userId: string | null
    originPortId: string | null
    destinationPortId: string | null
    departureDate: Date | null
    searchTimestamp: Date | null
    resultsCount: number | null
    searchDurationMs: number | null
  }

  export type SearchMaxAggregateOutputType = {
    id: string | null
    userId: string | null
    originPortId: string | null
    destinationPortId: string | null
    departureDate: Date | null
    searchTimestamp: Date | null
    resultsCount: number | null
    searchDurationMs: number | null
  }

  export type SearchCountAggregateOutputType = {
    id: number
    userId: number
    originPortId: number
    destinationPortId: number
    departureDate: number
    searchTimestamp: number
    resultsCount: number
    searchDurationMs: number
    _all: number
  }


  export type SearchAvgAggregateInputType = {
    resultsCount?: true
    searchDurationMs?: true
  }

  export type SearchSumAggregateInputType = {
    resultsCount?: true
    searchDurationMs?: true
  }

  export type SearchMinAggregateInputType = {
    id?: true
    userId?: true
    originPortId?: true
    destinationPortId?: true
    departureDate?: true
    searchTimestamp?: true
    resultsCount?: true
    searchDurationMs?: true
  }

  export type SearchMaxAggregateInputType = {
    id?: true
    userId?: true
    originPortId?: true
    destinationPortId?: true
    departureDate?: true
    searchTimestamp?: true
    resultsCount?: true
    searchDurationMs?: true
  }

  export type SearchCountAggregateInputType = {
    id?: true
    userId?: true
    originPortId?: true
    destinationPortId?: true
    departureDate?: true
    searchTimestamp?: true
    resultsCount?: true
    searchDurationMs?: true
    _all?: true
  }

  export type SearchAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Search to aggregate.
     */
    where?: SearchWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Searches to fetch.
     */
    orderBy?: SearchOrderByWithRelationInput | SearchOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: SearchWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Searches from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Searches.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned Searches
    **/
    _count?: true | SearchCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: SearchAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: SearchSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: SearchMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: SearchMaxAggregateInputType
  }

  export type GetSearchAggregateType<T extends SearchAggregateArgs> = {
        [P in keyof T & keyof AggregateSearch]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateSearch[P]>
      : GetScalarType<T[P], AggregateSearch[P]>
  }




  export type SearchGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: SearchWhereInput
    orderBy?: SearchOrderByWithAggregationInput | SearchOrderByWithAggregationInput[]
    by: SearchScalarFieldEnum[] | SearchScalarFieldEnum
    having?: SearchScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: SearchCountAggregateInputType | true
    _avg?: SearchAvgAggregateInputType
    _sum?: SearchSumAggregateInputType
    _min?: SearchMinAggregateInputType
    _max?: SearchMaxAggregateInputType
  }

  export type SearchGroupByOutputType = {
    id: string
    userId: string
    originPortId: string
    destinationPortId: string
    departureDate: Date
    searchTimestamp: Date
    resultsCount: number
    searchDurationMs: number | null
    _count: SearchCountAggregateOutputType | null
    _avg: SearchAvgAggregateOutputType | null
    _sum: SearchSumAggregateOutputType | null
    _min: SearchMinAggregateOutputType | null
    _max: SearchMaxAggregateOutputType | null
  }

  type GetSearchGroupByPayload<T extends SearchGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<SearchGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof SearchGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], SearchGroupByOutputType[P]>
            : GetScalarType<T[P], SearchGroupByOutputType[P]>
        }
      >
    >


  export type SearchSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    userId?: boolean
    originPortId?: boolean
    destinationPortId?: boolean
    departureDate?: boolean
    searchTimestamp?: boolean
    resultsCount?: boolean
    searchDurationMs?: boolean
    user?: boolean | UserDefaultArgs<ExtArgs>
    originPort?: boolean | PortDefaultArgs<ExtArgs>
    destinationPort?: boolean | PortDefaultArgs<ExtArgs>
    searchResults?: boolean | Search$searchResultsArgs<ExtArgs>
    _count?: boolean | SearchCountOutputTypeDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["search"]>

  export type SearchSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    userId?: boolean
    originPortId?: boolean
    destinationPortId?: boolean
    departureDate?: boolean
    searchTimestamp?: boolean
    resultsCount?: boolean
    searchDurationMs?: boolean
    user?: boolean | UserDefaultArgs<ExtArgs>
    originPort?: boolean | PortDefaultArgs<ExtArgs>
    destinationPort?: boolean | PortDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["search"]>

  export type SearchSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    userId?: boolean
    originPortId?: boolean
    destinationPortId?: boolean
    departureDate?: boolean
    searchTimestamp?: boolean
    resultsCount?: boolean
    searchDurationMs?: boolean
    user?: boolean | UserDefaultArgs<ExtArgs>
    originPort?: boolean | PortDefaultArgs<ExtArgs>
    destinationPort?: boolean | PortDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["search"]>

  export type SearchSelectScalar = {
    id?: boolean
    userId?: boolean
    originPortId?: boolean
    destinationPortId?: boolean
    departureDate?: boolean
    searchTimestamp?: boolean
    resultsCount?: boolean
    searchDurationMs?: boolean
  }

  export type SearchOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "userId" | "originPortId" | "destinationPortId" | "departureDate" | "searchTimestamp" | "resultsCount" | "searchDurationMs", ExtArgs["result"]["search"]>
  export type SearchInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | UserDefaultArgs<ExtArgs>
    originPort?: boolean | PortDefaultArgs<ExtArgs>
    destinationPort?: boolean | PortDefaultArgs<ExtArgs>
    searchResults?: boolean | Search$searchResultsArgs<ExtArgs>
    _count?: boolean | SearchCountOutputTypeDefaultArgs<ExtArgs>
  }
  export type SearchIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | UserDefaultArgs<ExtArgs>
    originPort?: boolean | PortDefaultArgs<ExtArgs>
    destinationPort?: boolean | PortDefaultArgs<ExtArgs>
  }
  export type SearchIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | UserDefaultArgs<ExtArgs>
    originPort?: boolean | PortDefaultArgs<ExtArgs>
    destinationPort?: boolean | PortDefaultArgs<ExtArgs>
  }

  export type $SearchPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "Search"
    objects: {
      user: Prisma.$UserPayload<ExtArgs>
      originPort: Prisma.$PortPayload<ExtArgs>
      destinationPort: Prisma.$PortPayload<ExtArgs>
      searchResults: Prisma.$SearchResultPayload<ExtArgs>[]
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      userId: string
      originPortId: string
      destinationPortId: string
      departureDate: Date
      searchTimestamp: Date
      resultsCount: number
      searchDurationMs: number | null
    }, ExtArgs["result"]["search"]>
    composites: {}
  }

  type SearchGetPayload<S extends boolean | null | undefined | SearchDefaultArgs> = $Result.GetResult<Prisma.$SearchPayload, S>

  type SearchCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<SearchFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: SearchCountAggregateInputType | true
    }

  export interface SearchDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Search'], meta: { name: 'Search' } }
    /**
     * Find zero or one Search that matches the filter.
     * @param {SearchFindUniqueArgs} args - Arguments to find a Search
     * @example
     * // Get one Search
     * const search = await prisma.search.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends SearchFindUniqueArgs>(args: SelectSubset<T, SearchFindUniqueArgs<ExtArgs>>): Prisma__SearchClient<$Result.GetResult<Prisma.$SearchPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one Search that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {SearchFindUniqueOrThrowArgs} args - Arguments to find a Search
     * @example
     * // Get one Search
     * const search = await prisma.search.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends SearchFindUniqueOrThrowArgs>(args: SelectSubset<T, SearchFindUniqueOrThrowArgs<ExtArgs>>): Prisma__SearchClient<$Result.GetResult<Prisma.$SearchPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Search that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {SearchFindFirstArgs} args - Arguments to find a Search
     * @example
     * // Get one Search
     * const search = await prisma.search.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends SearchFindFirstArgs>(args?: SelectSubset<T, SearchFindFirstArgs<ExtArgs>>): Prisma__SearchClient<$Result.GetResult<Prisma.$SearchPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Search that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {SearchFindFirstOrThrowArgs} args - Arguments to find a Search
     * @example
     * // Get one Search
     * const search = await prisma.search.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends SearchFindFirstOrThrowArgs>(args?: SelectSubset<T, SearchFindFirstOrThrowArgs<ExtArgs>>): Prisma__SearchClient<$Result.GetResult<Prisma.$SearchPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more Searches that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {SearchFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all Searches
     * const searches = await prisma.search.findMany()
     * 
     * // Get first 10 Searches
     * const searches = await prisma.search.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const searchWithIdOnly = await prisma.search.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends SearchFindManyArgs>(args?: SelectSubset<T, SearchFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$SearchPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a Search.
     * @param {SearchCreateArgs} args - Arguments to create a Search.
     * @example
     * // Create one Search
     * const Search = await prisma.search.create({
     *   data: {
     *     // ... data to create a Search
     *   }
     * })
     * 
     */
    create<T extends SearchCreateArgs>(args: SelectSubset<T, SearchCreateArgs<ExtArgs>>): Prisma__SearchClient<$Result.GetResult<Prisma.$SearchPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many Searches.
     * @param {SearchCreateManyArgs} args - Arguments to create many Searches.
     * @example
     * // Create many Searches
     * const search = await prisma.search.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends SearchCreateManyArgs>(args?: SelectSubset<T, SearchCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many Searches and returns the data saved in the database.
     * @param {SearchCreateManyAndReturnArgs} args - Arguments to create many Searches.
     * @example
     * // Create many Searches
     * const search = await prisma.search.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many Searches and only return the `id`
     * const searchWithIdOnly = await prisma.search.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends SearchCreateManyAndReturnArgs>(args?: SelectSubset<T, SearchCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$SearchPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a Search.
     * @param {SearchDeleteArgs} args - Arguments to delete one Search.
     * @example
     * // Delete one Search
     * const Search = await prisma.search.delete({
     *   where: {
     *     // ... filter to delete one Search
     *   }
     * })
     * 
     */
    delete<T extends SearchDeleteArgs>(args: SelectSubset<T, SearchDeleteArgs<ExtArgs>>): Prisma__SearchClient<$Result.GetResult<Prisma.$SearchPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one Search.
     * @param {SearchUpdateArgs} args - Arguments to update one Search.
     * @example
     * // Update one Search
     * const search = await prisma.search.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends SearchUpdateArgs>(args: SelectSubset<T, SearchUpdateArgs<ExtArgs>>): Prisma__SearchClient<$Result.GetResult<Prisma.$SearchPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more Searches.
     * @param {SearchDeleteManyArgs} args - Arguments to filter Searches to delete.
     * @example
     * // Delete a few Searches
     * const { count } = await prisma.search.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends SearchDeleteManyArgs>(args?: SelectSubset<T, SearchDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Searches.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {SearchUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many Searches
     * const search = await prisma.search.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends SearchUpdateManyArgs>(args: SelectSubset<T, SearchUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Searches and returns the data updated in the database.
     * @param {SearchUpdateManyAndReturnArgs} args - Arguments to update many Searches.
     * @example
     * // Update many Searches
     * const search = await prisma.search.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more Searches and only return the `id`
     * const searchWithIdOnly = await prisma.search.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends SearchUpdateManyAndReturnArgs>(args: SelectSubset<T, SearchUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$SearchPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one Search.
     * @param {SearchUpsertArgs} args - Arguments to update or create a Search.
     * @example
     * // Update or create a Search
     * const search = await prisma.search.upsert({
     *   create: {
     *     // ... data to create a Search
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the Search we want to update
     *   }
     * })
     */
    upsert<T extends SearchUpsertArgs>(args: SelectSubset<T, SearchUpsertArgs<ExtArgs>>): Prisma__SearchClient<$Result.GetResult<Prisma.$SearchPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of Searches.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {SearchCountArgs} args - Arguments to filter Searches to count.
     * @example
     * // Count the number of Searches
     * const count = await prisma.search.count({
     *   where: {
     *     // ... the filter for the Searches we want to count
     *   }
     * })
    **/
    count<T extends SearchCountArgs>(
      args?: Subset<T, SearchCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], SearchCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a Search.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {SearchAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends SearchAggregateArgs>(args: Subset<T, SearchAggregateArgs>): Prisma.PrismaPromise<GetSearchAggregateType<T>>

    /**
     * Group by Search.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {SearchGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends SearchGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: SearchGroupByArgs['orderBy'] }
        : { orderBy?: SearchGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, SearchGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetSearchGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the Search model
   */
  readonly fields: SearchFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for Search.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__SearchClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    user<T extends UserDefaultArgs<ExtArgs> = {}>(args?: Subset<T, UserDefaultArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    originPort<T extends PortDefaultArgs<ExtArgs> = {}>(args?: Subset<T, PortDefaultArgs<ExtArgs>>): Prisma__PortClient<$Result.GetResult<Prisma.$PortPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    destinationPort<T extends PortDefaultArgs<ExtArgs> = {}>(args?: Subset<T, PortDefaultArgs<ExtArgs>>): Prisma__PortClient<$Result.GetResult<Prisma.$PortPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    searchResults<T extends Search$searchResultsArgs<ExtArgs> = {}>(args?: Subset<T, Search$searchResultsArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$SearchResultPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the Search model
   */
  interface SearchFieldRefs {
    readonly id: FieldRef<"Search", 'String'>
    readonly userId: FieldRef<"Search", 'String'>
    readonly originPortId: FieldRef<"Search", 'String'>
    readonly destinationPortId: FieldRef<"Search", 'String'>
    readonly departureDate: FieldRef<"Search", 'DateTime'>
    readonly searchTimestamp: FieldRef<"Search", 'DateTime'>
    readonly resultsCount: FieldRef<"Search", 'Int'>
    readonly searchDurationMs: FieldRef<"Search", 'Int'>
  }
    

  // Custom InputTypes
  /**
   * Search findUnique
   */
  export type SearchFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Search
     */
    select?: SearchSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Search
     */
    omit?: SearchOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SearchInclude<ExtArgs> | null
    /**
     * Filter, which Search to fetch.
     */
    where: SearchWhereUniqueInput
  }

  /**
   * Search findUniqueOrThrow
   */
  export type SearchFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Search
     */
    select?: SearchSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Search
     */
    omit?: SearchOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SearchInclude<ExtArgs> | null
    /**
     * Filter, which Search to fetch.
     */
    where: SearchWhereUniqueInput
  }

  /**
   * Search findFirst
   */
  export type SearchFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Search
     */
    select?: SearchSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Search
     */
    omit?: SearchOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SearchInclude<ExtArgs> | null
    /**
     * Filter, which Search to fetch.
     */
    where?: SearchWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Searches to fetch.
     */
    orderBy?: SearchOrderByWithRelationInput | SearchOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Searches.
     */
    cursor?: SearchWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Searches from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Searches.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Searches.
     */
    distinct?: SearchScalarFieldEnum | SearchScalarFieldEnum[]
  }

  /**
   * Search findFirstOrThrow
   */
  export type SearchFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Search
     */
    select?: SearchSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Search
     */
    omit?: SearchOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SearchInclude<ExtArgs> | null
    /**
     * Filter, which Search to fetch.
     */
    where?: SearchWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Searches to fetch.
     */
    orderBy?: SearchOrderByWithRelationInput | SearchOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Searches.
     */
    cursor?: SearchWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Searches from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Searches.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Searches.
     */
    distinct?: SearchScalarFieldEnum | SearchScalarFieldEnum[]
  }

  /**
   * Search findMany
   */
  export type SearchFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Search
     */
    select?: SearchSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Search
     */
    omit?: SearchOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SearchInclude<ExtArgs> | null
    /**
     * Filter, which Searches to fetch.
     */
    where?: SearchWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Searches to fetch.
     */
    orderBy?: SearchOrderByWithRelationInput | SearchOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing Searches.
     */
    cursor?: SearchWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Searches from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Searches.
     */
    skip?: number
    distinct?: SearchScalarFieldEnum | SearchScalarFieldEnum[]
  }

  /**
   * Search create
   */
  export type SearchCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Search
     */
    select?: SearchSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Search
     */
    omit?: SearchOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SearchInclude<ExtArgs> | null
    /**
     * The data needed to create a Search.
     */
    data: XOR<SearchCreateInput, SearchUncheckedCreateInput>
  }

  /**
   * Search createMany
   */
  export type SearchCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many Searches.
     */
    data: SearchCreateManyInput | SearchCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * Search createManyAndReturn
   */
  export type SearchCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Search
     */
    select?: SearchSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the Search
     */
    omit?: SearchOmit<ExtArgs> | null
    /**
     * The data used to create many Searches.
     */
    data: SearchCreateManyInput | SearchCreateManyInput[]
    skipDuplicates?: boolean
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SearchIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * Search update
   */
  export type SearchUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Search
     */
    select?: SearchSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Search
     */
    omit?: SearchOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SearchInclude<ExtArgs> | null
    /**
     * The data needed to update a Search.
     */
    data: XOR<SearchUpdateInput, SearchUncheckedUpdateInput>
    /**
     * Choose, which Search to update.
     */
    where: SearchWhereUniqueInput
  }

  /**
   * Search updateMany
   */
  export type SearchUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update Searches.
     */
    data: XOR<SearchUpdateManyMutationInput, SearchUncheckedUpdateManyInput>
    /**
     * Filter which Searches to update
     */
    where?: SearchWhereInput
    /**
     * Limit how many Searches to update.
     */
    limit?: number
  }

  /**
   * Search updateManyAndReturn
   */
  export type SearchUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Search
     */
    select?: SearchSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the Search
     */
    omit?: SearchOmit<ExtArgs> | null
    /**
     * The data used to update Searches.
     */
    data: XOR<SearchUpdateManyMutationInput, SearchUncheckedUpdateManyInput>
    /**
     * Filter which Searches to update
     */
    where?: SearchWhereInput
    /**
     * Limit how many Searches to update.
     */
    limit?: number
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SearchIncludeUpdateManyAndReturn<ExtArgs> | null
  }

  /**
   * Search upsert
   */
  export type SearchUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Search
     */
    select?: SearchSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Search
     */
    omit?: SearchOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SearchInclude<ExtArgs> | null
    /**
     * The filter to search for the Search to update in case it exists.
     */
    where: SearchWhereUniqueInput
    /**
     * In case the Search found by the `where` argument doesn't exist, create a new Search with this data.
     */
    create: XOR<SearchCreateInput, SearchUncheckedCreateInput>
    /**
     * In case the Search was found with the provided `where` argument, update it with this data.
     */
    update: XOR<SearchUpdateInput, SearchUncheckedUpdateInput>
  }

  /**
   * Search delete
   */
  export type SearchDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Search
     */
    select?: SearchSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Search
     */
    omit?: SearchOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SearchInclude<ExtArgs> | null
    /**
     * Filter which Search to delete.
     */
    where: SearchWhereUniqueInput
  }

  /**
   * Search deleteMany
   */
  export type SearchDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Searches to delete
     */
    where?: SearchWhereInput
    /**
     * Limit how many Searches to delete.
     */
    limit?: number
  }

  /**
   * Search.searchResults
   */
  export type Search$searchResultsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the SearchResult
     */
    select?: SearchResultSelect<ExtArgs> | null
    /**
     * Omit specific fields from the SearchResult
     */
    omit?: SearchResultOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SearchResultInclude<ExtArgs> | null
    where?: SearchResultWhereInput
    orderBy?: SearchResultOrderByWithRelationInput | SearchResultOrderByWithRelationInput[]
    cursor?: SearchResultWhereUniqueInput
    take?: number
    skip?: number
    distinct?: SearchResultScalarFieldEnum | SearchResultScalarFieldEnum[]
  }

  /**
   * Search without action
   */
  export type SearchDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Search
     */
    select?: SearchSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Search
     */
    omit?: SearchOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SearchInclude<ExtArgs> | null
  }


  /**
   * Model SearchResult
   */

  export type AggregateSearchResult = {
    _count: SearchResultCountAggregateOutputType | null
    _avg: SearchResultAvgAggregateOutputType | null
    _sum: SearchResultSumAggregateOutputType | null
    _min: SearchResultMinAggregateOutputType | null
    _max: SearchResultMaxAggregateOutputType | null
  }

  export type SearchResultAvgAggregateOutputType = {
    transitDays: number | null
  }

  export type SearchResultSumAggregateOutputType = {
    transitDays: number | null
  }

  export type SearchResultMinAggregateOutputType = {
    id: string | null
    searchId: string | null
    carrierId: string | null
    vesselName: string | null
    voyageNumber: string | null
    departureDate: Date | null
    arrivalDate: Date | null
    transitDays: number | null
    createdAt: Date | null
  }

  export type SearchResultMaxAggregateOutputType = {
    id: string | null
    searchId: string | null
    carrierId: string | null
    vesselName: string | null
    voyageNumber: string | null
    departureDate: Date | null
    arrivalDate: Date | null
    transitDays: number | null
    createdAt: Date | null
  }

  export type SearchResultCountAggregateOutputType = {
    id: number
    searchId: number
    carrierId: number
    vesselName: number
    voyageNumber: number
    departureDate: number
    arrivalDate: number
    transitDays: number
    rawApiResponse: number
    createdAt: number
    _all: number
  }


  export type SearchResultAvgAggregateInputType = {
    transitDays?: true
  }

  export type SearchResultSumAggregateInputType = {
    transitDays?: true
  }

  export type SearchResultMinAggregateInputType = {
    id?: true
    searchId?: true
    carrierId?: true
    vesselName?: true
    voyageNumber?: true
    departureDate?: true
    arrivalDate?: true
    transitDays?: true
    createdAt?: true
  }

  export type SearchResultMaxAggregateInputType = {
    id?: true
    searchId?: true
    carrierId?: true
    vesselName?: true
    voyageNumber?: true
    departureDate?: true
    arrivalDate?: true
    transitDays?: true
    createdAt?: true
  }

  export type SearchResultCountAggregateInputType = {
    id?: true
    searchId?: true
    carrierId?: true
    vesselName?: true
    voyageNumber?: true
    departureDate?: true
    arrivalDate?: true
    transitDays?: true
    rawApiResponse?: true
    createdAt?: true
    _all?: true
  }

  export type SearchResultAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which SearchResult to aggregate.
     */
    where?: SearchResultWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of SearchResults to fetch.
     */
    orderBy?: SearchResultOrderByWithRelationInput | SearchResultOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: SearchResultWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` SearchResults from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` SearchResults.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned SearchResults
    **/
    _count?: true | SearchResultCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: SearchResultAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: SearchResultSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: SearchResultMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: SearchResultMaxAggregateInputType
  }

  export type GetSearchResultAggregateType<T extends SearchResultAggregateArgs> = {
        [P in keyof T & keyof AggregateSearchResult]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateSearchResult[P]>
      : GetScalarType<T[P], AggregateSearchResult[P]>
  }




  export type SearchResultGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: SearchResultWhereInput
    orderBy?: SearchResultOrderByWithAggregationInput | SearchResultOrderByWithAggregationInput[]
    by: SearchResultScalarFieldEnum[] | SearchResultScalarFieldEnum
    having?: SearchResultScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: SearchResultCountAggregateInputType | true
    _avg?: SearchResultAvgAggregateInputType
    _sum?: SearchResultSumAggregateInputType
    _min?: SearchResultMinAggregateInputType
    _max?: SearchResultMaxAggregateInputType
  }

  export type SearchResultGroupByOutputType = {
    id: string
    searchId: string
    carrierId: string
    vesselName: string
    voyageNumber: string | null
    departureDate: Date
    arrivalDate: Date
    transitDays: number
    rawApiResponse: JsonValue | null
    createdAt: Date
    _count: SearchResultCountAggregateOutputType | null
    _avg: SearchResultAvgAggregateOutputType | null
    _sum: SearchResultSumAggregateOutputType | null
    _min: SearchResultMinAggregateOutputType | null
    _max: SearchResultMaxAggregateOutputType | null
  }

  type GetSearchResultGroupByPayload<T extends SearchResultGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<SearchResultGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof SearchResultGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], SearchResultGroupByOutputType[P]>
            : GetScalarType<T[P], SearchResultGroupByOutputType[P]>
        }
      >
    >


  export type SearchResultSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    searchId?: boolean
    carrierId?: boolean
    vesselName?: boolean
    voyageNumber?: boolean
    departureDate?: boolean
    arrivalDate?: boolean
    transitDays?: boolean
    rawApiResponse?: boolean
    createdAt?: boolean
    search?: boolean | SearchDefaultArgs<ExtArgs>
    carrier?: boolean | CarrierDefaultArgs<ExtArgs>
    portRotations?: boolean | SearchResult$portRotationsArgs<ExtArgs>
    _count?: boolean | SearchResultCountOutputTypeDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["searchResult"]>

  export type SearchResultSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    searchId?: boolean
    carrierId?: boolean
    vesselName?: boolean
    voyageNumber?: boolean
    departureDate?: boolean
    arrivalDate?: boolean
    transitDays?: boolean
    rawApiResponse?: boolean
    createdAt?: boolean
    search?: boolean | SearchDefaultArgs<ExtArgs>
    carrier?: boolean | CarrierDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["searchResult"]>

  export type SearchResultSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    searchId?: boolean
    carrierId?: boolean
    vesselName?: boolean
    voyageNumber?: boolean
    departureDate?: boolean
    arrivalDate?: boolean
    transitDays?: boolean
    rawApiResponse?: boolean
    createdAt?: boolean
    search?: boolean | SearchDefaultArgs<ExtArgs>
    carrier?: boolean | CarrierDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["searchResult"]>

  export type SearchResultSelectScalar = {
    id?: boolean
    searchId?: boolean
    carrierId?: boolean
    vesselName?: boolean
    voyageNumber?: boolean
    departureDate?: boolean
    arrivalDate?: boolean
    transitDays?: boolean
    rawApiResponse?: boolean
    createdAt?: boolean
  }

  export type SearchResultOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "searchId" | "carrierId" | "vesselName" | "voyageNumber" | "departureDate" | "arrivalDate" | "transitDays" | "rawApiResponse" | "createdAt", ExtArgs["result"]["searchResult"]>
  export type SearchResultInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    search?: boolean | SearchDefaultArgs<ExtArgs>
    carrier?: boolean | CarrierDefaultArgs<ExtArgs>
    portRotations?: boolean | SearchResult$portRotationsArgs<ExtArgs>
    _count?: boolean | SearchResultCountOutputTypeDefaultArgs<ExtArgs>
  }
  export type SearchResultIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    search?: boolean | SearchDefaultArgs<ExtArgs>
    carrier?: boolean | CarrierDefaultArgs<ExtArgs>
  }
  export type SearchResultIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    search?: boolean | SearchDefaultArgs<ExtArgs>
    carrier?: boolean | CarrierDefaultArgs<ExtArgs>
  }

  export type $SearchResultPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "SearchResult"
    objects: {
      search: Prisma.$SearchPayload<ExtArgs>
      carrier: Prisma.$CarrierPayload<ExtArgs>
      portRotations: Prisma.$PortRotationPayload<ExtArgs>[]
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      searchId: string
      carrierId: string
      vesselName: string
      voyageNumber: string | null
      departureDate: Date
      arrivalDate: Date
      transitDays: number
      rawApiResponse: Prisma.JsonValue | null
      createdAt: Date
    }, ExtArgs["result"]["searchResult"]>
    composites: {}
  }

  type SearchResultGetPayload<S extends boolean | null | undefined | SearchResultDefaultArgs> = $Result.GetResult<Prisma.$SearchResultPayload, S>

  type SearchResultCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<SearchResultFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: SearchResultCountAggregateInputType | true
    }

  export interface SearchResultDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['SearchResult'], meta: { name: 'SearchResult' } }
    /**
     * Find zero or one SearchResult that matches the filter.
     * @param {SearchResultFindUniqueArgs} args - Arguments to find a SearchResult
     * @example
     * // Get one SearchResult
     * const searchResult = await prisma.searchResult.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends SearchResultFindUniqueArgs>(args: SelectSubset<T, SearchResultFindUniqueArgs<ExtArgs>>): Prisma__SearchResultClient<$Result.GetResult<Prisma.$SearchResultPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one SearchResult that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {SearchResultFindUniqueOrThrowArgs} args - Arguments to find a SearchResult
     * @example
     * // Get one SearchResult
     * const searchResult = await prisma.searchResult.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends SearchResultFindUniqueOrThrowArgs>(args: SelectSubset<T, SearchResultFindUniqueOrThrowArgs<ExtArgs>>): Prisma__SearchResultClient<$Result.GetResult<Prisma.$SearchResultPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first SearchResult that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {SearchResultFindFirstArgs} args - Arguments to find a SearchResult
     * @example
     * // Get one SearchResult
     * const searchResult = await prisma.searchResult.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends SearchResultFindFirstArgs>(args?: SelectSubset<T, SearchResultFindFirstArgs<ExtArgs>>): Prisma__SearchResultClient<$Result.GetResult<Prisma.$SearchResultPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first SearchResult that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {SearchResultFindFirstOrThrowArgs} args - Arguments to find a SearchResult
     * @example
     * // Get one SearchResult
     * const searchResult = await prisma.searchResult.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends SearchResultFindFirstOrThrowArgs>(args?: SelectSubset<T, SearchResultFindFirstOrThrowArgs<ExtArgs>>): Prisma__SearchResultClient<$Result.GetResult<Prisma.$SearchResultPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more SearchResults that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {SearchResultFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all SearchResults
     * const searchResults = await prisma.searchResult.findMany()
     * 
     * // Get first 10 SearchResults
     * const searchResults = await prisma.searchResult.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const searchResultWithIdOnly = await prisma.searchResult.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends SearchResultFindManyArgs>(args?: SelectSubset<T, SearchResultFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$SearchResultPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a SearchResult.
     * @param {SearchResultCreateArgs} args - Arguments to create a SearchResult.
     * @example
     * // Create one SearchResult
     * const SearchResult = await prisma.searchResult.create({
     *   data: {
     *     // ... data to create a SearchResult
     *   }
     * })
     * 
     */
    create<T extends SearchResultCreateArgs>(args: SelectSubset<T, SearchResultCreateArgs<ExtArgs>>): Prisma__SearchResultClient<$Result.GetResult<Prisma.$SearchResultPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many SearchResults.
     * @param {SearchResultCreateManyArgs} args - Arguments to create many SearchResults.
     * @example
     * // Create many SearchResults
     * const searchResult = await prisma.searchResult.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends SearchResultCreateManyArgs>(args?: SelectSubset<T, SearchResultCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many SearchResults and returns the data saved in the database.
     * @param {SearchResultCreateManyAndReturnArgs} args - Arguments to create many SearchResults.
     * @example
     * // Create many SearchResults
     * const searchResult = await prisma.searchResult.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many SearchResults and only return the `id`
     * const searchResultWithIdOnly = await prisma.searchResult.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends SearchResultCreateManyAndReturnArgs>(args?: SelectSubset<T, SearchResultCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$SearchResultPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a SearchResult.
     * @param {SearchResultDeleteArgs} args - Arguments to delete one SearchResult.
     * @example
     * // Delete one SearchResult
     * const SearchResult = await prisma.searchResult.delete({
     *   where: {
     *     // ... filter to delete one SearchResult
     *   }
     * })
     * 
     */
    delete<T extends SearchResultDeleteArgs>(args: SelectSubset<T, SearchResultDeleteArgs<ExtArgs>>): Prisma__SearchResultClient<$Result.GetResult<Prisma.$SearchResultPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one SearchResult.
     * @param {SearchResultUpdateArgs} args - Arguments to update one SearchResult.
     * @example
     * // Update one SearchResult
     * const searchResult = await prisma.searchResult.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends SearchResultUpdateArgs>(args: SelectSubset<T, SearchResultUpdateArgs<ExtArgs>>): Prisma__SearchResultClient<$Result.GetResult<Prisma.$SearchResultPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more SearchResults.
     * @param {SearchResultDeleteManyArgs} args - Arguments to filter SearchResults to delete.
     * @example
     * // Delete a few SearchResults
     * const { count } = await prisma.searchResult.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends SearchResultDeleteManyArgs>(args?: SelectSubset<T, SearchResultDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more SearchResults.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {SearchResultUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many SearchResults
     * const searchResult = await prisma.searchResult.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends SearchResultUpdateManyArgs>(args: SelectSubset<T, SearchResultUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more SearchResults and returns the data updated in the database.
     * @param {SearchResultUpdateManyAndReturnArgs} args - Arguments to update many SearchResults.
     * @example
     * // Update many SearchResults
     * const searchResult = await prisma.searchResult.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more SearchResults and only return the `id`
     * const searchResultWithIdOnly = await prisma.searchResult.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends SearchResultUpdateManyAndReturnArgs>(args: SelectSubset<T, SearchResultUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$SearchResultPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one SearchResult.
     * @param {SearchResultUpsertArgs} args - Arguments to update or create a SearchResult.
     * @example
     * // Update or create a SearchResult
     * const searchResult = await prisma.searchResult.upsert({
     *   create: {
     *     // ... data to create a SearchResult
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the SearchResult we want to update
     *   }
     * })
     */
    upsert<T extends SearchResultUpsertArgs>(args: SelectSubset<T, SearchResultUpsertArgs<ExtArgs>>): Prisma__SearchResultClient<$Result.GetResult<Prisma.$SearchResultPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of SearchResults.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {SearchResultCountArgs} args - Arguments to filter SearchResults to count.
     * @example
     * // Count the number of SearchResults
     * const count = await prisma.searchResult.count({
     *   where: {
     *     // ... the filter for the SearchResults we want to count
     *   }
     * })
    **/
    count<T extends SearchResultCountArgs>(
      args?: Subset<T, SearchResultCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], SearchResultCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a SearchResult.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {SearchResultAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends SearchResultAggregateArgs>(args: Subset<T, SearchResultAggregateArgs>): Prisma.PrismaPromise<GetSearchResultAggregateType<T>>

    /**
     * Group by SearchResult.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {SearchResultGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends SearchResultGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: SearchResultGroupByArgs['orderBy'] }
        : { orderBy?: SearchResultGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, SearchResultGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetSearchResultGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the SearchResult model
   */
  readonly fields: SearchResultFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for SearchResult.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__SearchResultClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    search<T extends SearchDefaultArgs<ExtArgs> = {}>(args?: Subset<T, SearchDefaultArgs<ExtArgs>>): Prisma__SearchClient<$Result.GetResult<Prisma.$SearchPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    carrier<T extends CarrierDefaultArgs<ExtArgs> = {}>(args?: Subset<T, CarrierDefaultArgs<ExtArgs>>): Prisma__CarrierClient<$Result.GetResult<Prisma.$CarrierPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    portRotations<T extends SearchResult$portRotationsArgs<ExtArgs> = {}>(args?: Subset<T, SearchResult$portRotationsArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$PortRotationPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the SearchResult model
   */
  interface SearchResultFieldRefs {
    readonly id: FieldRef<"SearchResult", 'String'>
    readonly searchId: FieldRef<"SearchResult", 'String'>
    readonly carrierId: FieldRef<"SearchResult", 'String'>
    readonly vesselName: FieldRef<"SearchResult", 'String'>
    readonly voyageNumber: FieldRef<"SearchResult", 'String'>
    readonly departureDate: FieldRef<"SearchResult", 'DateTime'>
    readonly arrivalDate: FieldRef<"SearchResult", 'DateTime'>
    readonly transitDays: FieldRef<"SearchResult", 'Int'>
    readonly rawApiResponse: FieldRef<"SearchResult", 'Json'>
    readonly createdAt: FieldRef<"SearchResult", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * SearchResult findUnique
   */
  export type SearchResultFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the SearchResult
     */
    select?: SearchResultSelect<ExtArgs> | null
    /**
     * Omit specific fields from the SearchResult
     */
    omit?: SearchResultOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SearchResultInclude<ExtArgs> | null
    /**
     * Filter, which SearchResult to fetch.
     */
    where: SearchResultWhereUniqueInput
  }

  /**
   * SearchResult findUniqueOrThrow
   */
  export type SearchResultFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the SearchResult
     */
    select?: SearchResultSelect<ExtArgs> | null
    /**
     * Omit specific fields from the SearchResult
     */
    omit?: SearchResultOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SearchResultInclude<ExtArgs> | null
    /**
     * Filter, which SearchResult to fetch.
     */
    where: SearchResultWhereUniqueInput
  }

  /**
   * SearchResult findFirst
   */
  export type SearchResultFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the SearchResult
     */
    select?: SearchResultSelect<ExtArgs> | null
    /**
     * Omit specific fields from the SearchResult
     */
    omit?: SearchResultOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SearchResultInclude<ExtArgs> | null
    /**
     * Filter, which SearchResult to fetch.
     */
    where?: SearchResultWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of SearchResults to fetch.
     */
    orderBy?: SearchResultOrderByWithRelationInput | SearchResultOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for SearchResults.
     */
    cursor?: SearchResultWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` SearchResults from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` SearchResults.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of SearchResults.
     */
    distinct?: SearchResultScalarFieldEnum | SearchResultScalarFieldEnum[]
  }

  /**
   * SearchResult findFirstOrThrow
   */
  export type SearchResultFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the SearchResult
     */
    select?: SearchResultSelect<ExtArgs> | null
    /**
     * Omit specific fields from the SearchResult
     */
    omit?: SearchResultOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SearchResultInclude<ExtArgs> | null
    /**
     * Filter, which SearchResult to fetch.
     */
    where?: SearchResultWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of SearchResults to fetch.
     */
    orderBy?: SearchResultOrderByWithRelationInput | SearchResultOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for SearchResults.
     */
    cursor?: SearchResultWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` SearchResults from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` SearchResults.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of SearchResults.
     */
    distinct?: SearchResultScalarFieldEnum | SearchResultScalarFieldEnum[]
  }

  /**
   * SearchResult findMany
   */
  export type SearchResultFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the SearchResult
     */
    select?: SearchResultSelect<ExtArgs> | null
    /**
     * Omit specific fields from the SearchResult
     */
    omit?: SearchResultOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SearchResultInclude<ExtArgs> | null
    /**
     * Filter, which SearchResults to fetch.
     */
    where?: SearchResultWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of SearchResults to fetch.
     */
    orderBy?: SearchResultOrderByWithRelationInput | SearchResultOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing SearchResults.
     */
    cursor?: SearchResultWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` SearchResults from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` SearchResults.
     */
    skip?: number
    distinct?: SearchResultScalarFieldEnum | SearchResultScalarFieldEnum[]
  }

  /**
   * SearchResult create
   */
  export type SearchResultCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the SearchResult
     */
    select?: SearchResultSelect<ExtArgs> | null
    /**
     * Omit specific fields from the SearchResult
     */
    omit?: SearchResultOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SearchResultInclude<ExtArgs> | null
    /**
     * The data needed to create a SearchResult.
     */
    data: XOR<SearchResultCreateInput, SearchResultUncheckedCreateInput>
  }

  /**
   * SearchResult createMany
   */
  export type SearchResultCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many SearchResults.
     */
    data: SearchResultCreateManyInput | SearchResultCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * SearchResult createManyAndReturn
   */
  export type SearchResultCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the SearchResult
     */
    select?: SearchResultSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the SearchResult
     */
    omit?: SearchResultOmit<ExtArgs> | null
    /**
     * The data used to create many SearchResults.
     */
    data: SearchResultCreateManyInput | SearchResultCreateManyInput[]
    skipDuplicates?: boolean
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SearchResultIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * SearchResult update
   */
  export type SearchResultUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the SearchResult
     */
    select?: SearchResultSelect<ExtArgs> | null
    /**
     * Omit specific fields from the SearchResult
     */
    omit?: SearchResultOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SearchResultInclude<ExtArgs> | null
    /**
     * The data needed to update a SearchResult.
     */
    data: XOR<SearchResultUpdateInput, SearchResultUncheckedUpdateInput>
    /**
     * Choose, which SearchResult to update.
     */
    where: SearchResultWhereUniqueInput
  }

  /**
   * SearchResult updateMany
   */
  export type SearchResultUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update SearchResults.
     */
    data: XOR<SearchResultUpdateManyMutationInput, SearchResultUncheckedUpdateManyInput>
    /**
     * Filter which SearchResults to update
     */
    where?: SearchResultWhereInput
    /**
     * Limit how many SearchResults to update.
     */
    limit?: number
  }

  /**
   * SearchResult updateManyAndReturn
   */
  export type SearchResultUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the SearchResult
     */
    select?: SearchResultSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the SearchResult
     */
    omit?: SearchResultOmit<ExtArgs> | null
    /**
     * The data used to update SearchResults.
     */
    data: XOR<SearchResultUpdateManyMutationInput, SearchResultUncheckedUpdateManyInput>
    /**
     * Filter which SearchResults to update
     */
    where?: SearchResultWhereInput
    /**
     * Limit how many SearchResults to update.
     */
    limit?: number
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SearchResultIncludeUpdateManyAndReturn<ExtArgs> | null
  }

  /**
   * SearchResult upsert
   */
  export type SearchResultUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the SearchResult
     */
    select?: SearchResultSelect<ExtArgs> | null
    /**
     * Omit specific fields from the SearchResult
     */
    omit?: SearchResultOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SearchResultInclude<ExtArgs> | null
    /**
     * The filter to search for the SearchResult to update in case it exists.
     */
    where: SearchResultWhereUniqueInput
    /**
     * In case the SearchResult found by the `where` argument doesn't exist, create a new SearchResult with this data.
     */
    create: XOR<SearchResultCreateInput, SearchResultUncheckedCreateInput>
    /**
     * In case the SearchResult was found with the provided `where` argument, update it with this data.
     */
    update: XOR<SearchResultUpdateInput, SearchResultUncheckedUpdateInput>
  }

  /**
   * SearchResult delete
   */
  export type SearchResultDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the SearchResult
     */
    select?: SearchResultSelect<ExtArgs> | null
    /**
     * Omit specific fields from the SearchResult
     */
    omit?: SearchResultOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SearchResultInclude<ExtArgs> | null
    /**
     * Filter which SearchResult to delete.
     */
    where: SearchResultWhereUniqueInput
  }

  /**
   * SearchResult deleteMany
   */
  export type SearchResultDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which SearchResults to delete
     */
    where?: SearchResultWhereInput
    /**
     * Limit how many SearchResults to delete.
     */
    limit?: number
  }

  /**
   * SearchResult.portRotations
   */
  export type SearchResult$portRotationsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the PortRotation
     */
    select?: PortRotationSelect<ExtArgs> | null
    /**
     * Omit specific fields from the PortRotation
     */
    omit?: PortRotationOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PortRotationInclude<ExtArgs> | null
    where?: PortRotationWhereInput
    orderBy?: PortRotationOrderByWithRelationInput | PortRotationOrderByWithRelationInput[]
    cursor?: PortRotationWhereUniqueInput
    take?: number
    skip?: number
    distinct?: PortRotationScalarFieldEnum | PortRotationScalarFieldEnum[]
  }

  /**
   * SearchResult without action
   */
  export type SearchResultDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the SearchResult
     */
    select?: SearchResultSelect<ExtArgs> | null
    /**
     * Omit specific fields from the SearchResult
     */
    omit?: SearchResultOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: SearchResultInclude<ExtArgs> | null
  }


  /**
   * Model PortRotation
   */

  export type AggregatePortRotation = {
    _count: PortRotationCountAggregateOutputType | null
    _avg: PortRotationAvgAggregateOutputType | null
    _sum: PortRotationSumAggregateOutputType | null
    _min: PortRotationMinAggregateOutputType | null
    _max: PortRotationMaxAggregateOutputType | null
  }

  export type PortRotationAvgAggregateOutputType = {
    sequenceOrder: number | null
  }

  export type PortRotationSumAggregateOutputType = {
    sequenceOrder: number | null
  }

  export type PortRotationMinAggregateOutputType = {
    id: string | null
    searchResultId: string | null
    portId: string | null
    sequenceOrder: number | null
    arrivalDate: Date | null
    departureDate: Date | null
  }

  export type PortRotationMaxAggregateOutputType = {
    id: string | null
    searchResultId: string | null
    portId: string | null
    sequenceOrder: number | null
    arrivalDate: Date | null
    departureDate: Date | null
  }

  export type PortRotationCountAggregateOutputType = {
    id: number
    searchResultId: number
    portId: number
    sequenceOrder: number
    arrivalDate: number
    departureDate: number
    _all: number
  }


  export type PortRotationAvgAggregateInputType = {
    sequenceOrder?: true
  }

  export type PortRotationSumAggregateInputType = {
    sequenceOrder?: true
  }

  export type PortRotationMinAggregateInputType = {
    id?: true
    searchResultId?: true
    portId?: true
    sequenceOrder?: true
    arrivalDate?: true
    departureDate?: true
  }

  export type PortRotationMaxAggregateInputType = {
    id?: true
    searchResultId?: true
    portId?: true
    sequenceOrder?: true
    arrivalDate?: true
    departureDate?: true
  }

  export type PortRotationCountAggregateInputType = {
    id?: true
    searchResultId?: true
    portId?: true
    sequenceOrder?: true
    arrivalDate?: true
    departureDate?: true
    _all?: true
  }

  export type PortRotationAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which PortRotation to aggregate.
     */
    where?: PortRotationWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of PortRotations to fetch.
     */
    orderBy?: PortRotationOrderByWithRelationInput | PortRotationOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: PortRotationWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` PortRotations from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` PortRotations.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned PortRotations
    **/
    _count?: true | PortRotationCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: PortRotationAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: PortRotationSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: PortRotationMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: PortRotationMaxAggregateInputType
  }

  export type GetPortRotationAggregateType<T extends PortRotationAggregateArgs> = {
        [P in keyof T & keyof AggregatePortRotation]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregatePortRotation[P]>
      : GetScalarType<T[P], AggregatePortRotation[P]>
  }




  export type PortRotationGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: PortRotationWhereInput
    orderBy?: PortRotationOrderByWithAggregationInput | PortRotationOrderByWithAggregationInput[]
    by: PortRotationScalarFieldEnum[] | PortRotationScalarFieldEnum
    having?: PortRotationScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: PortRotationCountAggregateInputType | true
    _avg?: PortRotationAvgAggregateInputType
    _sum?: PortRotationSumAggregateInputType
    _min?: PortRotationMinAggregateInputType
    _max?: PortRotationMaxAggregateInputType
  }

  export type PortRotationGroupByOutputType = {
    id: string
    searchResultId: string
    portId: string
    sequenceOrder: number
    arrivalDate: Date | null
    departureDate: Date | null
    _count: PortRotationCountAggregateOutputType | null
    _avg: PortRotationAvgAggregateOutputType | null
    _sum: PortRotationSumAggregateOutputType | null
    _min: PortRotationMinAggregateOutputType | null
    _max: PortRotationMaxAggregateOutputType | null
  }

  type GetPortRotationGroupByPayload<T extends PortRotationGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<PortRotationGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof PortRotationGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], PortRotationGroupByOutputType[P]>
            : GetScalarType<T[P], PortRotationGroupByOutputType[P]>
        }
      >
    >


  export type PortRotationSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    searchResultId?: boolean
    portId?: boolean
    sequenceOrder?: boolean
    arrivalDate?: boolean
    departureDate?: boolean
    searchResult?: boolean | SearchResultDefaultArgs<ExtArgs>
    port?: boolean | PortDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["portRotation"]>

  export type PortRotationSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    searchResultId?: boolean
    portId?: boolean
    sequenceOrder?: boolean
    arrivalDate?: boolean
    departureDate?: boolean
    searchResult?: boolean | SearchResultDefaultArgs<ExtArgs>
    port?: boolean | PortDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["portRotation"]>

  export type PortRotationSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    searchResultId?: boolean
    portId?: boolean
    sequenceOrder?: boolean
    arrivalDate?: boolean
    departureDate?: boolean
    searchResult?: boolean | SearchResultDefaultArgs<ExtArgs>
    port?: boolean | PortDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["portRotation"]>

  export type PortRotationSelectScalar = {
    id?: boolean
    searchResultId?: boolean
    portId?: boolean
    sequenceOrder?: boolean
    arrivalDate?: boolean
    departureDate?: boolean
  }

  export type PortRotationOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "searchResultId" | "portId" | "sequenceOrder" | "arrivalDate" | "departureDate", ExtArgs["result"]["portRotation"]>
  export type PortRotationInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    searchResult?: boolean | SearchResultDefaultArgs<ExtArgs>
    port?: boolean | PortDefaultArgs<ExtArgs>
  }
  export type PortRotationIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    searchResult?: boolean | SearchResultDefaultArgs<ExtArgs>
    port?: boolean | PortDefaultArgs<ExtArgs>
  }
  export type PortRotationIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    searchResult?: boolean | SearchResultDefaultArgs<ExtArgs>
    port?: boolean | PortDefaultArgs<ExtArgs>
  }

  export type $PortRotationPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "PortRotation"
    objects: {
      searchResult: Prisma.$SearchResultPayload<ExtArgs>
      port: Prisma.$PortPayload<ExtArgs>
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      searchResultId: string
      portId: string
      sequenceOrder: number
      arrivalDate: Date | null
      departureDate: Date | null
    }, ExtArgs["result"]["portRotation"]>
    composites: {}
  }

  type PortRotationGetPayload<S extends boolean | null | undefined | PortRotationDefaultArgs> = $Result.GetResult<Prisma.$PortRotationPayload, S>

  type PortRotationCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<PortRotationFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: PortRotationCountAggregateInputType | true
    }

  export interface PortRotationDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['PortRotation'], meta: { name: 'PortRotation' } }
    /**
     * Find zero or one PortRotation that matches the filter.
     * @param {PortRotationFindUniqueArgs} args - Arguments to find a PortRotation
     * @example
     * // Get one PortRotation
     * const portRotation = await prisma.portRotation.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends PortRotationFindUniqueArgs>(args: SelectSubset<T, PortRotationFindUniqueArgs<ExtArgs>>): Prisma__PortRotationClient<$Result.GetResult<Prisma.$PortRotationPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one PortRotation that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {PortRotationFindUniqueOrThrowArgs} args - Arguments to find a PortRotation
     * @example
     * // Get one PortRotation
     * const portRotation = await prisma.portRotation.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends PortRotationFindUniqueOrThrowArgs>(args: SelectSubset<T, PortRotationFindUniqueOrThrowArgs<ExtArgs>>): Prisma__PortRotationClient<$Result.GetResult<Prisma.$PortRotationPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first PortRotation that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {PortRotationFindFirstArgs} args - Arguments to find a PortRotation
     * @example
     * // Get one PortRotation
     * const portRotation = await prisma.portRotation.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends PortRotationFindFirstArgs>(args?: SelectSubset<T, PortRotationFindFirstArgs<ExtArgs>>): Prisma__PortRotationClient<$Result.GetResult<Prisma.$PortRotationPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first PortRotation that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {PortRotationFindFirstOrThrowArgs} args - Arguments to find a PortRotation
     * @example
     * // Get one PortRotation
     * const portRotation = await prisma.portRotation.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends PortRotationFindFirstOrThrowArgs>(args?: SelectSubset<T, PortRotationFindFirstOrThrowArgs<ExtArgs>>): Prisma__PortRotationClient<$Result.GetResult<Prisma.$PortRotationPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more PortRotations that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {PortRotationFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all PortRotations
     * const portRotations = await prisma.portRotation.findMany()
     * 
     * // Get first 10 PortRotations
     * const portRotations = await prisma.portRotation.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const portRotationWithIdOnly = await prisma.portRotation.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends PortRotationFindManyArgs>(args?: SelectSubset<T, PortRotationFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$PortRotationPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a PortRotation.
     * @param {PortRotationCreateArgs} args - Arguments to create a PortRotation.
     * @example
     * // Create one PortRotation
     * const PortRotation = await prisma.portRotation.create({
     *   data: {
     *     // ... data to create a PortRotation
     *   }
     * })
     * 
     */
    create<T extends PortRotationCreateArgs>(args: SelectSubset<T, PortRotationCreateArgs<ExtArgs>>): Prisma__PortRotationClient<$Result.GetResult<Prisma.$PortRotationPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many PortRotations.
     * @param {PortRotationCreateManyArgs} args - Arguments to create many PortRotations.
     * @example
     * // Create many PortRotations
     * const portRotation = await prisma.portRotation.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends PortRotationCreateManyArgs>(args?: SelectSubset<T, PortRotationCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many PortRotations and returns the data saved in the database.
     * @param {PortRotationCreateManyAndReturnArgs} args - Arguments to create many PortRotations.
     * @example
     * // Create many PortRotations
     * const portRotation = await prisma.portRotation.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many PortRotations and only return the `id`
     * const portRotationWithIdOnly = await prisma.portRotation.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends PortRotationCreateManyAndReturnArgs>(args?: SelectSubset<T, PortRotationCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$PortRotationPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a PortRotation.
     * @param {PortRotationDeleteArgs} args - Arguments to delete one PortRotation.
     * @example
     * // Delete one PortRotation
     * const PortRotation = await prisma.portRotation.delete({
     *   where: {
     *     // ... filter to delete one PortRotation
     *   }
     * })
     * 
     */
    delete<T extends PortRotationDeleteArgs>(args: SelectSubset<T, PortRotationDeleteArgs<ExtArgs>>): Prisma__PortRotationClient<$Result.GetResult<Prisma.$PortRotationPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one PortRotation.
     * @param {PortRotationUpdateArgs} args - Arguments to update one PortRotation.
     * @example
     * // Update one PortRotation
     * const portRotation = await prisma.portRotation.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends PortRotationUpdateArgs>(args: SelectSubset<T, PortRotationUpdateArgs<ExtArgs>>): Prisma__PortRotationClient<$Result.GetResult<Prisma.$PortRotationPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more PortRotations.
     * @param {PortRotationDeleteManyArgs} args - Arguments to filter PortRotations to delete.
     * @example
     * // Delete a few PortRotations
     * const { count } = await prisma.portRotation.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends PortRotationDeleteManyArgs>(args?: SelectSubset<T, PortRotationDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more PortRotations.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {PortRotationUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many PortRotations
     * const portRotation = await prisma.portRotation.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends PortRotationUpdateManyArgs>(args: SelectSubset<T, PortRotationUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more PortRotations and returns the data updated in the database.
     * @param {PortRotationUpdateManyAndReturnArgs} args - Arguments to update many PortRotations.
     * @example
     * // Update many PortRotations
     * const portRotation = await prisma.portRotation.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more PortRotations and only return the `id`
     * const portRotationWithIdOnly = await prisma.portRotation.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends PortRotationUpdateManyAndReturnArgs>(args: SelectSubset<T, PortRotationUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$PortRotationPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one PortRotation.
     * @param {PortRotationUpsertArgs} args - Arguments to update or create a PortRotation.
     * @example
     * // Update or create a PortRotation
     * const portRotation = await prisma.portRotation.upsert({
     *   create: {
     *     // ... data to create a PortRotation
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the PortRotation we want to update
     *   }
     * })
     */
    upsert<T extends PortRotationUpsertArgs>(args: SelectSubset<T, PortRotationUpsertArgs<ExtArgs>>): Prisma__PortRotationClient<$Result.GetResult<Prisma.$PortRotationPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of PortRotations.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {PortRotationCountArgs} args - Arguments to filter PortRotations to count.
     * @example
     * // Count the number of PortRotations
     * const count = await prisma.portRotation.count({
     *   where: {
     *     // ... the filter for the PortRotations we want to count
     *   }
     * })
    **/
    count<T extends PortRotationCountArgs>(
      args?: Subset<T, PortRotationCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], PortRotationCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a PortRotation.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {PortRotationAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends PortRotationAggregateArgs>(args: Subset<T, PortRotationAggregateArgs>): Prisma.PrismaPromise<GetPortRotationAggregateType<T>>

    /**
     * Group by PortRotation.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {PortRotationGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends PortRotationGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: PortRotationGroupByArgs['orderBy'] }
        : { orderBy?: PortRotationGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, PortRotationGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetPortRotationGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the PortRotation model
   */
  readonly fields: PortRotationFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for PortRotation.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__PortRotationClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    searchResult<T extends SearchResultDefaultArgs<ExtArgs> = {}>(args?: Subset<T, SearchResultDefaultArgs<ExtArgs>>): Prisma__SearchResultClient<$Result.GetResult<Prisma.$SearchResultPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    port<T extends PortDefaultArgs<ExtArgs> = {}>(args?: Subset<T, PortDefaultArgs<ExtArgs>>): Prisma__PortClient<$Result.GetResult<Prisma.$PortPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the PortRotation model
   */
  interface PortRotationFieldRefs {
    readonly id: FieldRef<"PortRotation", 'String'>
    readonly searchResultId: FieldRef<"PortRotation", 'String'>
    readonly portId: FieldRef<"PortRotation", 'String'>
    readonly sequenceOrder: FieldRef<"PortRotation", 'Int'>
    readonly arrivalDate: FieldRef<"PortRotation", 'DateTime'>
    readonly departureDate: FieldRef<"PortRotation", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * PortRotation findUnique
   */
  export type PortRotationFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the PortRotation
     */
    select?: PortRotationSelect<ExtArgs> | null
    /**
     * Omit specific fields from the PortRotation
     */
    omit?: PortRotationOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PortRotationInclude<ExtArgs> | null
    /**
     * Filter, which PortRotation to fetch.
     */
    where: PortRotationWhereUniqueInput
  }

  /**
   * PortRotation findUniqueOrThrow
   */
  export type PortRotationFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the PortRotation
     */
    select?: PortRotationSelect<ExtArgs> | null
    /**
     * Omit specific fields from the PortRotation
     */
    omit?: PortRotationOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PortRotationInclude<ExtArgs> | null
    /**
     * Filter, which PortRotation to fetch.
     */
    where: PortRotationWhereUniqueInput
  }

  /**
   * PortRotation findFirst
   */
  export type PortRotationFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the PortRotation
     */
    select?: PortRotationSelect<ExtArgs> | null
    /**
     * Omit specific fields from the PortRotation
     */
    omit?: PortRotationOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PortRotationInclude<ExtArgs> | null
    /**
     * Filter, which PortRotation to fetch.
     */
    where?: PortRotationWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of PortRotations to fetch.
     */
    orderBy?: PortRotationOrderByWithRelationInput | PortRotationOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for PortRotations.
     */
    cursor?: PortRotationWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` PortRotations from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` PortRotations.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of PortRotations.
     */
    distinct?: PortRotationScalarFieldEnum | PortRotationScalarFieldEnum[]
  }

  /**
   * PortRotation findFirstOrThrow
   */
  export type PortRotationFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the PortRotation
     */
    select?: PortRotationSelect<ExtArgs> | null
    /**
     * Omit specific fields from the PortRotation
     */
    omit?: PortRotationOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PortRotationInclude<ExtArgs> | null
    /**
     * Filter, which PortRotation to fetch.
     */
    where?: PortRotationWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of PortRotations to fetch.
     */
    orderBy?: PortRotationOrderByWithRelationInput | PortRotationOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for PortRotations.
     */
    cursor?: PortRotationWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` PortRotations from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` PortRotations.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of PortRotations.
     */
    distinct?: PortRotationScalarFieldEnum | PortRotationScalarFieldEnum[]
  }

  /**
   * PortRotation findMany
   */
  export type PortRotationFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the PortRotation
     */
    select?: PortRotationSelect<ExtArgs> | null
    /**
     * Omit specific fields from the PortRotation
     */
    omit?: PortRotationOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PortRotationInclude<ExtArgs> | null
    /**
     * Filter, which PortRotations to fetch.
     */
    where?: PortRotationWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of PortRotations to fetch.
     */
    orderBy?: PortRotationOrderByWithRelationInput | PortRotationOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing PortRotations.
     */
    cursor?: PortRotationWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` PortRotations from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` PortRotations.
     */
    skip?: number
    distinct?: PortRotationScalarFieldEnum | PortRotationScalarFieldEnum[]
  }

  /**
   * PortRotation create
   */
  export type PortRotationCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the PortRotation
     */
    select?: PortRotationSelect<ExtArgs> | null
    /**
     * Omit specific fields from the PortRotation
     */
    omit?: PortRotationOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PortRotationInclude<ExtArgs> | null
    /**
     * The data needed to create a PortRotation.
     */
    data: XOR<PortRotationCreateInput, PortRotationUncheckedCreateInput>
  }

  /**
   * PortRotation createMany
   */
  export type PortRotationCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many PortRotations.
     */
    data: PortRotationCreateManyInput | PortRotationCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * PortRotation createManyAndReturn
   */
  export type PortRotationCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the PortRotation
     */
    select?: PortRotationSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the PortRotation
     */
    omit?: PortRotationOmit<ExtArgs> | null
    /**
     * The data used to create many PortRotations.
     */
    data: PortRotationCreateManyInput | PortRotationCreateManyInput[]
    skipDuplicates?: boolean
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PortRotationIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * PortRotation update
   */
  export type PortRotationUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the PortRotation
     */
    select?: PortRotationSelect<ExtArgs> | null
    /**
     * Omit specific fields from the PortRotation
     */
    omit?: PortRotationOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PortRotationInclude<ExtArgs> | null
    /**
     * The data needed to update a PortRotation.
     */
    data: XOR<PortRotationUpdateInput, PortRotationUncheckedUpdateInput>
    /**
     * Choose, which PortRotation to update.
     */
    where: PortRotationWhereUniqueInput
  }

  /**
   * PortRotation updateMany
   */
  export type PortRotationUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update PortRotations.
     */
    data: XOR<PortRotationUpdateManyMutationInput, PortRotationUncheckedUpdateManyInput>
    /**
     * Filter which PortRotations to update
     */
    where?: PortRotationWhereInput
    /**
     * Limit how many PortRotations to update.
     */
    limit?: number
  }

  /**
   * PortRotation updateManyAndReturn
   */
  export type PortRotationUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the PortRotation
     */
    select?: PortRotationSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the PortRotation
     */
    omit?: PortRotationOmit<ExtArgs> | null
    /**
     * The data used to update PortRotations.
     */
    data: XOR<PortRotationUpdateManyMutationInput, PortRotationUncheckedUpdateManyInput>
    /**
     * Filter which PortRotations to update
     */
    where?: PortRotationWhereInput
    /**
     * Limit how many PortRotations to update.
     */
    limit?: number
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PortRotationIncludeUpdateManyAndReturn<ExtArgs> | null
  }

  /**
   * PortRotation upsert
   */
  export type PortRotationUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the PortRotation
     */
    select?: PortRotationSelect<ExtArgs> | null
    /**
     * Omit specific fields from the PortRotation
     */
    omit?: PortRotationOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PortRotationInclude<ExtArgs> | null
    /**
     * The filter to search for the PortRotation to update in case it exists.
     */
    where: PortRotationWhereUniqueInput
    /**
     * In case the PortRotation found by the `where` argument doesn't exist, create a new PortRotation with this data.
     */
    create: XOR<PortRotationCreateInput, PortRotationUncheckedCreateInput>
    /**
     * In case the PortRotation was found with the provided `where` argument, update it with this data.
     */
    update: XOR<PortRotationUpdateInput, PortRotationUncheckedUpdateInput>
  }

  /**
   * PortRotation delete
   */
  export type PortRotationDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the PortRotation
     */
    select?: PortRotationSelect<ExtArgs> | null
    /**
     * Omit specific fields from the PortRotation
     */
    omit?: PortRotationOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PortRotationInclude<ExtArgs> | null
    /**
     * Filter which PortRotation to delete.
     */
    where: PortRotationWhereUniqueInput
  }

  /**
   * PortRotation deleteMany
   */
  export type PortRotationDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which PortRotations to delete
     */
    where?: PortRotationWhereInput
    /**
     * Limit how many PortRotations to delete.
     */
    limit?: number
  }

  /**
   * PortRotation without action
   */
  export type PortRotationDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the PortRotation
     */
    select?: PortRotationSelect<ExtArgs> | null
    /**
     * Omit specific fields from the PortRotation
     */
    omit?: PortRotationOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PortRotationInclude<ExtArgs> | null
  }


  /**
   * Enums
   */

  export const TransactionIsolationLevel: {
    ReadUncommitted: 'ReadUncommitted',
    ReadCommitted: 'ReadCommitted',
    RepeatableRead: 'RepeatableRead',
    Serializable: 'Serializable'
  };

  export type TransactionIsolationLevel = (typeof TransactionIsolationLevel)[keyof typeof TransactionIsolationLevel]


  export const UserScalarFieldEnum: {
    id: 'id',
    email: 'email',
    passwordHash: 'passwordHash',
    firstName: 'firstName',
    lastName: 'lastName',
    companyName: 'companyName',
    isActive: 'isActive',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt'
  };

  export type UserScalarFieldEnum = (typeof UserScalarFieldEnum)[keyof typeof UserScalarFieldEnum]


  export const PortScalarFieldEnum: {
    id: 'id',
    code: 'code',
    name: 'name',
    countryCode: 'countryCode',
    countryName: 'countryName',
    latitude: 'latitude',
    longitude: 'longitude',
    isActive: 'isActive',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt'
  };

  export type PortScalarFieldEnum = (typeof PortScalarFieldEnum)[keyof typeof PortScalarFieldEnum]


  export const CarrierScalarFieldEnum: {
    id: 'id',
    name: 'name',
    code: 'code',
    apiEndpoint: 'apiEndpoint',
    apiKey: 'apiKey',
    isActive: 'isActive',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt'
  };

  export type CarrierScalarFieldEnum = (typeof CarrierScalarFieldEnum)[keyof typeof CarrierScalarFieldEnum]


  export const SearchScalarFieldEnum: {
    id: 'id',
    userId: 'userId',
    originPortId: 'originPortId',
    destinationPortId: 'destinationPortId',
    departureDate: 'departureDate',
    searchTimestamp: 'searchTimestamp',
    resultsCount: 'resultsCount',
    searchDurationMs: 'searchDurationMs'
  };

  export type SearchScalarFieldEnum = (typeof SearchScalarFieldEnum)[keyof typeof SearchScalarFieldEnum]


  export const SearchResultScalarFieldEnum: {
    id: 'id',
    searchId: 'searchId',
    carrierId: 'carrierId',
    vesselName: 'vesselName',
    voyageNumber: 'voyageNumber',
    departureDate: 'departureDate',
    arrivalDate: 'arrivalDate',
    transitDays: 'transitDays',
    rawApiResponse: 'rawApiResponse',
    createdAt: 'createdAt'
  };

  export type SearchResultScalarFieldEnum = (typeof SearchResultScalarFieldEnum)[keyof typeof SearchResultScalarFieldEnum]


  export const PortRotationScalarFieldEnum: {
    id: 'id',
    searchResultId: 'searchResultId',
    portId: 'portId',
    sequenceOrder: 'sequenceOrder',
    arrivalDate: 'arrivalDate',
    departureDate: 'departureDate'
  };

  export type PortRotationScalarFieldEnum = (typeof PortRotationScalarFieldEnum)[keyof typeof PortRotationScalarFieldEnum]


  export const SortOrder: {
    asc: 'asc',
    desc: 'desc'
  };

  export type SortOrder = (typeof SortOrder)[keyof typeof SortOrder]


  export const NullableJsonNullValueInput: {
    DbNull: typeof DbNull,
    JsonNull: typeof JsonNull
  };

  export type NullableJsonNullValueInput = (typeof NullableJsonNullValueInput)[keyof typeof NullableJsonNullValueInput]


  export const QueryMode: {
    default: 'default',
    insensitive: 'insensitive'
  };

  export type QueryMode = (typeof QueryMode)[keyof typeof QueryMode]


  export const NullsOrder: {
    first: 'first',
    last: 'last'
  };

  export type NullsOrder = (typeof NullsOrder)[keyof typeof NullsOrder]


  export const JsonNullValueFilter: {
    DbNull: typeof DbNull,
    JsonNull: typeof JsonNull,
    AnyNull: typeof AnyNull
  };

  export type JsonNullValueFilter = (typeof JsonNullValueFilter)[keyof typeof JsonNullValueFilter]


  /**
   * Field references
   */


  /**
   * Reference to a field of type 'String'
   */
  export type StringFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'String'>
    


  /**
   * Reference to a field of type 'String[]'
   */
  export type ListStringFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'String[]'>
    


  /**
   * Reference to a field of type 'Boolean'
   */
  export type BooleanFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Boolean'>
    


  /**
   * Reference to a field of type 'DateTime'
   */
  export type DateTimeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'DateTime'>
    


  /**
   * Reference to a field of type 'DateTime[]'
   */
  export type ListDateTimeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'DateTime[]'>
    


  /**
   * Reference to a field of type 'Float'
   */
  export type FloatFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Float'>
    


  /**
   * Reference to a field of type 'Float[]'
   */
  export type ListFloatFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Float[]'>
    


  /**
   * Reference to a field of type 'Int'
   */
  export type IntFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Int'>
    


  /**
   * Reference to a field of type 'Int[]'
   */
  export type ListIntFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Int[]'>
    


  /**
   * Reference to a field of type 'Json'
   */
  export type JsonFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Json'>
    


  /**
   * Reference to a field of type 'QueryMode'
   */
  export type EnumQueryModeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'QueryMode'>
    
  /**
   * Deep Input Types
   */


  export type UserWhereInput = {
    AND?: UserWhereInput | UserWhereInput[]
    OR?: UserWhereInput[]
    NOT?: UserWhereInput | UserWhereInput[]
    id?: StringFilter<"User"> | string
    email?: StringFilter<"User"> | string
    passwordHash?: StringFilter<"User"> | string
    firstName?: StringFilter<"User"> | string
    lastName?: StringFilter<"User"> | string
    companyName?: StringNullableFilter<"User"> | string | null
    isActive?: BoolFilter<"User"> | boolean
    createdAt?: DateTimeFilter<"User"> | Date | string
    updatedAt?: DateTimeFilter<"User"> | Date | string
    searches?: SearchListRelationFilter
  }

  export type UserOrderByWithRelationInput = {
    id?: SortOrder
    email?: SortOrder
    passwordHash?: SortOrder
    firstName?: SortOrder
    lastName?: SortOrder
    companyName?: SortOrderInput | SortOrder
    isActive?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    searches?: SearchOrderByRelationAggregateInput
  }

  export type UserWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    email?: string
    AND?: UserWhereInput | UserWhereInput[]
    OR?: UserWhereInput[]
    NOT?: UserWhereInput | UserWhereInput[]
    passwordHash?: StringFilter<"User"> | string
    firstName?: StringFilter<"User"> | string
    lastName?: StringFilter<"User"> | string
    companyName?: StringNullableFilter<"User"> | string | null
    isActive?: BoolFilter<"User"> | boolean
    createdAt?: DateTimeFilter<"User"> | Date | string
    updatedAt?: DateTimeFilter<"User"> | Date | string
    searches?: SearchListRelationFilter
  }, "id" | "email">

  export type UserOrderByWithAggregationInput = {
    id?: SortOrder
    email?: SortOrder
    passwordHash?: SortOrder
    firstName?: SortOrder
    lastName?: SortOrder
    companyName?: SortOrderInput | SortOrder
    isActive?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    _count?: UserCountOrderByAggregateInput
    _max?: UserMaxOrderByAggregateInput
    _min?: UserMinOrderByAggregateInput
  }

  export type UserScalarWhereWithAggregatesInput = {
    AND?: UserScalarWhereWithAggregatesInput | UserScalarWhereWithAggregatesInput[]
    OR?: UserScalarWhereWithAggregatesInput[]
    NOT?: UserScalarWhereWithAggregatesInput | UserScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"User"> | string
    email?: StringWithAggregatesFilter<"User"> | string
    passwordHash?: StringWithAggregatesFilter<"User"> | string
    firstName?: StringWithAggregatesFilter<"User"> | string
    lastName?: StringWithAggregatesFilter<"User"> | string
    companyName?: StringNullableWithAggregatesFilter<"User"> | string | null
    isActive?: BoolWithAggregatesFilter<"User"> | boolean
    createdAt?: DateTimeWithAggregatesFilter<"User"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"User"> | Date | string
  }

  export type PortWhereInput = {
    AND?: PortWhereInput | PortWhereInput[]
    OR?: PortWhereInput[]
    NOT?: PortWhereInput | PortWhereInput[]
    id?: StringFilter<"Port"> | string
    code?: StringFilter<"Port"> | string
    name?: StringFilter<"Port"> | string
    countryCode?: StringFilter<"Port"> | string
    countryName?: StringFilter<"Port"> | string
    latitude?: FloatNullableFilter<"Port"> | number | null
    longitude?: FloatNullableFilter<"Port"> | number | null
    isActive?: BoolFilter<"Port"> | boolean
    createdAt?: DateTimeFilter<"Port"> | Date | string
    updatedAt?: DateTimeFilter<"Port"> | Date | string
    originSearches?: SearchListRelationFilter
    destinationSearches?: SearchListRelationFilter
    portRotations?: PortRotationListRelationFilter
  }

  export type PortOrderByWithRelationInput = {
    id?: SortOrder
    code?: SortOrder
    name?: SortOrder
    countryCode?: SortOrder
    countryName?: SortOrder
    latitude?: SortOrderInput | SortOrder
    longitude?: SortOrderInput | SortOrder
    isActive?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    originSearches?: SearchOrderByRelationAggregateInput
    destinationSearches?: SearchOrderByRelationAggregateInput
    portRotations?: PortRotationOrderByRelationAggregateInput
  }

  export type PortWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    code?: string
    AND?: PortWhereInput | PortWhereInput[]
    OR?: PortWhereInput[]
    NOT?: PortWhereInput | PortWhereInput[]
    name?: StringFilter<"Port"> | string
    countryCode?: StringFilter<"Port"> | string
    countryName?: StringFilter<"Port"> | string
    latitude?: FloatNullableFilter<"Port"> | number | null
    longitude?: FloatNullableFilter<"Port"> | number | null
    isActive?: BoolFilter<"Port"> | boolean
    createdAt?: DateTimeFilter<"Port"> | Date | string
    updatedAt?: DateTimeFilter<"Port"> | Date | string
    originSearches?: SearchListRelationFilter
    destinationSearches?: SearchListRelationFilter
    portRotations?: PortRotationListRelationFilter
  }, "id" | "code">

  export type PortOrderByWithAggregationInput = {
    id?: SortOrder
    code?: SortOrder
    name?: SortOrder
    countryCode?: SortOrder
    countryName?: SortOrder
    latitude?: SortOrderInput | SortOrder
    longitude?: SortOrderInput | SortOrder
    isActive?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    _count?: PortCountOrderByAggregateInput
    _avg?: PortAvgOrderByAggregateInput
    _max?: PortMaxOrderByAggregateInput
    _min?: PortMinOrderByAggregateInput
    _sum?: PortSumOrderByAggregateInput
  }

  export type PortScalarWhereWithAggregatesInput = {
    AND?: PortScalarWhereWithAggregatesInput | PortScalarWhereWithAggregatesInput[]
    OR?: PortScalarWhereWithAggregatesInput[]
    NOT?: PortScalarWhereWithAggregatesInput | PortScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"Port"> | string
    code?: StringWithAggregatesFilter<"Port"> | string
    name?: StringWithAggregatesFilter<"Port"> | string
    countryCode?: StringWithAggregatesFilter<"Port"> | string
    countryName?: StringWithAggregatesFilter<"Port"> | string
    latitude?: FloatNullableWithAggregatesFilter<"Port"> | number | null
    longitude?: FloatNullableWithAggregatesFilter<"Port"> | number | null
    isActive?: BoolWithAggregatesFilter<"Port"> | boolean
    createdAt?: DateTimeWithAggregatesFilter<"Port"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"Port"> | Date | string
  }

  export type CarrierWhereInput = {
    AND?: CarrierWhereInput | CarrierWhereInput[]
    OR?: CarrierWhereInput[]
    NOT?: CarrierWhereInput | CarrierWhereInput[]
    id?: StringFilter<"Carrier"> | string
    name?: StringFilter<"Carrier"> | string
    code?: StringFilter<"Carrier"> | string
    apiEndpoint?: StringNullableFilter<"Carrier"> | string | null
    apiKey?: StringNullableFilter<"Carrier"> | string | null
    isActive?: BoolFilter<"Carrier"> | boolean
    createdAt?: DateTimeFilter<"Carrier"> | Date | string
    updatedAt?: DateTimeFilter<"Carrier"> | Date | string
    searchResults?: SearchResultListRelationFilter
  }

  export type CarrierOrderByWithRelationInput = {
    id?: SortOrder
    name?: SortOrder
    code?: SortOrder
    apiEndpoint?: SortOrderInput | SortOrder
    apiKey?: SortOrderInput | SortOrder
    isActive?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    searchResults?: SearchResultOrderByRelationAggregateInput
  }

  export type CarrierWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    code?: string
    AND?: CarrierWhereInput | CarrierWhereInput[]
    OR?: CarrierWhereInput[]
    NOT?: CarrierWhereInput | CarrierWhereInput[]
    name?: StringFilter<"Carrier"> | string
    apiEndpoint?: StringNullableFilter<"Carrier"> | string | null
    apiKey?: StringNullableFilter<"Carrier"> | string | null
    isActive?: BoolFilter<"Carrier"> | boolean
    createdAt?: DateTimeFilter<"Carrier"> | Date | string
    updatedAt?: DateTimeFilter<"Carrier"> | Date | string
    searchResults?: SearchResultListRelationFilter
  }, "id" | "code">

  export type CarrierOrderByWithAggregationInput = {
    id?: SortOrder
    name?: SortOrder
    code?: SortOrder
    apiEndpoint?: SortOrderInput | SortOrder
    apiKey?: SortOrderInput | SortOrder
    isActive?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    _count?: CarrierCountOrderByAggregateInput
    _max?: CarrierMaxOrderByAggregateInput
    _min?: CarrierMinOrderByAggregateInput
  }

  export type CarrierScalarWhereWithAggregatesInput = {
    AND?: CarrierScalarWhereWithAggregatesInput | CarrierScalarWhereWithAggregatesInput[]
    OR?: CarrierScalarWhereWithAggregatesInput[]
    NOT?: CarrierScalarWhereWithAggregatesInput | CarrierScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"Carrier"> | string
    name?: StringWithAggregatesFilter<"Carrier"> | string
    code?: StringWithAggregatesFilter<"Carrier"> | string
    apiEndpoint?: StringNullableWithAggregatesFilter<"Carrier"> | string | null
    apiKey?: StringNullableWithAggregatesFilter<"Carrier"> | string | null
    isActive?: BoolWithAggregatesFilter<"Carrier"> | boolean
    createdAt?: DateTimeWithAggregatesFilter<"Carrier"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"Carrier"> | Date | string
  }

  export type SearchWhereInput = {
    AND?: SearchWhereInput | SearchWhereInput[]
    OR?: SearchWhereInput[]
    NOT?: SearchWhereInput | SearchWhereInput[]
    id?: StringFilter<"Search"> | string
    userId?: StringFilter<"Search"> | string
    originPortId?: StringFilter<"Search"> | string
    destinationPortId?: StringFilter<"Search"> | string
    departureDate?: DateTimeFilter<"Search"> | Date | string
    searchTimestamp?: DateTimeFilter<"Search"> | Date | string
    resultsCount?: IntFilter<"Search"> | number
    searchDurationMs?: IntNullableFilter<"Search"> | number | null
    user?: XOR<UserScalarRelationFilter, UserWhereInput>
    originPort?: XOR<PortScalarRelationFilter, PortWhereInput>
    destinationPort?: XOR<PortScalarRelationFilter, PortWhereInput>
    searchResults?: SearchResultListRelationFilter
  }

  export type SearchOrderByWithRelationInput = {
    id?: SortOrder
    userId?: SortOrder
    originPortId?: SortOrder
    destinationPortId?: SortOrder
    departureDate?: SortOrder
    searchTimestamp?: SortOrder
    resultsCount?: SortOrder
    searchDurationMs?: SortOrderInput | SortOrder
    user?: UserOrderByWithRelationInput
    originPort?: PortOrderByWithRelationInput
    destinationPort?: PortOrderByWithRelationInput
    searchResults?: SearchResultOrderByRelationAggregateInput
  }

  export type SearchWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    AND?: SearchWhereInput | SearchWhereInput[]
    OR?: SearchWhereInput[]
    NOT?: SearchWhereInput | SearchWhereInput[]
    userId?: StringFilter<"Search"> | string
    originPortId?: StringFilter<"Search"> | string
    destinationPortId?: StringFilter<"Search"> | string
    departureDate?: DateTimeFilter<"Search"> | Date | string
    searchTimestamp?: DateTimeFilter<"Search"> | Date | string
    resultsCount?: IntFilter<"Search"> | number
    searchDurationMs?: IntNullableFilter<"Search"> | number | null
    user?: XOR<UserScalarRelationFilter, UserWhereInput>
    originPort?: XOR<PortScalarRelationFilter, PortWhereInput>
    destinationPort?: XOR<PortScalarRelationFilter, PortWhereInput>
    searchResults?: SearchResultListRelationFilter
  }, "id">

  export type SearchOrderByWithAggregationInput = {
    id?: SortOrder
    userId?: SortOrder
    originPortId?: SortOrder
    destinationPortId?: SortOrder
    departureDate?: SortOrder
    searchTimestamp?: SortOrder
    resultsCount?: SortOrder
    searchDurationMs?: SortOrderInput | SortOrder
    _count?: SearchCountOrderByAggregateInput
    _avg?: SearchAvgOrderByAggregateInput
    _max?: SearchMaxOrderByAggregateInput
    _min?: SearchMinOrderByAggregateInput
    _sum?: SearchSumOrderByAggregateInput
  }

  export type SearchScalarWhereWithAggregatesInput = {
    AND?: SearchScalarWhereWithAggregatesInput | SearchScalarWhereWithAggregatesInput[]
    OR?: SearchScalarWhereWithAggregatesInput[]
    NOT?: SearchScalarWhereWithAggregatesInput | SearchScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"Search"> | string
    userId?: StringWithAggregatesFilter<"Search"> | string
    originPortId?: StringWithAggregatesFilter<"Search"> | string
    destinationPortId?: StringWithAggregatesFilter<"Search"> | string
    departureDate?: DateTimeWithAggregatesFilter<"Search"> | Date | string
    searchTimestamp?: DateTimeWithAggregatesFilter<"Search"> | Date | string
    resultsCount?: IntWithAggregatesFilter<"Search"> | number
    searchDurationMs?: IntNullableWithAggregatesFilter<"Search"> | number | null
  }

  export type SearchResultWhereInput = {
    AND?: SearchResultWhereInput | SearchResultWhereInput[]
    OR?: SearchResultWhereInput[]
    NOT?: SearchResultWhereInput | SearchResultWhereInput[]
    id?: StringFilter<"SearchResult"> | string
    searchId?: StringFilter<"SearchResult"> | string
    carrierId?: StringFilter<"SearchResult"> | string
    vesselName?: StringFilter<"SearchResult"> | string
    voyageNumber?: StringNullableFilter<"SearchResult"> | string | null
    departureDate?: DateTimeFilter<"SearchResult"> | Date | string
    arrivalDate?: DateTimeFilter<"SearchResult"> | Date | string
    transitDays?: IntFilter<"SearchResult"> | number
    rawApiResponse?: JsonNullableFilter<"SearchResult">
    createdAt?: DateTimeFilter<"SearchResult"> | Date | string
    search?: XOR<SearchScalarRelationFilter, SearchWhereInput>
    carrier?: XOR<CarrierScalarRelationFilter, CarrierWhereInput>
    portRotations?: PortRotationListRelationFilter
  }

  export type SearchResultOrderByWithRelationInput = {
    id?: SortOrder
    searchId?: SortOrder
    carrierId?: SortOrder
    vesselName?: SortOrder
    voyageNumber?: SortOrderInput | SortOrder
    departureDate?: SortOrder
    arrivalDate?: SortOrder
    transitDays?: SortOrder
    rawApiResponse?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    search?: SearchOrderByWithRelationInput
    carrier?: CarrierOrderByWithRelationInput
    portRotations?: PortRotationOrderByRelationAggregateInput
  }

  export type SearchResultWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    AND?: SearchResultWhereInput | SearchResultWhereInput[]
    OR?: SearchResultWhereInput[]
    NOT?: SearchResultWhereInput | SearchResultWhereInput[]
    searchId?: StringFilter<"SearchResult"> | string
    carrierId?: StringFilter<"SearchResult"> | string
    vesselName?: StringFilter<"SearchResult"> | string
    voyageNumber?: StringNullableFilter<"SearchResult"> | string | null
    departureDate?: DateTimeFilter<"SearchResult"> | Date | string
    arrivalDate?: DateTimeFilter<"SearchResult"> | Date | string
    transitDays?: IntFilter<"SearchResult"> | number
    rawApiResponse?: JsonNullableFilter<"SearchResult">
    createdAt?: DateTimeFilter<"SearchResult"> | Date | string
    search?: XOR<SearchScalarRelationFilter, SearchWhereInput>
    carrier?: XOR<CarrierScalarRelationFilter, CarrierWhereInput>
    portRotations?: PortRotationListRelationFilter
  }, "id">

  export type SearchResultOrderByWithAggregationInput = {
    id?: SortOrder
    searchId?: SortOrder
    carrierId?: SortOrder
    vesselName?: SortOrder
    voyageNumber?: SortOrderInput | SortOrder
    departureDate?: SortOrder
    arrivalDate?: SortOrder
    transitDays?: SortOrder
    rawApiResponse?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    _count?: SearchResultCountOrderByAggregateInput
    _avg?: SearchResultAvgOrderByAggregateInput
    _max?: SearchResultMaxOrderByAggregateInput
    _min?: SearchResultMinOrderByAggregateInput
    _sum?: SearchResultSumOrderByAggregateInput
  }

  export type SearchResultScalarWhereWithAggregatesInput = {
    AND?: SearchResultScalarWhereWithAggregatesInput | SearchResultScalarWhereWithAggregatesInput[]
    OR?: SearchResultScalarWhereWithAggregatesInput[]
    NOT?: SearchResultScalarWhereWithAggregatesInput | SearchResultScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"SearchResult"> | string
    searchId?: StringWithAggregatesFilter<"SearchResult"> | string
    carrierId?: StringWithAggregatesFilter<"SearchResult"> | string
    vesselName?: StringWithAggregatesFilter<"SearchResult"> | string
    voyageNumber?: StringNullableWithAggregatesFilter<"SearchResult"> | string | null
    departureDate?: DateTimeWithAggregatesFilter<"SearchResult"> | Date | string
    arrivalDate?: DateTimeWithAggregatesFilter<"SearchResult"> | Date | string
    transitDays?: IntWithAggregatesFilter<"SearchResult"> | number
    rawApiResponse?: JsonNullableWithAggregatesFilter<"SearchResult">
    createdAt?: DateTimeWithAggregatesFilter<"SearchResult"> | Date | string
  }

  export type PortRotationWhereInput = {
    AND?: PortRotationWhereInput | PortRotationWhereInput[]
    OR?: PortRotationWhereInput[]
    NOT?: PortRotationWhereInput | PortRotationWhereInput[]
    id?: StringFilter<"PortRotation"> | string
    searchResultId?: StringFilter<"PortRotation"> | string
    portId?: StringFilter<"PortRotation"> | string
    sequenceOrder?: IntFilter<"PortRotation"> | number
    arrivalDate?: DateTimeNullableFilter<"PortRotation"> | Date | string | null
    departureDate?: DateTimeNullableFilter<"PortRotation"> | Date | string | null
    searchResult?: XOR<SearchResultScalarRelationFilter, SearchResultWhereInput>
    port?: XOR<PortScalarRelationFilter, PortWhereInput>
  }

  export type PortRotationOrderByWithRelationInput = {
    id?: SortOrder
    searchResultId?: SortOrder
    portId?: SortOrder
    sequenceOrder?: SortOrder
    arrivalDate?: SortOrderInput | SortOrder
    departureDate?: SortOrderInput | SortOrder
    searchResult?: SearchResultOrderByWithRelationInput
    port?: PortOrderByWithRelationInput
  }

  export type PortRotationWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    searchResultId_sequenceOrder?: PortRotationSearchResultIdSequenceOrderCompoundUniqueInput
    AND?: PortRotationWhereInput | PortRotationWhereInput[]
    OR?: PortRotationWhereInput[]
    NOT?: PortRotationWhereInput | PortRotationWhereInput[]
    searchResultId?: StringFilter<"PortRotation"> | string
    portId?: StringFilter<"PortRotation"> | string
    sequenceOrder?: IntFilter<"PortRotation"> | number
    arrivalDate?: DateTimeNullableFilter<"PortRotation"> | Date | string | null
    departureDate?: DateTimeNullableFilter<"PortRotation"> | Date | string | null
    searchResult?: XOR<SearchResultScalarRelationFilter, SearchResultWhereInput>
    port?: XOR<PortScalarRelationFilter, PortWhereInput>
  }, "id" | "searchResultId_sequenceOrder">

  export type PortRotationOrderByWithAggregationInput = {
    id?: SortOrder
    searchResultId?: SortOrder
    portId?: SortOrder
    sequenceOrder?: SortOrder
    arrivalDate?: SortOrderInput | SortOrder
    departureDate?: SortOrderInput | SortOrder
    _count?: PortRotationCountOrderByAggregateInput
    _avg?: PortRotationAvgOrderByAggregateInput
    _max?: PortRotationMaxOrderByAggregateInput
    _min?: PortRotationMinOrderByAggregateInput
    _sum?: PortRotationSumOrderByAggregateInput
  }

  export type PortRotationScalarWhereWithAggregatesInput = {
    AND?: PortRotationScalarWhereWithAggregatesInput | PortRotationScalarWhereWithAggregatesInput[]
    OR?: PortRotationScalarWhereWithAggregatesInput[]
    NOT?: PortRotationScalarWhereWithAggregatesInput | PortRotationScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"PortRotation"> | string
    searchResultId?: StringWithAggregatesFilter<"PortRotation"> | string
    portId?: StringWithAggregatesFilter<"PortRotation"> | string
    sequenceOrder?: IntWithAggregatesFilter<"PortRotation"> | number
    arrivalDate?: DateTimeNullableWithAggregatesFilter<"PortRotation"> | Date | string | null
    departureDate?: DateTimeNullableWithAggregatesFilter<"PortRotation"> | Date | string | null
  }

  export type UserCreateInput = {
    id?: string
    email: string
    passwordHash: string
    firstName: string
    lastName: string
    companyName?: string | null
    isActive?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    searches?: SearchCreateNestedManyWithoutUserInput
  }

  export type UserUncheckedCreateInput = {
    id?: string
    email: string
    passwordHash: string
    firstName: string
    lastName: string
    companyName?: string | null
    isActive?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    searches?: SearchUncheckedCreateNestedManyWithoutUserInput
  }

  export type UserUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    passwordHash?: StringFieldUpdateOperationsInput | string
    firstName?: StringFieldUpdateOperationsInput | string
    lastName?: StringFieldUpdateOperationsInput | string
    companyName?: NullableStringFieldUpdateOperationsInput | string | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    searches?: SearchUpdateManyWithoutUserNestedInput
  }

  export type UserUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    passwordHash?: StringFieldUpdateOperationsInput | string
    firstName?: StringFieldUpdateOperationsInput | string
    lastName?: StringFieldUpdateOperationsInput | string
    companyName?: NullableStringFieldUpdateOperationsInput | string | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    searches?: SearchUncheckedUpdateManyWithoutUserNestedInput
  }

  export type UserCreateManyInput = {
    id?: string
    email: string
    passwordHash: string
    firstName: string
    lastName: string
    companyName?: string | null
    isActive?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type UserUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    passwordHash?: StringFieldUpdateOperationsInput | string
    firstName?: StringFieldUpdateOperationsInput | string
    lastName?: StringFieldUpdateOperationsInput | string
    companyName?: NullableStringFieldUpdateOperationsInput | string | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type UserUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    passwordHash?: StringFieldUpdateOperationsInput | string
    firstName?: StringFieldUpdateOperationsInput | string
    lastName?: StringFieldUpdateOperationsInput | string
    companyName?: NullableStringFieldUpdateOperationsInput | string | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type PortCreateInput = {
    id?: string
    code: string
    name: string
    countryCode: string
    countryName: string
    latitude?: number | null
    longitude?: number | null
    isActive?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    originSearches?: SearchCreateNestedManyWithoutOriginPortInput
    destinationSearches?: SearchCreateNestedManyWithoutDestinationPortInput
    portRotations?: PortRotationCreateNestedManyWithoutPortInput
  }

  export type PortUncheckedCreateInput = {
    id?: string
    code: string
    name: string
    countryCode: string
    countryName: string
    latitude?: number | null
    longitude?: number | null
    isActive?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    originSearches?: SearchUncheckedCreateNestedManyWithoutOriginPortInput
    destinationSearches?: SearchUncheckedCreateNestedManyWithoutDestinationPortInput
    portRotations?: PortRotationUncheckedCreateNestedManyWithoutPortInput
  }

  export type PortUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    code?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    countryCode?: StringFieldUpdateOperationsInput | string
    countryName?: StringFieldUpdateOperationsInput | string
    latitude?: NullableFloatFieldUpdateOperationsInput | number | null
    longitude?: NullableFloatFieldUpdateOperationsInput | number | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    originSearches?: SearchUpdateManyWithoutOriginPortNestedInput
    destinationSearches?: SearchUpdateManyWithoutDestinationPortNestedInput
    portRotations?: PortRotationUpdateManyWithoutPortNestedInput
  }

  export type PortUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    code?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    countryCode?: StringFieldUpdateOperationsInput | string
    countryName?: StringFieldUpdateOperationsInput | string
    latitude?: NullableFloatFieldUpdateOperationsInput | number | null
    longitude?: NullableFloatFieldUpdateOperationsInput | number | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    originSearches?: SearchUncheckedUpdateManyWithoutOriginPortNestedInput
    destinationSearches?: SearchUncheckedUpdateManyWithoutDestinationPortNestedInput
    portRotations?: PortRotationUncheckedUpdateManyWithoutPortNestedInput
  }

  export type PortCreateManyInput = {
    id?: string
    code: string
    name: string
    countryCode: string
    countryName: string
    latitude?: number | null
    longitude?: number | null
    isActive?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type PortUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    code?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    countryCode?: StringFieldUpdateOperationsInput | string
    countryName?: StringFieldUpdateOperationsInput | string
    latitude?: NullableFloatFieldUpdateOperationsInput | number | null
    longitude?: NullableFloatFieldUpdateOperationsInput | number | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type PortUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    code?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    countryCode?: StringFieldUpdateOperationsInput | string
    countryName?: StringFieldUpdateOperationsInput | string
    latitude?: NullableFloatFieldUpdateOperationsInput | number | null
    longitude?: NullableFloatFieldUpdateOperationsInput | number | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type CarrierCreateInput = {
    id?: string
    name: string
    code: string
    apiEndpoint?: string | null
    apiKey?: string | null
    isActive?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    searchResults?: SearchResultCreateNestedManyWithoutCarrierInput
  }

  export type CarrierUncheckedCreateInput = {
    id?: string
    name: string
    code: string
    apiEndpoint?: string | null
    apiKey?: string | null
    isActive?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    searchResults?: SearchResultUncheckedCreateNestedManyWithoutCarrierInput
  }

  export type CarrierUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    code?: StringFieldUpdateOperationsInput | string
    apiEndpoint?: NullableStringFieldUpdateOperationsInput | string | null
    apiKey?: NullableStringFieldUpdateOperationsInput | string | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    searchResults?: SearchResultUpdateManyWithoutCarrierNestedInput
  }

  export type CarrierUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    code?: StringFieldUpdateOperationsInput | string
    apiEndpoint?: NullableStringFieldUpdateOperationsInput | string | null
    apiKey?: NullableStringFieldUpdateOperationsInput | string | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    searchResults?: SearchResultUncheckedUpdateManyWithoutCarrierNestedInput
  }

  export type CarrierCreateManyInput = {
    id?: string
    name: string
    code: string
    apiEndpoint?: string | null
    apiKey?: string | null
    isActive?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type CarrierUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    code?: StringFieldUpdateOperationsInput | string
    apiEndpoint?: NullableStringFieldUpdateOperationsInput | string | null
    apiKey?: NullableStringFieldUpdateOperationsInput | string | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type CarrierUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    code?: StringFieldUpdateOperationsInput | string
    apiEndpoint?: NullableStringFieldUpdateOperationsInput | string | null
    apiKey?: NullableStringFieldUpdateOperationsInput | string | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type SearchCreateInput = {
    id?: string
    departureDate: Date | string
    searchTimestamp?: Date | string
    resultsCount?: number
    searchDurationMs?: number | null
    user: UserCreateNestedOneWithoutSearchesInput
    originPort: PortCreateNestedOneWithoutOriginSearchesInput
    destinationPort: PortCreateNestedOneWithoutDestinationSearchesInput
    searchResults?: SearchResultCreateNestedManyWithoutSearchInput
  }

  export type SearchUncheckedCreateInput = {
    id?: string
    userId: string
    originPortId: string
    destinationPortId: string
    departureDate: Date | string
    searchTimestamp?: Date | string
    resultsCount?: number
    searchDurationMs?: number | null
    searchResults?: SearchResultUncheckedCreateNestedManyWithoutSearchInput
  }

  export type SearchUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    departureDate?: DateTimeFieldUpdateOperationsInput | Date | string
    searchTimestamp?: DateTimeFieldUpdateOperationsInput | Date | string
    resultsCount?: IntFieldUpdateOperationsInput | number
    searchDurationMs?: NullableIntFieldUpdateOperationsInput | number | null
    user?: UserUpdateOneRequiredWithoutSearchesNestedInput
    originPort?: PortUpdateOneRequiredWithoutOriginSearchesNestedInput
    destinationPort?: PortUpdateOneRequiredWithoutDestinationSearchesNestedInput
    searchResults?: SearchResultUpdateManyWithoutSearchNestedInput
  }

  export type SearchUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    originPortId?: StringFieldUpdateOperationsInput | string
    destinationPortId?: StringFieldUpdateOperationsInput | string
    departureDate?: DateTimeFieldUpdateOperationsInput | Date | string
    searchTimestamp?: DateTimeFieldUpdateOperationsInput | Date | string
    resultsCount?: IntFieldUpdateOperationsInput | number
    searchDurationMs?: NullableIntFieldUpdateOperationsInput | number | null
    searchResults?: SearchResultUncheckedUpdateManyWithoutSearchNestedInput
  }

  export type SearchCreateManyInput = {
    id?: string
    userId: string
    originPortId: string
    destinationPortId: string
    departureDate: Date | string
    searchTimestamp?: Date | string
    resultsCount?: number
    searchDurationMs?: number | null
  }

  export type SearchUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    departureDate?: DateTimeFieldUpdateOperationsInput | Date | string
    searchTimestamp?: DateTimeFieldUpdateOperationsInput | Date | string
    resultsCount?: IntFieldUpdateOperationsInput | number
    searchDurationMs?: NullableIntFieldUpdateOperationsInput | number | null
  }

  export type SearchUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    originPortId?: StringFieldUpdateOperationsInput | string
    destinationPortId?: StringFieldUpdateOperationsInput | string
    departureDate?: DateTimeFieldUpdateOperationsInput | Date | string
    searchTimestamp?: DateTimeFieldUpdateOperationsInput | Date | string
    resultsCount?: IntFieldUpdateOperationsInput | number
    searchDurationMs?: NullableIntFieldUpdateOperationsInput | number | null
  }

  export type SearchResultCreateInput = {
    id?: string
    vesselName: string
    voyageNumber?: string | null
    departureDate: Date | string
    arrivalDate: Date | string
    transitDays: number
    rawApiResponse?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: Date | string
    search: SearchCreateNestedOneWithoutSearchResultsInput
    carrier: CarrierCreateNestedOneWithoutSearchResultsInput
    portRotations?: PortRotationCreateNestedManyWithoutSearchResultInput
  }

  export type SearchResultUncheckedCreateInput = {
    id?: string
    searchId: string
    carrierId: string
    vesselName: string
    voyageNumber?: string | null
    departureDate: Date | string
    arrivalDate: Date | string
    transitDays: number
    rawApiResponse?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: Date | string
    portRotations?: PortRotationUncheckedCreateNestedManyWithoutSearchResultInput
  }

  export type SearchResultUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    vesselName?: StringFieldUpdateOperationsInput | string
    voyageNumber?: NullableStringFieldUpdateOperationsInput | string | null
    departureDate?: DateTimeFieldUpdateOperationsInput | Date | string
    arrivalDate?: DateTimeFieldUpdateOperationsInput | Date | string
    transitDays?: IntFieldUpdateOperationsInput | number
    rawApiResponse?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    search?: SearchUpdateOneRequiredWithoutSearchResultsNestedInput
    carrier?: CarrierUpdateOneRequiredWithoutSearchResultsNestedInput
    portRotations?: PortRotationUpdateManyWithoutSearchResultNestedInput
  }

  export type SearchResultUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    searchId?: StringFieldUpdateOperationsInput | string
    carrierId?: StringFieldUpdateOperationsInput | string
    vesselName?: StringFieldUpdateOperationsInput | string
    voyageNumber?: NullableStringFieldUpdateOperationsInput | string | null
    departureDate?: DateTimeFieldUpdateOperationsInput | Date | string
    arrivalDate?: DateTimeFieldUpdateOperationsInput | Date | string
    transitDays?: IntFieldUpdateOperationsInput | number
    rawApiResponse?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    portRotations?: PortRotationUncheckedUpdateManyWithoutSearchResultNestedInput
  }

  export type SearchResultCreateManyInput = {
    id?: string
    searchId: string
    carrierId: string
    vesselName: string
    voyageNumber?: string | null
    departureDate: Date | string
    arrivalDate: Date | string
    transitDays: number
    rawApiResponse?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: Date | string
  }

  export type SearchResultUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    vesselName?: StringFieldUpdateOperationsInput | string
    voyageNumber?: NullableStringFieldUpdateOperationsInput | string | null
    departureDate?: DateTimeFieldUpdateOperationsInput | Date | string
    arrivalDate?: DateTimeFieldUpdateOperationsInput | Date | string
    transitDays?: IntFieldUpdateOperationsInput | number
    rawApiResponse?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type SearchResultUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    searchId?: StringFieldUpdateOperationsInput | string
    carrierId?: StringFieldUpdateOperationsInput | string
    vesselName?: StringFieldUpdateOperationsInput | string
    voyageNumber?: NullableStringFieldUpdateOperationsInput | string | null
    departureDate?: DateTimeFieldUpdateOperationsInput | Date | string
    arrivalDate?: DateTimeFieldUpdateOperationsInput | Date | string
    transitDays?: IntFieldUpdateOperationsInput | number
    rawApiResponse?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type PortRotationCreateInput = {
    id?: string
    sequenceOrder: number
    arrivalDate?: Date | string | null
    departureDate?: Date | string | null
    searchResult: SearchResultCreateNestedOneWithoutPortRotationsInput
    port: PortCreateNestedOneWithoutPortRotationsInput
  }

  export type PortRotationUncheckedCreateInput = {
    id?: string
    searchResultId: string
    portId: string
    sequenceOrder: number
    arrivalDate?: Date | string | null
    departureDate?: Date | string | null
  }

  export type PortRotationUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    sequenceOrder?: IntFieldUpdateOperationsInput | number
    arrivalDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    departureDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    searchResult?: SearchResultUpdateOneRequiredWithoutPortRotationsNestedInput
    port?: PortUpdateOneRequiredWithoutPortRotationsNestedInput
  }

  export type PortRotationUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    searchResultId?: StringFieldUpdateOperationsInput | string
    portId?: StringFieldUpdateOperationsInput | string
    sequenceOrder?: IntFieldUpdateOperationsInput | number
    arrivalDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    departureDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  }

  export type PortRotationCreateManyInput = {
    id?: string
    searchResultId: string
    portId: string
    sequenceOrder: number
    arrivalDate?: Date | string | null
    departureDate?: Date | string | null
  }

  export type PortRotationUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    sequenceOrder?: IntFieldUpdateOperationsInput | number
    arrivalDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    departureDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  }

  export type PortRotationUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    searchResultId?: StringFieldUpdateOperationsInput | string
    portId?: StringFieldUpdateOperationsInput | string
    sequenceOrder?: IntFieldUpdateOperationsInput | number
    arrivalDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    departureDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  }

  export type StringFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringFilter<$PrismaModel> | string
  }

  export type StringNullableFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringNullableFilter<$PrismaModel> | string | null
  }

  export type BoolFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolFilter<$PrismaModel> | boolean
  }

  export type DateTimeFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeFilter<$PrismaModel> | Date | string
  }

  export type SearchListRelationFilter = {
    every?: SearchWhereInput
    some?: SearchWhereInput
    none?: SearchWhereInput
  }

  export type SortOrderInput = {
    sort: SortOrder
    nulls?: NullsOrder
  }

  export type SearchOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type UserCountOrderByAggregateInput = {
    id?: SortOrder
    email?: SortOrder
    passwordHash?: SortOrder
    firstName?: SortOrder
    lastName?: SortOrder
    companyName?: SortOrder
    isActive?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type UserMaxOrderByAggregateInput = {
    id?: SortOrder
    email?: SortOrder
    passwordHash?: SortOrder
    firstName?: SortOrder
    lastName?: SortOrder
    companyName?: SortOrder
    isActive?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type UserMinOrderByAggregateInput = {
    id?: SortOrder
    email?: SortOrder
    passwordHash?: SortOrder
    firstName?: SortOrder
    lastName?: SortOrder
    companyName?: SortOrder
    isActive?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type StringWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringWithAggregatesFilter<$PrismaModel> | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedStringFilter<$PrismaModel>
    _max?: NestedStringFilter<$PrismaModel>
  }

  export type StringNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringNullableWithAggregatesFilter<$PrismaModel> | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedStringNullableFilter<$PrismaModel>
    _max?: NestedStringNullableFilter<$PrismaModel>
  }

  export type BoolWithAggregatesFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolWithAggregatesFilter<$PrismaModel> | boolean
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedBoolFilter<$PrismaModel>
    _max?: NestedBoolFilter<$PrismaModel>
  }

  export type DateTimeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeWithAggregatesFilter<$PrismaModel> | Date | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedDateTimeFilter<$PrismaModel>
    _max?: NestedDateTimeFilter<$PrismaModel>
  }

  export type FloatNullableFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel> | null
    in?: number[] | ListFloatFieldRefInput<$PrismaModel> | null
    notIn?: number[] | ListFloatFieldRefInput<$PrismaModel> | null
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatNullableFilter<$PrismaModel> | number | null
  }

  export type PortRotationListRelationFilter = {
    every?: PortRotationWhereInput
    some?: PortRotationWhereInput
    none?: PortRotationWhereInput
  }

  export type PortRotationOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type PortCountOrderByAggregateInput = {
    id?: SortOrder
    code?: SortOrder
    name?: SortOrder
    countryCode?: SortOrder
    countryName?: SortOrder
    latitude?: SortOrder
    longitude?: SortOrder
    isActive?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type PortAvgOrderByAggregateInput = {
    latitude?: SortOrder
    longitude?: SortOrder
  }

  export type PortMaxOrderByAggregateInput = {
    id?: SortOrder
    code?: SortOrder
    name?: SortOrder
    countryCode?: SortOrder
    countryName?: SortOrder
    latitude?: SortOrder
    longitude?: SortOrder
    isActive?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type PortMinOrderByAggregateInput = {
    id?: SortOrder
    code?: SortOrder
    name?: SortOrder
    countryCode?: SortOrder
    countryName?: SortOrder
    latitude?: SortOrder
    longitude?: SortOrder
    isActive?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type PortSumOrderByAggregateInput = {
    latitude?: SortOrder
    longitude?: SortOrder
  }

  export type FloatNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel> | null
    in?: number[] | ListFloatFieldRefInput<$PrismaModel> | null
    notIn?: number[] | ListFloatFieldRefInput<$PrismaModel> | null
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatNullableWithAggregatesFilter<$PrismaModel> | number | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _avg?: NestedFloatNullableFilter<$PrismaModel>
    _sum?: NestedFloatNullableFilter<$PrismaModel>
    _min?: NestedFloatNullableFilter<$PrismaModel>
    _max?: NestedFloatNullableFilter<$PrismaModel>
  }

  export type SearchResultListRelationFilter = {
    every?: SearchResultWhereInput
    some?: SearchResultWhereInput
    none?: SearchResultWhereInput
  }

  export type SearchResultOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type CarrierCountOrderByAggregateInput = {
    id?: SortOrder
    name?: SortOrder
    code?: SortOrder
    apiEndpoint?: SortOrder
    apiKey?: SortOrder
    isActive?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type CarrierMaxOrderByAggregateInput = {
    id?: SortOrder
    name?: SortOrder
    code?: SortOrder
    apiEndpoint?: SortOrder
    apiKey?: SortOrder
    isActive?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type CarrierMinOrderByAggregateInput = {
    id?: SortOrder
    name?: SortOrder
    code?: SortOrder
    apiEndpoint?: SortOrder
    apiKey?: SortOrder
    isActive?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type IntFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[] | ListIntFieldRefInput<$PrismaModel>
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel>
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntFilter<$PrismaModel> | number
  }

  export type IntNullableFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableFilter<$PrismaModel> | number | null
  }

  export type UserScalarRelationFilter = {
    is?: UserWhereInput
    isNot?: UserWhereInput
  }

  export type PortScalarRelationFilter = {
    is?: PortWhereInput
    isNot?: PortWhereInput
  }

  export type SearchCountOrderByAggregateInput = {
    id?: SortOrder
    userId?: SortOrder
    originPortId?: SortOrder
    destinationPortId?: SortOrder
    departureDate?: SortOrder
    searchTimestamp?: SortOrder
    resultsCount?: SortOrder
    searchDurationMs?: SortOrder
  }

  export type SearchAvgOrderByAggregateInput = {
    resultsCount?: SortOrder
    searchDurationMs?: SortOrder
  }

  export type SearchMaxOrderByAggregateInput = {
    id?: SortOrder
    userId?: SortOrder
    originPortId?: SortOrder
    destinationPortId?: SortOrder
    departureDate?: SortOrder
    searchTimestamp?: SortOrder
    resultsCount?: SortOrder
    searchDurationMs?: SortOrder
  }

  export type SearchMinOrderByAggregateInput = {
    id?: SortOrder
    userId?: SortOrder
    originPortId?: SortOrder
    destinationPortId?: SortOrder
    departureDate?: SortOrder
    searchTimestamp?: SortOrder
    resultsCount?: SortOrder
    searchDurationMs?: SortOrder
  }

  export type SearchSumOrderByAggregateInput = {
    resultsCount?: SortOrder
    searchDurationMs?: SortOrder
  }

  export type IntWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[] | ListIntFieldRefInput<$PrismaModel>
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel>
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntWithAggregatesFilter<$PrismaModel> | number
    _count?: NestedIntFilter<$PrismaModel>
    _avg?: NestedFloatFilter<$PrismaModel>
    _sum?: NestedIntFilter<$PrismaModel>
    _min?: NestedIntFilter<$PrismaModel>
    _max?: NestedIntFilter<$PrismaModel>
  }

  export type IntNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableWithAggregatesFilter<$PrismaModel> | number | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _avg?: NestedFloatNullableFilter<$PrismaModel>
    _sum?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedIntNullableFilter<$PrismaModel>
    _max?: NestedIntNullableFilter<$PrismaModel>
  }
  export type JsonNullableFilter<$PrismaModel = never> =
    | PatchUndefined<
        Either<Required<JsonNullableFilterBase<$PrismaModel>>, Exclude<keyof Required<JsonNullableFilterBase<$PrismaModel>>, 'path'>>,
        Required<JsonNullableFilterBase<$PrismaModel>>
      >
    | OptionalFlat<Omit<Required<JsonNullableFilterBase<$PrismaModel>>, 'path'>>

  export type JsonNullableFilterBase<$PrismaModel = never> = {
    equals?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    path?: string[]
    mode?: QueryMode | EnumQueryModeFieldRefInput<$PrismaModel>
    string_contains?: string | StringFieldRefInput<$PrismaModel>
    string_starts_with?: string | StringFieldRefInput<$PrismaModel>
    string_ends_with?: string | StringFieldRefInput<$PrismaModel>
    array_starts_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_ends_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_contains?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    lt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    lte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    not?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
  }

  export type SearchScalarRelationFilter = {
    is?: SearchWhereInput
    isNot?: SearchWhereInput
  }

  export type CarrierScalarRelationFilter = {
    is?: CarrierWhereInput
    isNot?: CarrierWhereInput
  }

  export type SearchResultCountOrderByAggregateInput = {
    id?: SortOrder
    searchId?: SortOrder
    carrierId?: SortOrder
    vesselName?: SortOrder
    voyageNumber?: SortOrder
    departureDate?: SortOrder
    arrivalDate?: SortOrder
    transitDays?: SortOrder
    rawApiResponse?: SortOrder
    createdAt?: SortOrder
  }

  export type SearchResultAvgOrderByAggregateInput = {
    transitDays?: SortOrder
  }

  export type SearchResultMaxOrderByAggregateInput = {
    id?: SortOrder
    searchId?: SortOrder
    carrierId?: SortOrder
    vesselName?: SortOrder
    voyageNumber?: SortOrder
    departureDate?: SortOrder
    arrivalDate?: SortOrder
    transitDays?: SortOrder
    createdAt?: SortOrder
  }

  export type SearchResultMinOrderByAggregateInput = {
    id?: SortOrder
    searchId?: SortOrder
    carrierId?: SortOrder
    vesselName?: SortOrder
    voyageNumber?: SortOrder
    departureDate?: SortOrder
    arrivalDate?: SortOrder
    transitDays?: SortOrder
    createdAt?: SortOrder
  }

  export type SearchResultSumOrderByAggregateInput = {
    transitDays?: SortOrder
  }
  export type JsonNullableWithAggregatesFilter<$PrismaModel = never> =
    | PatchUndefined<
        Either<Required<JsonNullableWithAggregatesFilterBase<$PrismaModel>>, Exclude<keyof Required<JsonNullableWithAggregatesFilterBase<$PrismaModel>>, 'path'>>,
        Required<JsonNullableWithAggregatesFilterBase<$PrismaModel>>
      >
    | OptionalFlat<Omit<Required<JsonNullableWithAggregatesFilterBase<$PrismaModel>>, 'path'>>

  export type JsonNullableWithAggregatesFilterBase<$PrismaModel = never> = {
    equals?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    path?: string[]
    mode?: QueryMode | EnumQueryModeFieldRefInput<$PrismaModel>
    string_contains?: string | StringFieldRefInput<$PrismaModel>
    string_starts_with?: string | StringFieldRefInput<$PrismaModel>
    string_ends_with?: string | StringFieldRefInput<$PrismaModel>
    array_starts_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_ends_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_contains?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    lt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    lte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    not?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedJsonNullableFilter<$PrismaModel>
    _max?: NestedJsonNullableFilter<$PrismaModel>
  }

  export type DateTimeNullableFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableFilter<$PrismaModel> | Date | string | null
  }

  export type SearchResultScalarRelationFilter = {
    is?: SearchResultWhereInput
    isNot?: SearchResultWhereInput
  }

  export type PortRotationSearchResultIdSequenceOrderCompoundUniqueInput = {
    searchResultId: string
    sequenceOrder: number
  }

  export type PortRotationCountOrderByAggregateInput = {
    id?: SortOrder
    searchResultId?: SortOrder
    portId?: SortOrder
    sequenceOrder?: SortOrder
    arrivalDate?: SortOrder
    departureDate?: SortOrder
  }

  export type PortRotationAvgOrderByAggregateInput = {
    sequenceOrder?: SortOrder
  }

  export type PortRotationMaxOrderByAggregateInput = {
    id?: SortOrder
    searchResultId?: SortOrder
    portId?: SortOrder
    sequenceOrder?: SortOrder
    arrivalDate?: SortOrder
    departureDate?: SortOrder
  }

  export type PortRotationMinOrderByAggregateInput = {
    id?: SortOrder
    searchResultId?: SortOrder
    portId?: SortOrder
    sequenceOrder?: SortOrder
    arrivalDate?: SortOrder
    departureDate?: SortOrder
  }

  export type PortRotationSumOrderByAggregateInput = {
    sequenceOrder?: SortOrder
  }

  export type DateTimeNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableWithAggregatesFilter<$PrismaModel> | Date | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedDateTimeNullableFilter<$PrismaModel>
    _max?: NestedDateTimeNullableFilter<$PrismaModel>
  }

  export type SearchCreateNestedManyWithoutUserInput = {
    create?: XOR<SearchCreateWithoutUserInput, SearchUncheckedCreateWithoutUserInput> | SearchCreateWithoutUserInput[] | SearchUncheckedCreateWithoutUserInput[]
    connectOrCreate?: SearchCreateOrConnectWithoutUserInput | SearchCreateOrConnectWithoutUserInput[]
    createMany?: SearchCreateManyUserInputEnvelope
    connect?: SearchWhereUniqueInput | SearchWhereUniqueInput[]
  }

  export type SearchUncheckedCreateNestedManyWithoutUserInput = {
    create?: XOR<SearchCreateWithoutUserInput, SearchUncheckedCreateWithoutUserInput> | SearchCreateWithoutUserInput[] | SearchUncheckedCreateWithoutUserInput[]
    connectOrCreate?: SearchCreateOrConnectWithoutUserInput | SearchCreateOrConnectWithoutUserInput[]
    createMany?: SearchCreateManyUserInputEnvelope
    connect?: SearchWhereUniqueInput | SearchWhereUniqueInput[]
  }

  export type StringFieldUpdateOperationsInput = {
    set?: string
  }

  export type NullableStringFieldUpdateOperationsInput = {
    set?: string | null
  }

  export type BoolFieldUpdateOperationsInput = {
    set?: boolean
  }

  export type DateTimeFieldUpdateOperationsInput = {
    set?: Date | string
  }

  export type SearchUpdateManyWithoutUserNestedInput = {
    create?: XOR<SearchCreateWithoutUserInput, SearchUncheckedCreateWithoutUserInput> | SearchCreateWithoutUserInput[] | SearchUncheckedCreateWithoutUserInput[]
    connectOrCreate?: SearchCreateOrConnectWithoutUserInput | SearchCreateOrConnectWithoutUserInput[]
    upsert?: SearchUpsertWithWhereUniqueWithoutUserInput | SearchUpsertWithWhereUniqueWithoutUserInput[]
    createMany?: SearchCreateManyUserInputEnvelope
    set?: SearchWhereUniqueInput | SearchWhereUniqueInput[]
    disconnect?: SearchWhereUniqueInput | SearchWhereUniqueInput[]
    delete?: SearchWhereUniqueInput | SearchWhereUniqueInput[]
    connect?: SearchWhereUniqueInput | SearchWhereUniqueInput[]
    update?: SearchUpdateWithWhereUniqueWithoutUserInput | SearchUpdateWithWhereUniqueWithoutUserInput[]
    updateMany?: SearchUpdateManyWithWhereWithoutUserInput | SearchUpdateManyWithWhereWithoutUserInput[]
    deleteMany?: SearchScalarWhereInput | SearchScalarWhereInput[]
  }

  export type SearchUncheckedUpdateManyWithoutUserNestedInput = {
    create?: XOR<SearchCreateWithoutUserInput, SearchUncheckedCreateWithoutUserInput> | SearchCreateWithoutUserInput[] | SearchUncheckedCreateWithoutUserInput[]
    connectOrCreate?: SearchCreateOrConnectWithoutUserInput | SearchCreateOrConnectWithoutUserInput[]
    upsert?: SearchUpsertWithWhereUniqueWithoutUserInput | SearchUpsertWithWhereUniqueWithoutUserInput[]
    createMany?: SearchCreateManyUserInputEnvelope
    set?: SearchWhereUniqueInput | SearchWhereUniqueInput[]
    disconnect?: SearchWhereUniqueInput | SearchWhereUniqueInput[]
    delete?: SearchWhereUniqueInput | SearchWhereUniqueInput[]
    connect?: SearchWhereUniqueInput | SearchWhereUniqueInput[]
    update?: SearchUpdateWithWhereUniqueWithoutUserInput | SearchUpdateWithWhereUniqueWithoutUserInput[]
    updateMany?: SearchUpdateManyWithWhereWithoutUserInput | SearchUpdateManyWithWhereWithoutUserInput[]
    deleteMany?: SearchScalarWhereInput | SearchScalarWhereInput[]
  }

  export type SearchCreateNestedManyWithoutOriginPortInput = {
    create?: XOR<SearchCreateWithoutOriginPortInput, SearchUncheckedCreateWithoutOriginPortInput> | SearchCreateWithoutOriginPortInput[] | SearchUncheckedCreateWithoutOriginPortInput[]
    connectOrCreate?: SearchCreateOrConnectWithoutOriginPortInput | SearchCreateOrConnectWithoutOriginPortInput[]
    createMany?: SearchCreateManyOriginPortInputEnvelope
    connect?: SearchWhereUniqueInput | SearchWhereUniqueInput[]
  }

  export type SearchCreateNestedManyWithoutDestinationPortInput = {
    create?: XOR<SearchCreateWithoutDestinationPortInput, SearchUncheckedCreateWithoutDestinationPortInput> | SearchCreateWithoutDestinationPortInput[] | SearchUncheckedCreateWithoutDestinationPortInput[]
    connectOrCreate?: SearchCreateOrConnectWithoutDestinationPortInput | SearchCreateOrConnectWithoutDestinationPortInput[]
    createMany?: SearchCreateManyDestinationPortInputEnvelope
    connect?: SearchWhereUniqueInput | SearchWhereUniqueInput[]
  }

  export type PortRotationCreateNestedManyWithoutPortInput = {
    create?: XOR<PortRotationCreateWithoutPortInput, PortRotationUncheckedCreateWithoutPortInput> | PortRotationCreateWithoutPortInput[] | PortRotationUncheckedCreateWithoutPortInput[]
    connectOrCreate?: PortRotationCreateOrConnectWithoutPortInput | PortRotationCreateOrConnectWithoutPortInput[]
    createMany?: PortRotationCreateManyPortInputEnvelope
    connect?: PortRotationWhereUniqueInput | PortRotationWhereUniqueInput[]
  }

  export type SearchUncheckedCreateNestedManyWithoutOriginPortInput = {
    create?: XOR<SearchCreateWithoutOriginPortInput, SearchUncheckedCreateWithoutOriginPortInput> | SearchCreateWithoutOriginPortInput[] | SearchUncheckedCreateWithoutOriginPortInput[]
    connectOrCreate?: SearchCreateOrConnectWithoutOriginPortInput | SearchCreateOrConnectWithoutOriginPortInput[]
    createMany?: SearchCreateManyOriginPortInputEnvelope
    connect?: SearchWhereUniqueInput | SearchWhereUniqueInput[]
  }

  export type SearchUncheckedCreateNestedManyWithoutDestinationPortInput = {
    create?: XOR<SearchCreateWithoutDestinationPortInput, SearchUncheckedCreateWithoutDestinationPortInput> | SearchCreateWithoutDestinationPortInput[] | SearchUncheckedCreateWithoutDestinationPortInput[]
    connectOrCreate?: SearchCreateOrConnectWithoutDestinationPortInput | SearchCreateOrConnectWithoutDestinationPortInput[]
    createMany?: SearchCreateManyDestinationPortInputEnvelope
    connect?: SearchWhereUniqueInput | SearchWhereUniqueInput[]
  }

  export type PortRotationUncheckedCreateNestedManyWithoutPortInput = {
    create?: XOR<PortRotationCreateWithoutPortInput, PortRotationUncheckedCreateWithoutPortInput> | PortRotationCreateWithoutPortInput[] | PortRotationUncheckedCreateWithoutPortInput[]
    connectOrCreate?: PortRotationCreateOrConnectWithoutPortInput | PortRotationCreateOrConnectWithoutPortInput[]
    createMany?: PortRotationCreateManyPortInputEnvelope
    connect?: PortRotationWhereUniqueInput | PortRotationWhereUniqueInput[]
  }

  export type NullableFloatFieldUpdateOperationsInput = {
    set?: number | null
    increment?: number
    decrement?: number
    multiply?: number
    divide?: number
  }

  export type SearchUpdateManyWithoutOriginPortNestedInput = {
    create?: XOR<SearchCreateWithoutOriginPortInput, SearchUncheckedCreateWithoutOriginPortInput> | SearchCreateWithoutOriginPortInput[] | SearchUncheckedCreateWithoutOriginPortInput[]
    connectOrCreate?: SearchCreateOrConnectWithoutOriginPortInput | SearchCreateOrConnectWithoutOriginPortInput[]
    upsert?: SearchUpsertWithWhereUniqueWithoutOriginPortInput | SearchUpsertWithWhereUniqueWithoutOriginPortInput[]
    createMany?: SearchCreateManyOriginPortInputEnvelope
    set?: SearchWhereUniqueInput | SearchWhereUniqueInput[]
    disconnect?: SearchWhereUniqueInput | SearchWhereUniqueInput[]
    delete?: SearchWhereUniqueInput | SearchWhereUniqueInput[]
    connect?: SearchWhereUniqueInput | SearchWhereUniqueInput[]
    update?: SearchUpdateWithWhereUniqueWithoutOriginPortInput | SearchUpdateWithWhereUniqueWithoutOriginPortInput[]
    updateMany?: SearchUpdateManyWithWhereWithoutOriginPortInput | SearchUpdateManyWithWhereWithoutOriginPortInput[]
    deleteMany?: SearchScalarWhereInput | SearchScalarWhereInput[]
  }

  export type SearchUpdateManyWithoutDestinationPortNestedInput = {
    create?: XOR<SearchCreateWithoutDestinationPortInput, SearchUncheckedCreateWithoutDestinationPortInput> | SearchCreateWithoutDestinationPortInput[] | SearchUncheckedCreateWithoutDestinationPortInput[]
    connectOrCreate?: SearchCreateOrConnectWithoutDestinationPortInput | SearchCreateOrConnectWithoutDestinationPortInput[]
    upsert?: SearchUpsertWithWhereUniqueWithoutDestinationPortInput | SearchUpsertWithWhereUniqueWithoutDestinationPortInput[]
    createMany?: SearchCreateManyDestinationPortInputEnvelope
    set?: SearchWhereUniqueInput | SearchWhereUniqueInput[]
    disconnect?: SearchWhereUniqueInput | SearchWhereUniqueInput[]
    delete?: SearchWhereUniqueInput | SearchWhereUniqueInput[]
    connect?: SearchWhereUniqueInput | SearchWhereUniqueInput[]
    update?: SearchUpdateWithWhereUniqueWithoutDestinationPortInput | SearchUpdateWithWhereUniqueWithoutDestinationPortInput[]
    updateMany?: SearchUpdateManyWithWhereWithoutDestinationPortInput | SearchUpdateManyWithWhereWithoutDestinationPortInput[]
    deleteMany?: SearchScalarWhereInput | SearchScalarWhereInput[]
  }

  export type PortRotationUpdateManyWithoutPortNestedInput = {
    create?: XOR<PortRotationCreateWithoutPortInput, PortRotationUncheckedCreateWithoutPortInput> | PortRotationCreateWithoutPortInput[] | PortRotationUncheckedCreateWithoutPortInput[]
    connectOrCreate?: PortRotationCreateOrConnectWithoutPortInput | PortRotationCreateOrConnectWithoutPortInput[]
    upsert?: PortRotationUpsertWithWhereUniqueWithoutPortInput | PortRotationUpsertWithWhereUniqueWithoutPortInput[]
    createMany?: PortRotationCreateManyPortInputEnvelope
    set?: PortRotationWhereUniqueInput | PortRotationWhereUniqueInput[]
    disconnect?: PortRotationWhereUniqueInput | PortRotationWhereUniqueInput[]
    delete?: PortRotationWhereUniqueInput | PortRotationWhereUniqueInput[]
    connect?: PortRotationWhereUniqueInput | PortRotationWhereUniqueInput[]
    update?: PortRotationUpdateWithWhereUniqueWithoutPortInput | PortRotationUpdateWithWhereUniqueWithoutPortInput[]
    updateMany?: PortRotationUpdateManyWithWhereWithoutPortInput | PortRotationUpdateManyWithWhereWithoutPortInput[]
    deleteMany?: PortRotationScalarWhereInput | PortRotationScalarWhereInput[]
  }

  export type SearchUncheckedUpdateManyWithoutOriginPortNestedInput = {
    create?: XOR<SearchCreateWithoutOriginPortInput, SearchUncheckedCreateWithoutOriginPortInput> | SearchCreateWithoutOriginPortInput[] | SearchUncheckedCreateWithoutOriginPortInput[]
    connectOrCreate?: SearchCreateOrConnectWithoutOriginPortInput | SearchCreateOrConnectWithoutOriginPortInput[]
    upsert?: SearchUpsertWithWhereUniqueWithoutOriginPortInput | SearchUpsertWithWhereUniqueWithoutOriginPortInput[]
    createMany?: SearchCreateManyOriginPortInputEnvelope
    set?: SearchWhereUniqueInput | SearchWhereUniqueInput[]
    disconnect?: SearchWhereUniqueInput | SearchWhereUniqueInput[]
    delete?: SearchWhereUniqueInput | SearchWhereUniqueInput[]
    connect?: SearchWhereUniqueInput | SearchWhereUniqueInput[]
    update?: SearchUpdateWithWhereUniqueWithoutOriginPortInput | SearchUpdateWithWhereUniqueWithoutOriginPortInput[]
    updateMany?: SearchUpdateManyWithWhereWithoutOriginPortInput | SearchUpdateManyWithWhereWithoutOriginPortInput[]
    deleteMany?: SearchScalarWhereInput | SearchScalarWhereInput[]
  }

  export type SearchUncheckedUpdateManyWithoutDestinationPortNestedInput = {
    create?: XOR<SearchCreateWithoutDestinationPortInput, SearchUncheckedCreateWithoutDestinationPortInput> | SearchCreateWithoutDestinationPortInput[] | SearchUncheckedCreateWithoutDestinationPortInput[]
    connectOrCreate?: SearchCreateOrConnectWithoutDestinationPortInput | SearchCreateOrConnectWithoutDestinationPortInput[]
    upsert?: SearchUpsertWithWhereUniqueWithoutDestinationPortInput | SearchUpsertWithWhereUniqueWithoutDestinationPortInput[]
    createMany?: SearchCreateManyDestinationPortInputEnvelope
    set?: SearchWhereUniqueInput | SearchWhereUniqueInput[]
    disconnect?: SearchWhereUniqueInput | SearchWhereUniqueInput[]
    delete?: SearchWhereUniqueInput | SearchWhereUniqueInput[]
    connect?: SearchWhereUniqueInput | SearchWhereUniqueInput[]
    update?: SearchUpdateWithWhereUniqueWithoutDestinationPortInput | SearchUpdateWithWhereUniqueWithoutDestinationPortInput[]
    updateMany?: SearchUpdateManyWithWhereWithoutDestinationPortInput | SearchUpdateManyWithWhereWithoutDestinationPortInput[]
    deleteMany?: SearchScalarWhereInput | SearchScalarWhereInput[]
  }

  export type PortRotationUncheckedUpdateManyWithoutPortNestedInput = {
    create?: XOR<PortRotationCreateWithoutPortInput, PortRotationUncheckedCreateWithoutPortInput> | PortRotationCreateWithoutPortInput[] | PortRotationUncheckedCreateWithoutPortInput[]
    connectOrCreate?: PortRotationCreateOrConnectWithoutPortInput | PortRotationCreateOrConnectWithoutPortInput[]
    upsert?: PortRotationUpsertWithWhereUniqueWithoutPortInput | PortRotationUpsertWithWhereUniqueWithoutPortInput[]
    createMany?: PortRotationCreateManyPortInputEnvelope
    set?: PortRotationWhereUniqueInput | PortRotationWhereUniqueInput[]
    disconnect?: PortRotationWhereUniqueInput | PortRotationWhereUniqueInput[]
    delete?: PortRotationWhereUniqueInput | PortRotationWhereUniqueInput[]
    connect?: PortRotationWhereUniqueInput | PortRotationWhereUniqueInput[]
    update?: PortRotationUpdateWithWhereUniqueWithoutPortInput | PortRotationUpdateWithWhereUniqueWithoutPortInput[]
    updateMany?: PortRotationUpdateManyWithWhereWithoutPortInput | PortRotationUpdateManyWithWhereWithoutPortInput[]
    deleteMany?: PortRotationScalarWhereInput | PortRotationScalarWhereInput[]
  }

  export type SearchResultCreateNestedManyWithoutCarrierInput = {
    create?: XOR<SearchResultCreateWithoutCarrierInput, SearchResultUncheckedCreateWithoutCarrierInput> | SearchResultCreateWithoutCarrierInput[] | SearchResultUncheckedCreateWithoutCarrierInput[]
    connectOrCreate?: SearchResultCreateOrConnectWithoutCarrierInput | SearchResultCreateOrConnectWithoutCarrierInput[]
    createMany?: SearchResultCreateManyCarrierInputEnvelope
    connect?: SearchResultWhereUniqueInput | SearchResultWhereUniqueInput[]
  }

  export type SearchResultUncheckedCreateNestedManyWithoutCarrierInput = {
    create?: XOR<SearchResultCreateWithoutCarrierInput, SearchResultUncheckedCreateWithoutCarrierInput> | SearchResultCreateWithoutCarrierInput[] | SearchResultUncheckedCreateWithoutCarrierInput[]
    connectOrCreate?: SearchResultCreateOrConnectWithoutCarrierInput | SearchResultCreateOrConnectWithoutCarrierInput[]
    createMany?: SearchResultCreateManyCarrierInputEnvelope
    connect?: SearchResultWhereUniqueInput | SearchResultWhereUniqueInput[]
  }

  export type SearchResultUpdateManyWithoutCarrierNestedInput = {
    create?: XOR<SearchResultCreateWithoutCarrierInput, SearchResultUncheckedCreateWithoutCarrierInput> | SearchResultCreateWithoutCarrierInput[] | SearchResultUncheckedCreateWithoutCarrierInput[]
    connectOrCreate?: SearchResultCreateOrConnectWithoutCarrierInput | SearchResultCreateOrConnectWithoutCarrierInput[]
    upsert?: SearchResultUpsertWithWhereUniqueWithoutCarrierInput | SearchResultUpsertWithWhereUniqueWithoutCarrierInput[]
    createMany?: SearchResultCreateManyCarrierInputEnvelope
    set?: SearchResultWhereUniqueInput | SearchResultWhereUniqueInput[]
    disconnect?: SearchResultWhereUniqueInput | SearchResultWhereUniqueInput[]
    delete?: SearchResultWhereUniqueInput | SearchResultWhereUniqueInput[]
    connect?: SearchResultWhereUniqueInput | SearchResultWhereUniqueInput[]
    update?: SearchResultUpdateWithWhereUniqueWithoutCarrierInput | SearchResultUpdateWithWhereUniqueWithoutCarrierInput[]
    updateMany?: SearchResultUpdateManyWithWhereWithoutCarrierInput | SearchResultUpdateManyWithWhereWithoutCarrierInput[]
    deleteMany?: SearchResultScalarWhereInput | SearchResultScalarWhereInput[]
  }

  export type SearchResultUncheckedUpdateManyWithoutCarrierNestedInput = {
    create?: XOR<SearchResultCreateWithoutCarrierInput, SearchResultUncheckedCreateWithoutCarrierInput> | SearchResultCreateWithoutCarrierInput[] | SearchResultUncheckedCreateWithoutCarrierInput[]
    connectOrCreate?: SearchResultCreateOrConnectWithoutCarrierInput | SearchResultCreateOrConnectWithoutCarrierInput[]
    upsert?: SearchResultUpsertWithWhereUniqueWithoutCarrierInput | SearchResultUpsertWithWhereUniqueWithoutCarrierInput[]
    createMany?: SearchResultCreateManyCarrierInputEnvelope
    set?: SearchResultWhereUniqueInput | SearchResultWhereUniqueInput[]
    disconnect?: SearchResultWhereUniqueInput | SearchResultWhereUniqueInput[]
    delete?: SearchResultWhereUniqueInput | SearchResultWhereUniqueInput[]
    connect?: SearchResultWhereUniqueInput | SearchResultWhereUniqueInput[]
    update?: SearchResultUpdateWithWhereUniqueWithoutCarrierInput | SearchResultUpdateWithWhereUniqueWithoutCarrierInput[]
    updateMany?: SearchResultUpdateManyWithWhereWithoutCarrierInput | SearchResultUpdateManyWithWhereWithoutCarrierInput[]
    deleteMany?: SearchResultScalarWhereInput | SearchResultScalarWhereInput[]
  }

  export type UserCreateNestedOneWithoutSearchesInput = {
    create?: XOR<UserCreateWithoutSearchesInput, UserUncheckedCreateWithoutSearchesInput>
    connectOrCreate?: UserCreateOrConnectWithoutSearchesInput
    connect?: UserWhereUniqueInput
  }

  export type PortCreateNestedOneWithoutOriginSearchesInput = {
    create?: XOR<PortCreateWithoutOriginSearchesInput, PortUncheckedCreateWithoutOriginSearchesInput>
    connectOrCreate?: PortCreateOrConnectWithoutOriginSearchesInput
    connect?: PortWhereUniqueInput
  }

  export type PortCreateNestedOneWithoutDestinationSearchesInput = {
    create?: XOR<PortCreateWithoutDestinationSearchesInput, PortUncheckedCreateWithoutDestinationSearchesInput>
    connectOrCreate?: PortCreateOrConnectWithoutDestinationSearchesInput
    connect?: PortWhereUniqueInput
  }

  export type SearchResultCreateNestedManyWithoutSearchInput = {
    create?: XOR<SearchResultCreateWithoutSearchInput, SearchResultUncheckedCreateWithoutSearchInput> | SearchResultCreateWithoutSearchInput[] | SearchResultUncheckedCreateWithoutSearchInput[]
    connectOrCreate?: SearchResultCreateOrConnectWithoutSearchInput | SearchResultCreateOrConnectWithoutSearchInput[]
    createMany?: SearchResultCreateManySearchInputEnvelope
    connect?: SearchResultWhereUniqueInput | SearchResultWhereUniqueInput[]
  }

  export type SearchResultUncheckedCreateNestedManyWithoutSearchInput = {
    create?: XOR<SearchResultCreateWithoutSearchInput, SearchResultUncheckedCreateWithoutSearchInput> | SearchResultCreateWithoutSearchInput[] | SearchResultUncheckedCreateWithoutSearchInput[]
    connectOrCreate?: SearchResultCreateOrConnectWithoutSearchInput | SearchResultCreateOrConnectWithoutSearchInput[]
    createMany?: SearchResultCreateManySearchInputEnvelope
    connect?: SearchResultWhereUniqueInput | SearchResultWhereUniqueInput[]
  }

  export type IntFieldUpdateOperationsInput = {
    set?: number
    increment?: number
    decrement?: number
    multiply?: number
    divide?: number
  }

  export type NullableIntFieldUpdateOperationsInput = {
    set?: number | null
    increment?: number
    decrement?: number
    multiply?: number
    divide?: number
  }

  export type UserUpdateOneRequiredWithoutSearchesNestedInput = {
    create?: XOR<UserCreateWithoutSearchesInput, UserUncheckedCreateWithoutSearchesInput>
    connectOrCreate?: UserCreateOrConnectWithoutSearchesInput
    upsert?: UserUpsertWithoutSearchesInput
    connect?: UserWhereUniqueInput
    update?: XOR<XOR<UserUpdateToOneWithWhereWithoutSearchesInput, UserUpdateWithoutSearchesInput>, UserUncheckedUpdateWithoutSearchesInput>
  }

  export type PortUpdateOneRequiredWithoutOriginSearchesNestedInput = {
    create?: XOR<PortCreateWithoutOriginSearchesInput, PortUncheckedCreateWithoutOriginSearchesInput>
    connectOrCreate?: PortCreateOrConnectWithoutOriginSearchesInput
    upsert?: PortUpsertWithoutOriginSearchesInput
    connect?: PortWhereUniqueInput
    update?: XOR<XOR<PortUpdateToOneWithWhereWithoutOriginSearchesInput, PortUpdateWithoutOriginSearchesInput>, PortUncheckedUpdateWithoutOriginSearchesInput>
  }

  export type PortUpdateOneRequiredWithoutDestinationSearchesNestedInput = {
    create?: XOR<PortCreateWithoutDestinationSearchesInput, PortUncheckedCreateWithoutDestinationSearchesInput>
    connectOrCreate?: PortCreateOrConnectWithoutDestinationSearchesInput
    upsert?: PortUpsertWithoutDestinationSearchesInput
    connect?: PortWhereUniqueInput
    update?: XOR<XOR<PortUpdateToOneWithWhereWithoutDestinationSearchesInput, PortUpdateWithoutDestinationSearchesInput>, PortUncheckedUpdateWithoutDestinationSearchesInput>
  }

  export type SearchResultUpdateManyWithoutSearchNestedInput = {
    create?: XOR<SearchResultCreateWithoutSearchInput, SearchResultUncheckedCreateWithoutSearchInput> | SearchResultCreateWithoutSearchInput[] | SearchResultUncheckedCreateWithoutSearchInput[]
    connectOrCreate?: SearchResultCreateOrConnectWithoutSearchInput | SearchResultCreateOrConnectWithoutSearchInput[]
    upsert?: SearchResultUpsertWithWhereUniqueWithoutSearchInput | SearchResultUpsertWithWhereUniqueWithoutSearchInput[]
    createMany?: SearchResultCreateManySearchInputEnvelope
    set?: SearchResultWhereUniqueInput | SearchResultWhereUniqueInput[]
    disconnect?: SearchResultWhereUniqueInput | SearchResultWhereUniqueInput[]
    delete?: SearchResultWhereUniqueInput | SearchResultWhereUniqueInput[]
    connect?: SearchResultWhereUniqueInput | SearchResultWhereUniqueInput[]
    update?: SearchResultUpdateWithWhereUniqueWithoutSearchInput | SearchResultUpdateWithWhereUniqueWithoutSearchInput[]
    updateMany?: SearchResultUpdateManyWithWhereWithoutSearchInput | SearchResultUpdateManyWithWhereWithoutSearchInput[]
    deleteMany?: SearchResultScalarWhereInput | SearchResultScalarWhereInput[]
  }

  export type SearchResultUncheckedUpdateManyWithoutSearchNestedInput = {
    create?: XOR<SearchResultCreateWithoutSearchInput, SearchResultUncheckedCreateWithoutSearchInput> | SearchResultCreateWithoutSearchInput[] | SearchResultUncheckedCreateWithoutSearchInput[]
    connectOrCreate?: SearchResultCreateOrConnectWithoutSearchInput | SearchResultCreateOrConnectWithoutSearchInput[]
    upsert?: SearchResultUpsertWithWhereUniqueWithoutSearchInput | SearchResultUpsertWithWhereUniqueWithoutSearchInput[]
    createMany?: SearchResultCreateManySearchInputEnvelope
    set?: SearchResultWhereUniqueInput | SearchResultWhereUniqueInput[]
    disconnect?: SearchResultWhereUniqueInput | SearchResultWhereUniqueInput[]
    delete?: SearchResultWhereUniqueInput | SearchResultWhereUniqueInput[]
    connect?: SearchResultWhereUniqueInput | SearchResultWhereUniqueInput[]
    update?: SearchResultUpdateWithWhereUniqueWithoutSearchInput | SearchResultUpdateWithWhereUniqueWithoutSearchInput[]
    updateMany?: SearchResultUpdateManyWithWhereWithoutSearchInput | SearchResultUpdateManyWithWhereWithoutSearchInput[]
    deleteMany?: SearchResultScalarWhereInput | SearchResultScalarWhereInput[]
  }

  export type SearchCreateNestedOneWithoutSearchResultsInput = {
    create?: XOR<SearchCreateWithoutSearchResultsInput, SearchUncheckedCreateWithoutSearchResultsInput>
    connectOrCreate?: SearchCreateOrConnectWithoutSearchResultsInput
    connect?: SearchWhereUniqueInput
  }

  export type CarrierCreateNestedOneWithoutSearchResultsInput = {
    create?: XOR<CarrierCreateWithoutSearchResultsInput, CarrierUncheckedCreateWithoutSearchResultsInput>
    connectOrCreate?: CarrierCreateOrConnectWithoutSearchResultsInput
    connect?: CarrierWhereUniqueInput
  }

  export type PortRotationCreateNestedManyWithoutSearchResultInput = {
    create?: XOR<PortRotationCreateWithoutSearchResultInput, PortRotationUncheckedCreateWithoutSearchResultInput> | PortRotationCreateWithoutSearchResultInput[] | PortRotationUncheckedCreateWithoutSearchResultInput[]
    connectOrCreate?: PortRotationCreateOrConnectWithoutSearchResultInput | PortRotationCreateOrConnectWithoutSearchResultInput[]
    createMany?: PortRotationCreateManySearchResultInputEnvelope
    connect?: PortRotationWhereUniqueInput | PortRotationWhereUniqueInput[]
  }

  export type PortRotationUncheckedCreateNestedManyWithoutSearchResultInput = {
    create?: XOR<PortRotationCreateWithoutSearchResultInput, PortRotationUncheckedCreateWithoutSearchResultInput> | PortRotationCreateWithoutSearchResultInput[] | PortRotationUncheckedCreateWithoutSearchResultInput[]
    connectOrCreate?: PortRotationCreateOrConnectWithoutSearchResultInput | PortRotationCreateOrConnectWithoutSearchResultInput[]
    createMany?: PortRotationCreateManySearchResultInputEnvelope
    connect?: PortRotationWhereUniqueInput | PortRotationWhereUniqueInput[]
  }

  export type SearchUpdateOneRequiredWithoutSearchResultsNestedInput = {
    create?: XOR<SearchCreateWithoutSearchResultsInput, SearchUncheckedCreateWithoutSearchResultsInput>
    connectOrCreate?: SearchCreateOrConnectWithoutSearchResultsInput
    upsert?: SearchUpsertWithoutSearchResultsInput
    connect?: SearchWhereUniqueInput
    update?: XOR<XOR<SearchUpdateToOneWithWhereWithoutSearchResultsInput, SearchUpdateWithoutSearchResultsInput>, SearchUncheckedUpdateWithoutSearchResultsInput>
  }

  export type CarrierUpdateOneRequiredWithoutSearchResultsNestedInput = {
    create?: XOR<CarrierCreateWithoutSearchResultsInput, CarrierUncheckedCreateWithoutSearchResultsInput>
    connectOrCreate?: CarrierCreateOrConnectWithoutSearchResultsInput
    upsert?: CarrierUpsertWithoutSearchResultsInput
    connect?: CarrierWhereUniqueInput
    update?: XOR<XOR<CarrierUpdateToOneWithWhereWithoutSearchResultsInput, CarrierUpdateWithoutSearchResultsInput>, CarrierUncheckedUpdateWithoutSearchResultsInput>
  }

  export type PortRotationUpdateManyWithoutSearchResultNestedInput = {
    create?: XOR<PortRotationCreateWithoutSearchResultInput, PortRotationUncheckedCreateWithoutSearchResultInput> | PortRotationCreateWithoutSearchResultInput[] | PortRotationUncheckedCreateWithoutSearchResultInput[]
    connectOrCreate?: PortRotationCreateOrConnectWithoutSearchResultInput | PortRotationCreateOrConnectWithoutSearchResultInput[]
    upsert?: PortRotationUpsertWithWhereUniqueWithoutSearchResultInput | PortRotationUpsertWithWhereUniqueWithoutSearchResultInput[]
    createMany?: PortRotationCreateManySearchResultInputEnvelope
    set?: PortRotationWhereUniqueInput | PortRotationWhereUniqueInput[]
    disconnect?: PortRotationWhereUniqueInput | PortRotationWhereUniqueInput[]
    delete?: PortRotationWhereUniqueInput | PortRotationWhereUniqueInput[]
    connect?: PortRotationWhereUniqueInput | PortRotationWhereUniqueInput[]
    update?: PortRotationUpdateWithWhereUniqueWithoutSearchResultInput | PortRotationUpdateWithWhereUniqueWithoutSearchResultInput[]
    updateMany?: PortRotationUpdateManyWithWhereWithoutSearchResultInput | PortRotationUpdateManyWithWhereWithoutSearchResultInput[]
    deleteMany?: PortRotationScalarWhereInput | PortRotationScalarWhereInput[]
  }

  export type PortRotationUncheckedUpdateManyWithoutSearchResultNestedInput = {
    create?: XOR<PortRotationCreateWithoutSearchResultInput, PortRotationUncheckedCreateWithoutSearchResultInput> | PortRotationCreateWithoutSearchResultInput[] | PortRotationUncheckedCreateWithoutSearchResultInput[]
    connectOrCreate?: PortRotationCreateOrConnectWithoutSearchResultInput | PortRotationCreateOrConnectWithoutSearchResultInput[]
    upsert?: PortRotationUpsertWithWhereUniqueWithoutSearchResultInput | PortRotationUpsertWithWhereUniqueWithoutSearchResultInput[]
    createMany?: PortRotationCreateManySearchResultInputEnvelope
    set?: PortRotationWhereUniqueInput | PortRotationWhereUniqueInput[]
    disconnect?: PortRotationWhereUniqueInput | PortRotationWhereUniqueInput[]
    delete?: PortRotationWhereUniqueInput | PortRotationWhereUniqueInput[]
    connect?: PortRotationWhereUniqueInput | PortRotationWhereUniqueInput[]
    update?: PortRotationUpdateWithWhereUniqueWithoutSearchResultInput | PortRotationUpdateWithWhereUniqueWithoutSearchResultInput[]
    updateMany?: PortRotationUpdateManyWithWhereWithoutSearchResultInput | PortRotationUpdateManyWithWhereWithoutSearchResultInput[]
    deleteMany?: PortRotationScalarWhereInput | PortRotationScalarWhereInput[]
  }

  export type SearchResultCreateNestedOneWithoutPortRotationsInput = {
    create?: XOR<SearchResultCreateWithoutPortRotationsInput, SearchResultUncheckedCreateWithoutPortRotationsInput>
    connectOrCreate?: SearchResultCreateOrConnectWithoutPortRotationsInput
    connect?: SearchResultWhereUniqueInput
  }

  export type PortCreateNestedOneWithoutPortRotationsInput = {
    create?: XOR<PortCreateWithoutPortRotationsInput, PortUncheckedCreateWithoutPortRotationsInput>
    connectOrCreate?: PortCreateOrConnectWithoutPortRotationsInput
    connect?: PortWhereUniqueInput
  }

  export type NullableDateTimeFieldUpdateOperationsInput = {
    set?: Date | string | null
  }

  export type SearchResultUpdateOneRequiredWithoutPortRotationsNestedInput = {
    create?: XOR<SearchResultCreateWithoutPortRotationsInput, SearchResultUncheckedCreateWithoutPortRotationsInput>
    connectOrCreate?: SearchResultCreateOrConnectWithoutPortRotationsInput
    upsert?: SearchResultUpsertWithoutPortRotationsInput
    connect?: SearchResultWhereUniqueInput
    update?: XOR<XOR<SearchResultUpdateToOneWithWhereWithoutPortRotationsInput, SearchResultUpdateWithoutPortRotationsInput>, SearchResultUncheckedUpdateWithoutPortRotationsInput>
  }

  export type PortUpdateOneRequiredWithoutPortRotationsNestedInput = {
    create?: XOR<PortCreateWithoutPortRotationsInput, PortUncheckedCreateWithoutPortRotationsInput>
    connectOrCreate?: PortCreateOrConnectWithoutPortRotationsInput
    upsert?: PortUpsertWithoutPortRotationsInput
    connect?: PortWhereUniqueInput
    update?: XOR<XOR<PortUpdateToOneWithWhereWithoutPortRotationsInput, PortUpdateWithoutPortRotationsInput>, PortUncheckedUpdateWithoutPortRotationsInput>
  }

  export type NestedStringFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringFilter<$PrismaModel> | string
  }

  export type NestedStringNullableFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableFilter<$PrismaModel> | string | null
  }

  export type NestedBoolFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolFilter<$PrismaModel> | boolean
  }

  export type NestedDateTimeFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeFilter<$PrismaModel> | Date | string
  }

  export type NestedStringWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringWithAggregatesFilter<$PrismaModel> | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedStringFilter<$PrismaModel>
    _max?: NestedStringFilter<$PrismaModel>
  }

  export type NestedIntFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[] | ListIntFieldRefInput<$PrismaModel>
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel>
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntFilter<$PrismaModel> | number
  }

  export type NestedStringNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableWithAggregatesFilter<$PrismaModel> | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedStringNullableFilter<$PrismaModel>
    _max?: NestedStringNullableFilter<$PrismaModel>
  }

  export type NestedIntNullableFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableFilter<$PrismaModel> | number | null
  }

  export type NestedBoolWithAggregatesFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolWithAggregatesFilter<$PrismaModel> | boolean
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedBoolFilter<$PrismaModel>
    _max?: NestedBoolFilter<$PrismaModel>
  }

  export type NestedDateTimeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeWithAggregatesFilter<$PrismaModel> | Date | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedDateTimeFilter<$PrismaModel>
    _max?: NestedDateTimeFilter<$PrismaModel>
  }

  export type NestedFloatNullableFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel> | null
    in?: number[] | ListFloatFieldRefInput<$PrismaModel> | null
    notIn?: number[] | ListFloatFieldRefInput<$PrismaModel> | null
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatNullableFilter<$PrismaModel> | number | null
  }

  export type NestedFloatNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel> | null
    in?: number[] | ListFloatFieldRefInput<$PrismaModel> | null
    notIn?: number[] | ListFloatFieldRefInput<$PrismaModel> | null
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatNullableWithAggregatesFilter<$PrismaModel> | number | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _avg?: NestedFloatNullableFilter<$PrismaModel>
    _sum?: NestedFloatNullableFilter<$PrismaModel>
    _min?: NestedFloatNullableFilter<$PrismaModel>
    _max?: NestedFloatNullableFilter<$PrismaModel>
  }

  export type NestedIntWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[] | ListIntFieldRefInput<$PrismaModel>
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel>
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntWithAggregatesFilter<$PrismaModel> | number
    _count?: NestedIntFilter<$PrismaModel>
    _avg?: NestedFloatFilter<$PrismaModel>
    _sum?: NestedIntFilter<$PrismaModel>
    _min?: NestedIntFilter<$PrismaModel>
    _max?: NestedIntFilter<$PrismaModel>
  }

  export type NestedFloatFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel>
    in?: number[] | ListFloatFieldRefInput<$PrismaModel>
    notIn?: number[] | ListFloatFieldRefInput<$PrismaModel>
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatFilter<$PrismaModel> | number
  }

  export type NestedIntNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableWithAggregatesFilter<$PrismaModel> | number | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _avg?: NestedFloatNullableFilter<$PrismaModel>
    _sum?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedIntNullableFilter<$PrismaModel>
    _max?: NestedIntNullableFilter<$PrismaModel>
  }
  export type NestedJsonNullableFilter<$PrismaModel = never> =
    | PatchUndefined<
        Either<Required<NestedJsonNullableFilterBase<$PrismaModel>>, Exclude<keyof Required<NestedJsonNullableFilterBase<$PrismaModel>>, 'path'>>,
        Required<NestedJsonNullableFilterBase<$PrismaModel>>
      >
    | OptionalFlat<Omit<Required<NestedJsonNullableFilterBase<$PrismaModel>>, 'path'>>

  export type NestedJsonNullableFilterBase<$PrismaModel = never> = {
    equals?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    path?: string[]
    mode?: QueryMode | EnumQueryModeFieldRefInput<$PrismaModel>
    string_contains?: string | StringFieldRefInput<$PrismaModel>
    string_starts_with?: string | StringFieldRefInput<$PrismaModel>
    string_ends_with?: string | StringFieldRefInput<$PrismaModel>
    array_starts_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_ends_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_contains?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    lt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    lte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    not?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
  }

  export type NestedDateTimeNullableFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableFilter<$PrismaModel> | Date | string | null
  }

  export type NestedDateTimeNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableWithAggregatesFilter<$PrismaModel> | Date | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedDateTimeNullableFilter<$PrismaModel>
    _max?: NestedDateTimeNullableFilter<$PrismaModel>
  }

  export type SearchCreateWithoutUserInput = {
    id?: string
    departureDate: Date | string
    searchTimestamp?: Date | string
    resultsCount?: number
    searchDurationMs?: number | null
    originPort: PortCreateNestedOneWithoutOriginSearchesInput
    destinationPort: PortCreateNestedOneWithoutDestinationSearchesInput
    searchResults?: SearchResultCreateNestedManyWithoutSearchInput
  }

  export type SearchUncheckedCreateWithoutUserInput = {
    id?: string
    originPortId: string
    destinationPortId: string
    departureDate: Date | string
    searchTimestamp?: Date | string
    resultsCount?: number
    searchDurationMs?: number | null
    searchResults?: SearchResultUncheckedCreateNestedManyWithoutSearchInput
  }

  export type SearchCreateOrConnectWithoutUserInput = {
    where: SearchWhereUniqueInput
    create: XOR<SearchCreateWithoutUserInput, SearchUncheckedCreateWithoutUserInput>
  }

  export type SearchCreateManyUserInputEnvelope = {
    data: SearchCreateManyUserInput | SearchCreateManyUserInput[]
    skipDuplicates?: boolean
  }

  export type SearchUpsertWithWhereUniqueWithoutUserInput = {
    where: SearchWhereUniqueInput
    update: XOR<SearchUpdateWithoutUserInput, SearchUncheckedUpdateWithoutUserInput>
    create: XOR<SearchCreateWithoutUserInput, SearchUncheckedCreateWithoutUserInput>
  }

  export type SearchUpdateWithWhereUniqueWithoutUserInput = {
    where: SearchWhereUniqueInput
    data: XOR<SearchUpdateWithoutUserInput, SearchUncheckedUpdateWithoutUserInput>
  }

  export type SearchUpdateManyWithWhereWithoutUserInput = {
    where: SearchScalarWhereInput
    data: XOR<SearchUpdateManyMutationInput, SearchUncheckedUpdateManyWithoutUserInput>
  }

  export type SearchScalarWhereInput = {
    AND?: SearchScalarWhereInput | SearchScalarWhereInput[]
    OR?: SearchScalarWhereInput[]
    NOT?: SearchScalarWhereInput | SearchScalarWhereInput[]
    id?: StringFilter<"Search"> | string
    userId?: StringFilter<"Search"> | string
    originPortId?: StringFilter<"Search"> | string
    destinationPortId?: StringFilter<"Search"> | string
    departureDate?: DateTimeFilter<"Search"> | Date | string
    searchTimestamp?: DateTimeFilter<"Search"> | Date | string
    resultsCount?: IntFilter<"Search"> | number
    searchDurationMs?: IntNullableFilter<"Search"> | number | null
  }

  export type SearchCreateWithoutOriginPortInput = {
    id?: string
    departureDate: Date | string
    searchTimestamp?: Date | string
    resultsCount?: number
    searchDurationMs?: number | null
    user: UserCreateNestedOneWithoutSearchesInput
    destinationPort: PortCreateNestedOneWithoutDestinationSearchesInput
    searchResults?: SearchResultCreateNestedManyWithoutSearchInput
  }

  export type SearchUncheckedCreateWithoutOriginPortInput = {
    id?: string
    userId: string
    destinationPortId: string
    departureDate: Date | string
    searchTimestamp?: Date | string
    resultsCount?: number
    searchDurationMs?: number | null
    searchResults?: SearchResultUncheckedCreateNestedManyWithoutSearchInput
  }

  export type SearchCreateOrConnectWithoutOriginPortInput = {
    where: SearchWhereUniqueInput
    create: XOR<SearchCreateWithoutOriginPortInput, SearchUncheckedCreateWithoutOriginPortInput>
  }

  export type SearchCreateManyOriginPortInputEnvelope = {
    data: SearchCreateManyOriginPortInput | SearchCreateManyOriginPortInput[]
    skipDuplicates?: boolean
  }

  export type SearchCreateWithoutDestinationPortInput = {
    id?: string
    departureDate: Date | string
    searchTimestamp?: Date | string
    resultsCount?: number
    searchDurationMs?: number | null
    user: UserCreateNestedOneWithoutSearchesInput
    originPort: PortCreateNestedOneWithoutOriginSearchesInput
    searchResults?: SearchResultCreateNestedManyWithoutSearchInput
  }

  export type SearchUncheckedCreateWithoutDestinationPortInput = {
    id?: string
    userId: string
    originPortId: string
    departureDate: Date | string
    searchTimestamp?: Date | string
    resultsCount?: number
    searchDurationMs?: number | null
    searchResults?: SearchResultUncheckedCreateNestedManyWithoutSearchInput
  }

  export type SearchCreateOrConnectWithoutDestinationPortInput = {
    where: SearchWhereUniqueInput
    create: XOR<SearchCreateWithoutDestinationPortInput, SearchUncheckedCreateWithoutDestinationPortInput>
  }

  export type SearchCreateManyDestinationPortInputEnvelope = {
    data: SearchCreateManyDestinationPortInput | SearchCreateManyDestinationPortInput[]
    skipDuplicates?: boolean
  }

  export type PortRotationCreateWithoutPortInput = {
    id?: string
    sequenceOrder: number
    arrivalDate?: Date | string | null
    departureDate?: Date | string | null
    searchResult: SearchResultCreateNestedOneWithoutPortRotationsInput
  }

  export type PortRotationUncheckedCreateWithoutPortInput = {
    id?: string
    searchResultId: string
    sequenceOrder: number
    arrivalDate?: Date | string | null
    departureDate?: Date | string | null
  }

  export type PortRotationCreateOrConnectWithoutPortInput = {
    where: PortRotationWhereUniqueInput
    create: XOR<PortRotationCreateWithoutPortInput, PortRotationUncheckedCreateWithoutPortInput>
  }

  export type PortRotationCreateManyPortInputEnvelope = {
    data: PortRotationCreateManyPortInput | PortRotationCreateManyPortInput[]
    skipDuplicates?: boolean
  }

  export type SearchUpsertWithWhereUniqueWithoutOriginPortInput = {
    where: SearchWhereUniqueInput
    update: XOR<SearchUpdateWithoutOriginPortInput, SearchUncheckedUpdateWithoutOriginPortInput>
    create: XOR<SearchCreateWithoutOriginPortInput, SearchUncheckedCreateWithoutOriginPortInput>
  }

  export type SearchUpdateWithWhereUniqueWithoutOriginPortInput = {
    where: SearchWhereUniqueInput
    data: XOR<SearchUpdateWithoutOriginPortInput, SearchUncheckedUpdateWithoutOriginPortInput>
  }

  export type SearchUpdateManyWithWhereWithoutOriginPortInput = {
    where: SearchScalarWhereInput
    data: XOR<SearchUpdateManyMutationInput, SearchUncheckedUpdateManyWithoutOriginPortInput>
  }

  export type SearchUpsertWithWhereUniqueWithoutDestinationPortInput = {
    where: SearchWhereUniqueInput
    update: XOR<SearchUpdateWithoutDestinationPortInput, SearchUncheckedUpdateWithoutDestinationPortInput>
    create: XOR<SearchCreateWithoutDestinationPortInput, SearchUncheckedCreateWithoutDestinationPortInput>
  }

  export type SearchUpdateWithWhereUniqueWithoutDestinationPortInput = {
    where: SearchWhereUniqueInput
    data: XOR<SearchUpdateWithoutDestinationPortInput, SearchUncheckedUpdateWithoutDestinationPortInput>
  }

  export type SearchUpdateManyWithWhereWithoutDestinationPortInput = {
    where: SearchScalarWhereInput
    data: XOR<SearchUpdateManyMutationInput, SearchUncheckedUpdateManyWithoutDestinationPortInput>
  }

  export type PortRotationUpsertWithWhereUniqueWithoutPortInput = {
    where: PortRotationWhereUniqueInput
    update: XOR<PortRotationUpdateWithoutPortInput, PortRotationUncheckedUpdateWithoutPortInput>
    create: XOR<PortRotationCreateWithoutPortInput, PortRotationUncheckedCreateWithoutPortInput>
  }

  export type PortRotationUpdateWithWhereUniqueWithoutPortInput = {
    where: PortRotationWhereUniqueInput
    data: XOR<PortRotationUpdateWithoutPortInput, PortRotationUncheckedUpdateWithoutPortInput>
  }

  export type PortRotationUpdateManyWithWhereWithoutPortInput = {
    where: PortRotationScalarWhereInput
    data: XOR<PortRotationUpdateManyMutationInput, PortRotationUncheckedUpdateManyWithoutPortInput>
  }

  export type PortRotationScalarWhereInput = {
    AND?: PortRotationScalarWhereInput | PortRotationScalarWhereInput[]
    OR?: PortRotationScalarWhereInput[]
    NOT?: PortRotationScalarWhereInput | PortRotationScalarWhereInput[]
    id?: StringFilter<"PortRotation"> | string
    searchResultId?: StringFilter<"PortRotation"> | string
    portId?: StringFilter<"PortRotation"> | string
    sequenceOrder?: IntFilter<"PortRotation"> | number
    arrivalDate?: DateTimeNullableFilter<"PortRotation"> | Date | string | null
    departureDate?: DateTimeNullableFilter<"PortRotation"> | Date | string | null
  }

  export type SearchResultCreateWithoutCarrierInput = {
    id?: string
    vesselName: string
    voyageNumber?: string | null
    departureDate: Date | string
    arrivalDate: Date | string
    transitDays: number
    rawApiResponse?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: Date | string
    search: SearchCreateNestedOneWithoutSearchResultsInput
    portRotations?: PortRotationCreateNestedManyWithoutSearchResultInput
  }

  export type SearchResultUncheckedCreateWithoutCarrierInput = {
    id?: string
    searchId: string
    vesselName: string
    voyageNumber?: string | null
    departureDate: Date | string
    arrivalDate: Date | string
    transitDays: number
    rawApiResponse?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: Date | string
    portRotations?: PortRotationUncheckedCreateNestedManyWithoutSearchResultInput
  }

  export type SearchResultCreateOrConnectWithoutCarrierInput = {
    where: SearchResultWhereUniqueInput
    create: XOR<SearchResultCreateWithoutCarrierInput, SearchResultUncheckedCreateWithoutCarrierInput>
  }

  export type SearchResultCreateManyCarrierInputEnvelope = {
    data: SearchResultCreateManyCarrierInput | SearchResultCreateManyCarrierInput[]
    skipDuplicates?: boolean
  }

  export type SearchResultUpsertWithWhereUniqueWithoutCarrierInput = {
    where: SearchResultWhereUniqueInput
    update: XOR<SearchResultUpdateWithoutCarrierInput, SearchResultUncheckedUpdateWithoutCarrierInput>
    create: XOR<SearchResultCreateWithoutCarrierInput, SearchResultUncheckedCreateWithoutCarrierInput>
  }

  export type SearchResultUpdateWithWhereUniqueWithoutCarrierInput = {
    where: SearchResultWhereUniqueInput
    data: XOR<SearchResultUpdateWithoutCarrierInput, SearchResultUncheckedUpdateWithoutCarrierInput>
  }

  export type SearchResultUpdateManyWithWhereWithoutCarrierInput = {
    where: SearchResultScalarWhereInput
    data: XOR<SearchResultUpdateManyMutationInput, SearchResultUncheckedUpdateManyWithoutCarrierInput>
  }

  export type SearchResultScalarWhereInput = {
    AND?: SearchResultScalarWhereInput | SearchResultScalarWhereInput[]
    OR?: SearchResultScalarWhereInput[]
    NOT?: SearchResultScalarWhereInput | SearchResultScalarWhereInput[]
    id?: StringFilter<"SearchResult"> | string
    searchId?: StringFilter<"SearchResult"> | string
    carrierId?: StringFilter<"SearchResult"> | string
    vesselName?: StringFilter<"SearchResult"> | string
    voyageNumber?: StringNullableFilter<"SearchResult"> | string | null
    departureDate?: DateTimeFilter<"SearchResult"> | Date | string
    arrivalDate?: DateTimeFilter<"SearchResult"> | Date | string
    transitDays?: IntFilter<"SearchResult"> | number
    rawApiResponse?: JsonNullableFilter<"SearchResult">
    createdAt?: DateTimeFilter<"SearchResult"> | Date | string
  }

  export type UserCreateWithoutSearchesInput = {
    id?: string
    email: string
    passwordHash: string
    firstName: string
    lastName: string
    companyName?: string | null
    isActive?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type UserUncheckedCreateWithoutSearchesInput = {
    id?: string
    email: string
    passwordHash: string
    firstName: string
    lastName: string
    companyName?: string | null
    isActive?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type UserCreateOrConnectWithoutSearchesInput = {
    where: UserWhereUniqueInput
    create: XOR<UserCreateWithoutSearchesInput, UserUncheckedCreateWithoutSearchesInput>
  }

  export type PortCreateWithoutOriginSearchesInput = {
    id?: string
    code: string
    name: string
    countryCode: string
    countryName: string
    latitude?: number | null
    longitude?: number | null
    isActive?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    destinationSearches?: SearchCreateNestedManyWithoutDestinationPortInput
    portRotations?: PortRotationCreateNestedManyWithoutPortInput
  }

  export type PortUncheckedCreateWithoutOriginSearchesInput = {
    id?: string
    code: string
    name: string
    countryCode: string
    countryName: string
    latitude?: number | null
    longitude?: number | null
    isActive?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    destinationSearches?: SearchUncheckedCreateNestedManyWithoutDestinationPortInput
    portRotations?: PortRotationUncheckedCreateNestedManyWithoutPortInput
  }

  export type PortCreateOrConnectWithoutOriginSearchesInput = {
    where: PortWhereUniqueInput
    create: XOR<PortCreateWithoutOriginSearchesInput, PortUncheckedCreateWithoutOriginSearchesInput>
  }

  export type PortCreateWithoutDestinationSearchesInput = {
    id?: string
    code: string
    name: string
    countryCode: string
    countryName: string
    latitude?: number | null
    longitude?: number | null
    isActive?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    originSearches?: SearchCreateNestedManyWithoutOriginPortInput
    portRotations?: PortRotationCreateNestedManyWithoutPortInput
  }

  export type PortUncheckedCreateWithoutDestinationSearchesInput = {
    id?: string
    code: string
    name: string
    countryCode: string
    countryName: string
    latitude?: number | null
    longitude?: number | null
    isActive?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    originSearches?: SearchUncheckedCreateNestedManyWithoutOriginPortInput
    portRotations?: PortRotationUncheckedCreateNestedManyWithoutPortInput
  }

  export type PortCreateOrConnectWithoutDestinationSearchesInput = {
    where: PortWhereUniqueInput
    create: XOR<PortCreateWithoutDestinationSearchesInput, PortUncheckedCreateWithoutDestinationSearchesInput>
  }

  export type SearchResultCreateWithoutSearchInput = {
    id?: string
    vesselName: string
    voyageNumber?: string | null
    departureDate: Date | string
    arrivalDate: Date | string
    transitDays: number
    rawApiResponse?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: Date | string
    carrier: CarrierCreateNestedOneWithoutSearchResultsInput
    portRotations?: PortRotationCreateNestedManyWithoutSearchResultInput
  }

  export type SearchResultUncheckedCreateWithoutSearchInput = {
    id?: string
    carrierId: string
    vesselName: string
    voyageNumber?: string | null
    departureDate: Date | string
    arrivalDate: Date | string
    transitDays: number
    rawApiResponse?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: Date | string
    portRotations?: PortRotationUncheckedCreateNestedManyWithoutSearchResultInput
  }

  export type SearchResultCreateOrConnectWithoutSearchInput = {
    where: SearchResultWhereUniqueInput
    create: XOR<SearchResultCreateWithoutSearchInput, SearchResultUncheckedCreateWithoutSearchInput>
  }

  export type SearchResultCreateManySearchInputEnvelope = {
    data: SearchResultCreateManySearchInput | SearchResultCreateManySearchInput[]
    skipDuplicates?: boolean
  }

  export type UserUpsertWithoutSearchesInput = {
    update: XOR<UserUpdateWithoutSearchesInput, UserUncheckedUpdateWithoutSearchesInput>
    create: XOR<UserCreateWithoutSearchesInput, UserUncheckedCreateWithoutSearchesInput>
    where?: UserWhereInput
  }

  export type UserUpdateToOneWithWhereWithoutSearchesInput = {
    where?: UserWhereInput
    data: XOR<UserUpdateWithoutSearchesInput, UserUncheckedUpdateWithoutSearchesInput>
  }

  export type UserUpdateWithoutSearchesInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    passwordHash?: StringFieldUpdateOperationsInput | string
    firstName?: StringFieldUpdateOperationsInput | string
    lastName?: StringFieldUpdateOperationsInput | string
    companyName?: NullableStringFieldUpdateOperationsInput | string | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type UserUncheckedUpdateWithoutSearchesInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    passwordHash?: StringFieldUpdateOperationsInput | string
    firstName?: StringFieldUpdateOperationsInput | string
    lastName?: StringFieldUpdateOperationsInput | string
    companyName?: NullableStringFieldUpdateOperationsInput | string | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type PortUpsertWithoutOriginSearchesInput = {
    update: XOR<PortUpdateWithoutOriginSearchesInput, PortUncheckedUpdateWithoutOriginSearchesInput>
    create: XOR<PortCreateWithoutOriginSearchesInput, PortUncheckedCreateWithoutOriginSearchesInput>
    where?: PortWhereInput
  }

  export type PortUpdateToOneWithWhereWithoutOriginSearchesInput = {
    where?: PortWhereInput
    data: XOR<PortUpdateWithoutOriginSearchesInput, PortUncheckedUpdateWithoutOriginSearchesInput>
  }

  export type PortUpdateWithoutOriginSearchesInput = {
    id?: StringFieldUpdateOperationsInput | string
    code?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    countryCode?: StringFieldUpdateOperationsInput | string
    countryName?: StringFieldUpdateOperationsInput | string
    latitude?: NullableFloatFieldUpdateOperationsInput | number | null
    longitude?: NullableFloatFieldUpdateOperationsInput | number | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    destinationSearches?: SearchUpdateManyWithoutDestinationPortNestedInput
    portRotations?: PortRotationUpdateManyWithoutPortNestedInput
  }

  export type PortUncheckedUpdateWithoutOriginSearchesInput = {
    id?: StringFieldUpdateOperationsInput | string
    code?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    countryCode?: StringFieldUpdateOperationsInput | string
    countryName?: StringFieldUpdateOperationsInput | string
    latitude?: NullableFloatFieldUpdateOperationsInput | number | null
    longitude?: NullableFloatFieldUpdateOperationsInput | number | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    destinationSearches?: SearchUncheckedUpdateManyWithoutDestinationPortNestedInput
    portRotations?: PortRotationUncheckedUpdateManyWithoutPortNestedInput
  }

  export type PortUpsertWithoutDestinationSearchesInput = {
    update: XOR<PortUpdateWithoutDestinationSearchesInput, PortUncheckedUpdateWithoutDestinationSearchesInput>
    create: XOR<PortCreateWithoutDestinationSearchesInput, PortUncheckedCreateWithoutDestinationSearchesInput>
    where?: PortWhereInput
  }

  export type PortUpdateToOneWithWhereWithoutDestinationSearchesInput = {
    where?: PortWhereInput
    data: XOR<PortUpdateWithoutDestinationSearchesInput, PortUncheckedUpdateWithoutDestinationSearchesInput>
  }

  export type PortUpdateWithoutDestinationSearchesInput = {
    id?: StringFieldUpdateOperationsInput | string
    code?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    countryCode?: StringFieldUpdateOperationsInput | string
    countryName?: StringFieldUpdateOperationsInput | string
    latitude?: NullableFloatFieldUpdateOperationsInput | number | null
    longitude?: NullableFloatFieldUpdateOperationsInput | number | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    originSearches?: SearchUpdateManyWithoutOriginPortNestedInput
    portRotations?: PortRotationUpdateManyWithoutPortNestedInput
  }

  export type PortUncheckedUpdateWithoutDestinationSearchesInput = {
    id?: StringFieldUpdateOperationsInput | string
    code?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    countryCode?: StringFieldUpdateOperationsInput | string
    countryName?: StringFieldUpdateOperationsInput | string
    latitude?: NullableFloatFieldUpdateOperationsInput | number | null
    longitude?: NullableFloatFieldUpdateOperationsInput | number | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    originSearches?: SearchUncheckedUpdateManyWithoutOriginPortNestedInput
    portRotations?: PortRotationUncheckedUpdateManyWithoutPortNestedInput
  }

  export type SearchResultUpsertWithWhereUniqueWithoutSearchInput = {
    where: SearchResultWhereUniqueInput
    update: XOR<SearchResultUpdateWithoutSearchInput, SearchResultUncheckedUpdateWithoutSearchInput>
    create: XOR<SearchResultCreateWithoutSearchInput, SearchResultUncheckedCreateWithoutSearchInput>
  }

  export type SearchResultUpdateWithWhereUniqueWithoutSearchInput = {
    where: SearchResultWhereUniqueInput
    data: XOR<SearchResultUpdateWithoutSearchInput, SearchResultUncheckedUpdateWithoutSearchInput>
  }

  export type SearchResultUpdateManyWithWhereWithoutSearchInput = {
    where: SearchResultScalarWhereInput
    data: XOR<SearchResultUpdateManyMutationInput, SearchResultUncheckedUpdateManyWithoutSearchInput>
  }

  export type SearchCreateWithoutSearchResultsInput = {
    id?: string
    departureDate: Date | string
    searchTimestamp?: Date | string
    resultsCount?: number
    searchDurationMs?: number | null
    user: UserCreateNestedOneWithoutSearchesInput
    originPort: PortCreateNestedOneWithoutOriginSearchesInput
    destinationPort: PortCreateNestedOneWithoutDestinationSearchesInput
  }

  export type SearchUncheckedCreateWithoutSearchResultsInput = {
    id?: string
    userId: string
    originPortId: string
    destinationPortId: string
    departureDate: Date | string
    searchTimestamp?: Date | string
    resultsCount?: number
    searchDurationMs?: number | null
  }

  export type SearchCreateOrConnectWithoutSearchResultsInput = {
    where: SearchWhereUniqueInput
    create: XOR<SearchCreateWithoutSearchResultsInput, SearchUncheckedCreateWithoutSearchResultsInput>
  }

  export type CarrierCreateWithoutSearchResultsInput = {
    id?: string
    name: string
    code: string
    apiEndpoint?: string | null
    apiKey?: string | null
    isActive?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type CarrierUncheckedCreateWithoutSearchResultsInput = {
    id?: string
    name: string
    code: string
    apiEndpoint?: string | null
    apiKey?: string | null
    isActive?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type CarrierCreateOrConnectWithoutSearchResultsInput = {
    where: CarrierWhereUniqueInput
    create: XOR<CarrierCreateWithoutSearchResultsInput, CarrierUncheckedCreateWithoutSearchResultsInput>
  }

  export type PortRotationCreateWithoutSearchResultInput = {
    id?: string
    sequenceOrder: number
    arrivalDate?: Date | string | null
    departureDate?: Date | string | null
    port: PortCreateNestedOneWithoutPortRotationsInput
  }

  export type PortRotationUncheckedCreateWithoutSearchResultInput = {
    id?: string
    portId: string
    sequenceOrder: number
    arrivalDate?: Date | string | null
    departureDate?: Date | string | null
  }

  export type PortRotationCreateOrConnectWithoutSearchResultInput = {
    where: PortRotationWhereUniqueInput
    create: XOR<PortRotationCreateWithoutSearchResultInput, PortRotationUncheckedCreateWithoutSearchResultInput>
  }

  export type PortRotationCreateManySearchResultInputEnvelope = {
    data: PortRotationCreateManySearchResultInput | PortRotationCreateManySearchResultInput[]
    skipDuplicates?: boolean
  }

  export type SearchUpsertWithoutSearchResultsInput = {
    update: XOR<SearchUpdateWithoutSearchResultsInput, SearchUncheckedUpdateWithoutSearchResultsInput>
    create: XOR<SearchCreateWithoutSearchResultsInput, SearchUncheckedCreateWithoutSearchResultsInput>
    where?: SearchWhereInput
  }

  export type SearchUpdateToOneWithWhereWithoutSearchResultsInput = {
    where?: SearchWhereInput
    data: XOR<SearchUpdateWithoutSearchResultsInput, SearchUncheckedUpdateWithoutSearchResultsInput>
  }

  export type SearchUpdateWithoutSearchResultsInput = {
    id?: StringFieldUpdateOperationsInput | string
    departureDate?: DateTimeFieldUpdateOperationsInput | Date | string
    searchTimestamp?: DateTimeFieldUpdateOperationsInput | Date | string
    resultsCount?: IntFieldUpdateOperationsInput | number
    searchDurationMs?: NullableIntFieldUpdateOperationsInput | number | null
    user?: UserUpdateOneRequiredWithoutSearchesNestedInput
    originPort?: PortUpdateOneRequiredWithoutOriginSearchesNestedInput
    destinationPort?: PortUpdateOneRequiredWithoutDestinationSearchesNestedInput
  }

  export type SearchUncheckedUpdateWithoutSearchResultsInput = {
    id?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    originPortId?: StringFieldUpdateOperationsInput | string
    destinationPortId?: StringFieldUpdateOperationsInput | string
    departureDate?: DateTimeFieldUpdateOperationsInput | Date | string
    searchTimestamp?: DateTimeFieldUpdateOperationsInput | Date | string
    resultsCount?: IntFieldUpdateOperationsInput | number
    searchDurationMs?: NullableIntFieldUpdateOperationsInput | number | null
  }

  export type CarrierUpsertWithoutSearchResultsInput = {
    update: XOR<CarrierUpdateWithoutSearchResultsInput, CarrierUncheckedUpdateWithoutSearchResultsInput>
    create: XOR<CarrierCreateWithoutSearchResultsInput, CarrierUncheckedCreateWithoutSearchResultsInput>
    where?: CarrierWhereInput
  }

  export type CarrierUpdateToOneWithWhereWithoutSearchResultsInput = {
    where?: CarrierWhereInput
    data: XOR<CarrierUpdateWithoutSearchResultsInput, CarrierUncheckedUpdateWithoutSearchResultsInput>
  }

  export type CarrierUpdateWithoutSearchResultsInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    code?: StringFieldUpdateOperationsInput | string
    apiEndpoint?: NullableStringFieldUpdateOperationsInput | string | null
    apiKey?: NullableStringFieldUpdateOperationsInput | string | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type CarrierUncheckedUpdateWithoutSearchResultsInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    code?: StringFieldUpdateOperationsInput | string
    apiEndpoint?: NullableStringFieldUpdateOperationsInput | string | null
    apiKey?: NullableStringFieldUpdateOperationsInput | string | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type PortRotationUpsertWithWhereUniqueWithoutSearchResultInput = {
    where: PortRotationWhereUniqueInput
    update: XOR<PortRotationUpdateWithoutSearchResultInput, PortRotationUncheckedUpdateWithoutSearchResultInput>
    create: XOR<PortRotationCreateWithoutSearchResultInput, PortRotationUncheckedCreateWithoutSearchResultInput>
  }

  export type PortRotationUpdateWithWhereUniqueWithoutSearchResultInput = {
    where: PortRotationWhereUniqueInput
    data: XOR<PortRotationUpdateWithoutSearchResultInput, PortRotationUncheckedUpdateWithoutSearchResultInput>
  }

  export type PortRotationUpdateManyWithWhereWithoutSearchResultInput = {
    where: PortRotationScalarWhereInput
    data: XOR<PortRotationUpdateManyMutationInput, PortRotationUncheckedUpdateManyWithoutSearchResultInput>
  }

  export type SearchResultCreateWithoutPortRotationsInput = {
    id?: string
    vesselName: string
    voyageNumber?: string | null
    departureDate: Date | string
    arrivalDate: Date | string
    transitDays: number
    rawApiResponse?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: Date | string
    search: SearchCreateNestedOneWithoutSearchResultsInput
    carrier: CarrierCreateNestedOneWithoutSearchResultsInput
  }

  export type SearchResultUncheckedCreateWithoutPortRotationsInput = {
    id?: string
    searchId: string
    carrierId: string
    vesselName: string
    voyageNumber?: string | null
    departureDate: Date | string
    arrivalDate: Date | string
    transitDays: number
    rawApiResponse?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: Date | string
  }

  export type SearchResultCreateOrConnectWithoutPortRotationsInput = {
    where: SearchResultWhereUniqueInput
    create: XOR<SearchResultCreateWithoutPortRotationsInput, SearchResultUncheckedCreateWithoutPortRotationsInput>
  }

  export type PortCreateWithoutPortRotationsInput = {
    id?: string
    code: string
    name: string
    countryCode: string
    countryName: string
    latitude?: number | null
    longitude?: number | null
    isActive?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    originSearches?: SearchCreateNestedManyWithoutOriginPortInput
    destinationSearches?: SearchCreateNestedManyWithoutDestinationPortInput
  }

  export type PortUncheckedCreateWithoutPortRotationsInput = {
    id?: string
    code: string
    name: string
    countryCode: string
    countryName: string
    latitude?: number | null
    longitude?: number | null
    isActive?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    originSearches?: SearchUncheckedCreateNestedManyWithoutOriginPortInput
    destinationSearches?: SearchUncheckedCreateNestedManyWithoutDestinationPortInput
  }

  export type PortCreateOrConnectWithoutPortRotationsInput = {
    where: PortWhereUniqueInput
    create: XOR<PortCreateWithoutPortRotationsInput, PortUncheckedCreateWithoutPortRotationsInput>
  }

  export type SearchResultUpsertWithoutPortRotationsInput = {
    update: XOR<SearchResultUpdateWithoutPortRotationsInput, SearchResultUncheckedUpdateWithoutPortRotationsInput>
    create: XOR<SearchResultCreateWithoutPortRotationsInput, SearchResultUncheckedCreateWithoutPortRotationsInput>
    where?: SearchResultWhereInput
  }

  export type SearchResultUpdateToOneWithWhereWithoutPortRotationsInput = {
    where?: SearchResultWhereInput
    data: XOR<SearchResultUpdateWithoutPortRotationsInput, SearchResultUncheckedUpdateWithoutPortRotationsInput>
  }

  export type SearchResultUpdateWithoutPortRotationsInput = {
    id?: StringFieldUpdateOperationsInput | string
    vesselName?: StringFieldUpdateOperationsInput | string
    voyageNumber?: NullableStringFieldUpdateOperationsInput | string | null
    departureDate?: DateTimeFieldUpdateOperationsInput | Date | string
    arrivalDate?: DateTimeFieldUpdateOperationsInput | Date | string
    transitDays?: IntFieldUpdateOperationsInput | number
    rawApiResponse?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    search?: SearchUpdateOneRequiredWithoutSearchResultsNestedInput
    carrier?: CarrierUpdateOneRequiredWithoutSearchResultsNestedInput
  }

  export type SearchResultUncheckedUpdateWithoutPortRotationsInput = {
    id?: StringFieldUpdateOperationsInput | string
    searchId?: StringFieldUpdateOperationsInput | string
    carrierId?: StringFieldUpdateOperationsInput | string
    vesselName?: StringFieldUpdateOperationsInput | string
    voyageNumber?: NullableStringFieldUpdateOperationsInput | string | null
    departureDate?: DateTimeFieldUpdateOperationsInput | Date | string
    arrivalDate?: DateTimeFieldUpdateOperationsInput | Date | string
    transitDays?: IntFieldUpdateOperationsInput | number
    rawApiResponse?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type PortUpsertWithoutPortRotationsInput = {
    update: XOR<PortUpdateWithoutPortRotationsInput, PortUncheckedUpdateWithoutPortRotationsInput>
    create: XOR<PortCreateWithoutPortRotationsInput, PortUncheckedCreateWithoutPortRotationsInput>
    where?: PortWhereInput
  }

  export type PortUpdateToOneWithWhereWithoutPortRotationsInput = {
    where?: PortWhereInput
    data: XOR<PortUpdateWithoutPortRotationsInput, PortUncheckedUpdateWithoutPortRotationsInput>
  }

  export type PortUpdateWithoutPortRotationsInput = {
    id?: StringFieldUpdateOperationsInput | string
    code?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    countryCode?: StringFieldUpdateOperationsInput | string
    countryName?: StringFieldUpdateOperationsInput | string
    latitude?: NullableFloatFieldUpdateOperationsInput | number | null
    longitude?: NullableFloatFieldUpdateOperationsInput | number | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    originSearches?: SearchUpdateManyWithoutOriginPortNestedInput
    destinationSearches?: SearchUpdateManyWithoutDestinationPortNestedInput
  }

  export type PortUncheckedUpdateWithoutPortRotationsInput = {
    id?: StringFieldUpdateOperationsInput | string
    code?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    countryCode?: StringFieldUpdateOperationsInput | string
    countryName?: StringFieldUpdateOperationsInput | string
    latitude?: NullableFloatFieldUpdateOperationsInput | number | null
    longitude?: NullableFloatFieldUpdateOperationsInput | number | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    originSearches?: SearchUncheckedUpdateManyWithoutOriginPortNestedInput
    destinationSearches?: SearchUncheckedUpdateManyWithoutDestinationPortNestedInput
  }

  export type SearchCreateManyUserInput = {
    id?: string
    originPortId: string
    destinationPortId: string
    departureDate: Date | string
    searchTimestamp?: Date | string
    resultsCount?: number
    searchDurationMs?: number | null
  }

  export type SearchUpdateWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    departureDate?: DateTimeFieldUpdateOperationsInput | Date | string
    searchTimestamp?: DateTimeFieldUpdateOperationsInput | Date | string
    resultsCount?: IntFieldUpdateOperationsInput | number
    searchDurationMs?: NullableIntFieldUpdateOperationsInput | number | null
    originPort?: PortUpdateOneRequiredWithoutOriginSearchesNestedInput
    destinationPort?: PortUpdateOneRequiredWithoutDestinationSearchesNestedInput
    searchResults?: SearchResultUpdateManyWithoutSearchNestedInput
  }

  export type SearchUncheckedUpdateWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    originPortId?: StringFieldUpdateOperationsInput | string
    destinationPortId?: StringFieldUpdateOperationsInput | string
    departureDate?: DateTimeFieldUpdateOperationsInput | Date | string
    searchTimestamp?: DateTimeFieldUpdateOperationsInput | Date | string
    resultsCount?: IntFieldUpdateOperationsInput | number
    searchDurationMs?: NullableIntFieldUpdateOperationsInput | number | null
    searchResults?: SearchResultUncheckedUpdateManyWithoutSearchNestedInput
  }

  export type SearchUncheckedUpdateManyWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    originPortId?: StringFieldUpdateOperationsInput | string
    destinationPortId?: StringFieldUpdateOperationsInput | string
    departureDate?: DateTimeFieldUpdateOperationsInput | Date | string
    searchTimestamp?: DateTimeFieldUpdateOperationsInput | Date | string
    resultsCount?: IntFieldUpdateOperationsInput | number
    searchDurationMs?: NullableIntFieldUpdateOperationsInput | number | null
  }

  export type SearchCreateManyOriginPortInput = {
    id?: string
    userId: string
    destinationPortId: string
    departureDate: Date | string
    searchTimestamp?: Date | string
    resultsCount?: number
    searchDurationMs?: number | null
  }

  export type SearchCreateManyDestinationPortInput = {
    id?: string
    userId: string
    originPortId: string
    departureDate: Date | string
    searchTimestamp?: Date | string
    resultsCount?: number
    searchDurationMs?: number | null
  }

  export type PortRotationCreateManyPortInput = {
    id?: string
    searchResultId: string
    sequenceOrder: number
    arrivalDate?: Date | string | null
    departureDate?: Date | string | null
  }

  export type SearchUpdateWithoutOriginPortInput = {
    id?: StringFieldUpdateOperationsInput | string
    departureDate?: DateTimeFieldUpdateOperationsInput | Date | string
    searchTimestamp?: DateTimeFieldUpdateOperationsInput | Date | string
    resultsCount?: IntFieldUpdateOperationsInput | number
    searchDurationMs?: NullableIntFieldUpdateOperationsInput | number | null
    user?: UserUpdateOneRequiredWithoutSearchesNestedInput
    destinationPort?: PortUpdateOneRequiredWithoutDestinationSearchesNestedInput
    searchResults?: SearchResultUpdateManyWithoutSearchNestedInput
  }

  export type SearchUncheckedUpdateWithoutOriginPortInput = {
    id?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    destinationPortId?: StringFieldUpdateOperationsInput | string
    departureDate?: DateTimeFieldUpdateOperationsInput | Date | string
    searchTimestamp?: DateTimeFieldUpdateOperationsInput | Date | string
    resultsCount?: IntFieldUpdateOperationsInput | number
    searchDurationMs?: NullableIntFieldUpdateOperationsInput | number | null
    searchResults?: SearchResultUncheckedUpdateManyWithoutSearchNestedInput
  }

  export type SearchUncheckedUpdateManyWithoutOriginPortInput = {
    id?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    destinationPortId?: StringFieldUpdateOperationsInput | string
    departureDate?: DateTimeFieldUpdateOperationsInput | Date | string
    searchTimestamp?: DateTimeFieldUpdateOperationsInput | Date | string
    resultsCount?: IntFieldUpdateOperationsInput | number
    searchDurationMs?: NullableIntFieldUpdateOperationsInput | number | null
  }

  export type SearchUpdateWithoutDestinationPortInput = {
    id?: StringFieldUpdateOperationsInput | string
    departureDate?: DateTimeFieldUpdateOperationsInput | Date | string
    searchTimestamp?: DateTimeFieldUpdateOperationsInput | Date | string
    resultsCount?: IntFieldUpdateOperationsInput | number
    searchDurationMs?: NullableIntFieldUpdateOperationsInput | number | null
    user?: UserUpdateOneRequiredWithoutSearchesNestedInput
    originPort?: PortUpdateOneRequiredWithoutOriginSearchesNestedInput
    searchResults?: SearchResultUpdateManyWithoutSearchNestedInput
  }

  export type SearchUncheckedUpdateWithoutDestinationPortInput = {
    id?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    originPortId?: StringFieldUpdateOperationsInput | string
    departureDate?: DateTimeFieldUpdateOperationsInput | Date | string
    searchTimestamp?: DateTimeFieldUpdateOperationsInput | Date | string
    resultsCount?: IntFieldUpdateOperationsInput | number
    searchDurationMs?: NullableIntFieldUpdateOperationsInput | number | null
    searchResults?: SearchResultUncheckedUpdateManyWithoutSearchNestedInput
  }

  export type SearchUncheckedUpdateManyWithoutDestinationPortInput = {
    id?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
    originPortId?: StringFieldUpdateOperationsInput | string
    departureDate?: DateTimeFieldUpdateOperationsInput | Date | string
    searchTimestamp?: DateTimeFieldUpdateOperationsInput | Date | string
    resultsCount?: IntFieldUpdateOperationsInput | number
    searchDurationMs?: NullableIntFieldUpdateOperationsInput | number | null
  }

  export type PortRotationUpdateWithoutPortInput = {
    id?: StringFieldUpdateOperationsInput | string
    sequenceOrder?: IntFieldUpdateOperationsInput | number
    arrivalDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    departureDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    searchResult?: SearchResultUpdateOneRequiredWithoutPortRotationsNestedInput
  }

  export type PortRotationUncheckedUpdateWithoutPortInput = {
    id?: StringFieldUpdateOperationsInput | string
    searchResultId?: StringFieldUpdateOperationsInput | string
    sequenceOrder?: IntFieldUpdateOperationsInput | number
    arrivalDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    departureDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  }

  export type PortRotationUncheckedUpdateManyWithoutPortInput = {
    id?: StringFieldUpdateOperationsInput | string
    searchResultId?: StringFieldUpdateOperationsInput | string
    sequenceOrder?: IntFieldUpdateOperationsInput | number
    arrivalDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    departureDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  }

  export type SearchResultCreateManyCarrierInput = {
    id?: string
    searchId: string
    vesselName: string
    voyageNumber?: string | null
    departureDate: Date | string
    arrivalDate: Date | string
    transitDays: number
    rawApiResponse?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: Date | string
  }

  export type SearchResultUpdateWithoutCarrierInput = {
    id?: StringFieldUpdateOperationsInput | string
    vesselName?: StringFieldUpdateOperationsInput | string
    voyageNumber?: NullableStringFieldUpdateOperationsInput | string | null
    departureDate?: DateTimeFieldUpdateOperationsInput | Date | string
    arrivalDate?: DateTimeFieldUpdateOperationsInput | Date | string
    transitDays?: IntFieldUpdateOperationsInput | number
    rawApiResponse?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    search?: SearchUpdateOneRequiredWithoutSearchResultsNestedInput
    portRotations?: PortRotationUpdateManyWithoutSearchResultNestedInput
  }

  export type SearchResultUncheckedUpdateWithoutCarrierInput = {
    id?: StringFieldUpdateOperationsInput | string
    searchId?: StringFieldUpdateOperationsInput | string
    vesselName?: StringFieldUpdateOperationsInput | string
    voyageNumber?: NullableStringFieldUpdateOperationsInput | string | null
    departureDate?: DateTimeFieldUpdateOperationsInput | Date | string
    arrivalDate?: DateTimeFieldUpdateOperationsInput | Date | string
    transitDays?: IntFieldUpdateOperationsInput | number
    rawApiResponse?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    portRotations?: PortRotationUncheckedUpdateManyWithoutSearchResultNestedInput
  }

  export type SearchResultUncheckedUpdateManyWithoutCarrierInput = {
    id?: StringFieldUpdateOperationsInput | string
    searchId?: StringFieldUpdateOperationsInput | string
    vesselName?: StringFieldUpdateOperationsInput | string
    voyageNumber?: NullableStringFieldUpdateOperationsInput | string | null
    departureDate?: DateTimeFieldUpdateOperationsInput | Date | string
    arrivalDate?: DateTimeFieldUpdateOperationsInput | Date | string
    transitDays?: IntFieldUpdateOperationsInput | number
    rawApiResponse?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type SearchResultCreateManySearchInput = {
    id?: string
    carrierId: string
    vesselName: string
    voyageNumber?: string | null
    departureDate: Date | string
    arrivalDate: Date | string
    transitDays: number
    rawApiResponse?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: Date | string
  }

  export type SearchResultUpdateWithoutSearchInput = {
    id?: StringFieldUpdateOperationsInput | string
    vesselName?: StringFieldUpdateOperationsInput | string
    voyageNumber?: NullableStringFieldUpdateOperationsInput | string | null
    departureDate?: DateTimeFieldUpdateOperationsInput | Date | string
    arrivalDate?: DateTimeFieldUpdateOperationsInput | Date | string
    transitDays?: IntFieldUpdateOperationsInput | number
    rawApiResponse?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    carrier?: CarrierUpdateOneRequiredWithoutSearchResultsNestedInput
    portRotations?: PortRotationUpdateManyWithoutSearchResultNestedInput
  }

  export type SearchResultUncheckedUpdateWithoutSearchInput = {
    id?: StringFieldUpdateOperationsInput | string
    carrierId?: StringFieldUpdateOperationsInput | string
    vesselName?: StringFieldUpdateOperationsInput | string
    voyageNumber?: NullableStringFieldUpdateOperationsInput | string | null
    departureDate?: DateTimeFieldUpdateOperationsInput | Date | string
    arrivalDate?: DateTimeFieldUpdateOperationsInput | Date | string
    transitDays?: IntFieldUpdateOperationsInput | number
    rawApiResponse?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    portRotations?: PortRotationUncheckedUpdateManyWithoutSearchResultNestedInput
  }

  export type SearchResultUncheckedUpdateManyWithoutSearchInput = {
    id?: StringFieldUpdateOperationsInput | string
    carrierId?: StringFieldUpdateOperationsInput | string
    vesselName?: StringFieldUpdateOperationsInput | string
    voyageNumber?: NullableStringFieldUpdateOperationsInput | string | null
    departureDate?: DateTimeFieldUpdateOperationsInput | Date | string
    arrivalDate?: DateTimeFieldUpdateOperationsInput | Date | string
    transitDays?: IntFieldUpdateOperationsInput | number
    rawApiResponse?: NullableJsonNullValueInput | InputJsonValue
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type PortRotationCreateManySearchResultInput = {
    id?: string
    portId: string
    sequenceOrder: number
    arrivalDate?: Date | string | null
    departureDate?: Date | string | null
  }

  export type PortRotationUpdateWithoutSearchResultInput = {
    id?: StringFieldUpdateOperationsInput | string
    sequenceOrder?: IntFieldUpdateOperationsInput | number
    arrivalDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    departureDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    port?: PortUpdateOneRequiredWithoutPortRotationsNestedInput
  }

  export type PortRotationUncheckedUpdateWithoutSearchResultInput = {
    id?: StringFieldUpdateOperationsInput | string
    portId?: StringFieldUpdateOperationsInput | string
    sequenceOrder?: IntFieldUpdateOperationsInput | number
    arrivalDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    departureDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  }

  export type PortRotationUncheckedUpdateManyWithoutSearchResultInput = {
    id?: StringFieldUpdateOperationsInput | string
    portId?: StringFieldUpdateOperationsInput | string
    sequenceOrder?: IntFieldUpdateOperationsInput | number
    arrivalDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    departureDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  }



  /**
   * Batch Payload for updateMany & deleteMany & createMany
   */

  export type BatchPayload = {
    count: number
  }

  /**
   * DMMF
   */
  export const dmmf: runtime.BaseDMMF
}