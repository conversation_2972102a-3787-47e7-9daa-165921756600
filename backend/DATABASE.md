# Digital Freight Platform - Database Documentation

## 📊 Database Schema Overview

The Digital Freight Platform uses PostgreSQL with Prisma ORM. The database is designed to handle freight forwarding operations including user management, port data, carrier information, search queries, and shipping results.

## 🏗️ Database Schema

### Core Tables

#### Users
- **Purpose**: Freight forwarder user accounts
- **Key Fields**: email, password_hash, company_name
- **Relations**: One-to-many with Searches

#### Ports
- **Purpose**: Master data for worldwide shipping ports
- **Key Fields**: code (SHG, RTM, LAX), name, country, coordinates
- **Relations**: Many-to-many with Searches, Port Rotations

#### Carriers
- **Purpose**: Shipping carrier information
- **Key Fields**: name (Maersk, Hapag-Lloyd), code, API endpoints
- **Relations**: One-to-many with Search Results

#### Searches
- **Purpose**: User search queries for analytics and caching
- **Key Fields**: origin/destination ports, departure date, user
- **Relations**: Belongs to User, has many Search Results

#### Search Results
- **Purpose**: Shipping rotation results from carrier APIs
- **Key Fields**: vessel info, dates, transit time, raw API response
- **Relations**: Belongs to Search and Carrier, has many Port Rotations

#### Port Rotations
- **Purpose**: Sequence of ports in shipping routes
- **Key Fields**: port, sequence order, arrival/departure dates
- **Relations**: Belongs to Search Result and Port

## 🚀 Setup Instructions

### Option 1: Docker (Recommended for Development)

1. **Start the database services:**
   ```bash
   docker-compose up -d postgres redis
   ```

2. **Run database migrations:**
   ```bash
   cd backend
   npm run db:migrate
   ```

3. **Seed initial data:**
   ```bash
   npm run db:seed
   ```

4. **Access pgAdmin (optional):**
   - URL: http://localhost:5050
   - Email: <EMAIL>
   - Password: admin

### Option 2: Local PostgreSQL Installation

1. **Install PostgreSQL:**
   - macOS: `brew install postgresql`
   - Ubuntu: `sudo apt-get install postgresql postgresql-contrib`
   - Windows: Download from https://www.postgresql.org/download/

2. **Run setup script:**
   ```bash
   cd backend
   ./scripts/setup-db.sh
   ```

3. **Run migrations and seed:**
   ```bash
   npm run db:migrate
   npm run db:seed
   ```

## 🛠️ Available Commands

```bash
# Generate Prisma client
npm run db:generate

# Create and run migrations
npm run db:migrate

# Seed database with initial data
npm run db:seed

# Open Prisma Studio (database GUI)
npm run db:studio

# Reset database (WARNING: Deletes all data)
npm run db:reset
```

## 📈 Database Monitoring

### Health Check
The `/health` endpoint provides database connection status and basic statistics.

### Prisma Studio
Access the database GUI at http://localhost:5555 when running `npm run db:studio`

## 🔧 Environment Variables

```env
DATABASE_URL="postgresql://postgres:password@localhost:5432/digital_freight?schema=public"
DB_HOST=localhost
DB_PORT=5432
DB_NAME=digital_freight
DB_USER=postgres
DB_PASSWORD=password
```

## 📝 Migration Workflow

1. **Modify schema:** Edit `prisma/schema.prisma`
2. **Generate migration:** `npm run db:migrate`
3. **Apply to production:** `prisma migrate deploy`

## 🔍 Troubleshooting

### Connection Issues
- Ensure PostgreSQL is running: `pg_isready`
- Check connection string in `.env`
- Verify database exists: `psql -l`

### Migration Issues
- Reset database: `npm run db:reset`
- Check migration status: `prisma migrate status`
- Resolve conflicts manually if needed

## 🚨 Production Considerations

- Use connection pooling (PgBouncer)
- Set up read replicas for heavy read workloads
- Implement proper backup strategy
- Monitor query performance
- Use environment-specific connection strings
