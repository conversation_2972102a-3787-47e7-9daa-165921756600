'use client';

import React, { useState } from 'react';
import {
  TruckIcon,
  ClockIcon,
  MapPinIcon,
  EyeIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';

export interface SearchResult {
  id: string;
  carrier: {
    name: string;
    code: string;
  };
  vesselName: string;
  voyageNumber: string;
  serviceName?: string;
  departureDate: string;
  arrivalDate: string;
  transitDays: number;
  portRotation: Array<{
    portCode: string;
    portName: string;
    country: string;
    sequenceOrder: number;
    arrivalDate?: string;
    departureDate?: string;
  }>;
  rawApiResponse?: any;
}

export interface SearchResponse {
  searchId: string;
  results: SearchResult[];
  totalResults: number;
  searchDurationMs: number;
  carriers: string[];
  metadata: {
    originPort: {
      code: string;
      name: string;
      country: string;
    };
    destinationPort: {
      code: string;
      name: string;
      country: string;
    };
    departureDate: string;
    searchTimestamp: string;
  };
}

interface SearchResultsProps {
  searchResponse: SearchResponse | null;
  isLoading: boolean;
  onViewDetails?: (result: SearchResult) => void;
}

const SearchResults: React.FC<SearchResultsProps> = ({
  searchResponse,
  isLoading,
  onViewDetails,
}) => {
  const [selectedResult, setSelectedResult] = useState<SearchResult | null>(null);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    return `${(ms / 60000).toFixed(1)}min`;
  };

  const handleViewDetails = (result: SearchResult) => {
    setSelectedResult(result);
    onViewDetails?.(result);
  };

  const closeDetails = () => {
    setSelectedResult(null);
  };

  const getCarrierColor = (carrierCode: string) => {
    const colors: Record<string, string> = {
      MAEU: 'bg-blue-600 text-white',
      HLCU: 'bg-orange-600 text-white',
      CMDU: 'bg-green-600 text-white',
      MSCU: 'bg-purple-600 text-white',
    };
    return colors[carrierCode] || 'bg-gray-600 text-white';
  };

  if (isLoading) {
    return (
      <div className="text-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Searching across multiple carriers...</p>
        <p className="text-sm text-gray-500 mt-2">This may take a few moments</p>
      </div>
    );
  }

  if (!searchResponse) {
    return (
      <div className="text-center py-12">
        <TruckIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Ready to Search</h3>
        <p className="text-gray-600">
          Enter your search criteria above to find shipping routes.
        </p>
      </div>
    );
  }

  if (searchResponse.results.length === 0) {
    return (
      <div className="text-center py-12">
        <TruckIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Routes Found</h3>
        <p className="text-gray-600">
          No shipping schedules found for your search criteria. Try different ports or dates.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Search Summary */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="mb-4">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Search Results ({searchResponse.totalResults})
          </h2>
          <p className="text-gray-600 mb-1">
            <span className="font-medium">{searchResponse.metadata.originPort.name}</span> ({searchResponse.metadata.originPort.code}) → {' '}
            <span className="font-medium">{searchResponse.metadata.destinationPort.name}</span> ({searchResponse.metadata.destinationPort.code})
          </p>
          <p className="text-sm text-gray-500">
            Departure: {formatDate(searchResponse.metadata.departureDate)} •
            Search completed in {formatDuration(searchResponse.searchDurationMs)} •
            {searchResponse.carriers.length} carrier(s)
          </p>
        </div>
      </div>

      {/* Results List */}
      <div className="space-y-4">
        {searchResponse.results.map((result) => (
          <div key={result.id} className="bg-white rounded-lg shadow hover:shadow-md transition-shadow duration-200">
            <div className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${getCarrierColor(result.carrier.code)}`}>
                      {result.carrier.name}
                    </span>
                    {result.serviceName && (
                      <span className="px-2 py-1 bg-gray-100 text-gray-700 rounded text-sm">
                        {result.serviceName}
                      </span>
                    )}
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-1">
                    {result.vesselName}
                  </h3>
                  <p className="text-gray-600">
                    Voyage: {result.voyageNumber}
                  </p>
                </div>
                <button
                  onClick={() => handleViewDetails(result)}
                  className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200"
                >
                  <EyeIcon className="h-4 w-4 mr-2" />
                  View Details
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex items-center space-x-2">
                  <MapPinIcon className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Departure</p>
                    <p className="text-sm text-gray-600">{formatDate(result.departureDate)}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <MapPinIcon className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Arrival</p>
                    <p className="text-sm text-gray-600">{formatDate(result.arrivalDate)}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <ClockIcon className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Transit Time</p>
                    <p className="text-sm text-gray-600">{result.transitDays} days</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Details Modal */}
      {selectedResult && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-gray-900">Route Details</h2>
                <button
                  onClick={closeDetails}
                  className="p-2 hover:bg-gray-100 rounded-full transition-colors duration-200"
                >
                  <XMarkIcon className="h-6 w-6 text-gray-500" />
                </button>
              </div>

              <div className="space-y-6">
                {/* Vessel Information */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Vessel Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm font-medium text-gray-700">Vessel Name</p>
                      <p className="text-lg text-gray-900">{selectedResult.vesselName}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-700">Voyage Number</p>
                      <p className="text-lg text-gray-900">{selectedResult.voyageNumber}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-700">Carrier</p>
                      <p className="text-lg text-gray-900">{selectedResult.carrier.name}</p>
                    </div>
                    {selectedResult.serviceName && (
                      <div>
                        <p className="text-sm font-medium text-gray-700">Service</p>
                        <p className="text-lg text-gray-900">{selectedResult.serviceName}</p>
                      </div>
                    )}
                  </div>
                </div>

                {/* Schedule Information */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Schedule</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <p className="text-sm font-medium text-gray-700">Departure Date</p>
                      <p className="text-lg text-gray-900">{formatDate(selectedResult.departureDate)}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-700">Arrival Date</p>
                      <p className="text-lg text-gray-900">{formatDate(selectedResult.arrivalDate)}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-700">Transit Time</p>
                      <p className="text-lg text-gray-900">{selectedResult.transitDays} days</p>
                    </div>
                  </div>
                </div>

                {/* Port Rotation */}
                {selectedResult.portRotation && selectedResult.portRotation.length > 0 && (
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h3 className="text-lg font-semibold text-gray-900 mb-3">Port Rotation</h3>
                    <div className="space-y-3">
                      {selectedResult.portRotation.map((port, index) => (
                        <div key={index} className="flex items-center justify-between p-3 bg-white rounded border">
                          <div>
                            <p className="font-medium text-gray-900">{port.portName}</p>
                            <p className="text-sm text-gray-600">{port.portCode} • {port.country}</p>
                          </div>
                          <div className="text-right">
                            {port.arrivalDate && (
                              <p className="text-sm text-gray-600">Arrival: {formatDate(port.arrivalDate)}</p>
                            )}
                            {port.departureDate && (
                              <p className="text-sm text-gray-600">Departure: {formatDate(port.departureDate)}</p>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );

