'use client';

import React, { useState } from 'react';
import { 
  TruckIcon, 
  ClockIcon, 
  MapPinIcon, 
  ArrowDownTrayIcon,
  FunnelIcon,
  ArrowsUpDownIcon,
  EyeIcon,
  BookmarkIcon
} from '@heroicons/react/24/outline';

export interface SearchResult {
  id: string;
  carrier: {
    name: string;
    code: string;
  };
  vesselName: string;
  voyageNumber: string;
  serviceName?: string;
  departureDate: string;
  arrivalDate: string;
  transitDays: number;
  portRotation: Array<{
    portCode: string;
    portName: string;
    country: string;
    sequenceOrder: number;
    arrivalDate?: string;
    departureDate?: string;
  }>;
}

export interface SearchResponse {
  searchId: string;
  results: SearchResult[];
  totalResults: number;
  searchDurationMs: number;
  carriers: string[];
  metadata: {
    originPort: {
      code: string;
      name: string;
      country: string;
    };
    destinationPort: {
      code: string;
      name: string;
      country: string;
    };
    departureDate: string;
    searchTimestamp: string;
  };
}

interface SearchResultsProps {
  searchResponse: SearchResponse | null;
  isLoading: boolean;
  onExport?: (searchId: string, format: string) => void;
  onViewDetails?: (result: SearchResult) => void;
  onSaveSearch?: (searchId: string) => void;
}

type SortField = 'departureDate' | 'arrivalDate' | 'transitDays' | 'carrier';
type SortOrder = 'asc' | 'desc';

const SearchResults: React.FC<SearchResultsProps> = ({
  searchResponse,
  isLoading,
  onExport,
  onViewDetails,
  onSaveSearch,
}) => {
  const [sortField, setSortField] = useState<SortField>('departureDate');
  const [sortOrder, setSortOrder] = useState<SortOrder>('asc');
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    maxTransitDays: '',
    minTransitDays: '',
    carriers: [] as string[],
  });

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    return `${(ms / 60000).toFixed(1)}min`;
  };

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortOrder('asc');
    }
  };

  const getSortedResults = () => {
    if (!searchResponse) return [];
    
    const results = [...searchResponse.results];
    
    results.sort((a, b) => {
      let aValue: any, bValue: any;

      switch (sortField) {
        case 'departureDate':
          aValue = new Date(a.departureDate).getTime();
          bValue = new Date(b.departureDate).getTime();
          break;
        case 'arrivalDate':
          aValue = new Date(a.arrivalDate).getTime();
          bValue = new Date(b.arrivalDate).getTime();
          break;
        case 'transitDays':
          aValue = a.transitDays;
          bValue = b.transitDays;
          break;
        case 'carrier':
          aValue = a.carrier.name;
          bValue = b.carrier.name;
          break;
        default:
          return 0;
      }

      if (sortOrder === 'desc') {
        return bValue > aValue ? 1 : bValue < aValue ? -1 : 0;
      } else {
        return aValue > bValue ? 1 : aValue < bValue ? -1 : 0;
      }
    });

    return results;
  };

  const getCarrierColor = (carrierCode: string) => {
    const colors: Record<string, string> = {
      MAEU: 'bg-blue-100 text-blue-800',
      HLCU: 'bg-orange-100 text-orange-800',
      CMDU: 'bg-green-100 text-green-800',
      MSCU: 'bg-purple-100 text-purple-800',
    };
    return colors[carrierCode] || 'bg-gray-100 text-gray-800';
  };

  if (isLoading) {
    return (
      <div className="text-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Searching across multiple carriers...</p>
        <p className="text-sm text-gray-500 mt-2">This may take a few moments</p>
      </div>
    );
  }

  if (!searchResponse) {
    return (
      <div className="text-center py-12">
        <TruckIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Ready to Search</h3>
        <p className="text-gray-600">
          Enter your search criteria above to find shipping routes.
        </p>
      </div>
    );
  }

  const sortedResults = getSortedResults();

  return (
    <div className="space-y-6">
      {/* Search Summary */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">
              Search Results ({searchResponse.totalResults})
            </h2>
            <p className="text-gray-600">
              {searchResponse.metadata.originPort.code} ({searchResponse.metadata.originPort.name}) → {' '}
              {searchResponse.metadata.destinationPort.code} ({searchResponse.metadata.destinationPort.name})
            </p>
            <p className="text-sm text-gray-500">
              Departure: {formatDate(searchResponse.metadata.departureDate)} • 
              Search completed in {formatDuration(searchResponse.searchDurationMs)} • 
              {searchResponse.carriers.length} carrier(s)
            </p>
          </div>
          
          <div className="flex items-center space-x-3">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
            >
              <FunnelIcon className="h-4 w-4 mr-2" />
              Filters
            </button>
            
            {onExport && (
              <button
                onClick={() => onExport(searchResponse.searchId, 'csv')}
                className="flex items-center px-3 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
              >
                <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                Export CSV
              </button>
            )}
          </div>
        </div>

        {/* Carrier Summary */}
        <div className="flex flex-wrap gap-2">
          {searchResponse.carriers.map(carrierCode => (
            <span
              key={carrierCode}
              className={`px-2 py-1 text-xs font-medium rounded-full ${getCarrierColor(carrierCode)}`}
            >
              {carrierCode}
            </span>
          ))}
        </div>
      </div>

      {/* Filters Panel */}
      {showFilters && (
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Filter Results</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Min Transit Days
              </label>
              <input
                type="number"
                value={filters.minTransitDays}
                onChange={(e) => setFilters(prev => ({ ...prev, minTransitDays: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="e.g., 20"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Max Transit Days
              </label>
              <input
                type="number"
                value={filters.maxTransitDays}
                onChange={(e) => setFilters(prev => ({ ...prev, maxTransitDays: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="e.g., 40"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Carriers
              </label>
              <select
                multiple
                value={filters.carriers}
                onChange={(e) => setFilters(prev => ({ 
                  ...prev, 
                  carriers: Array.from(e.target.selectedOptions, option => option.value)
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {searchResponse.carriers.map(carrier => (
                  <option key={carrier} value={carrier}>{carrier}</option>
                ))}
              </select>
            </div>
          </div>
        </div>
      )}

      {/* Sort Controls */}
      <div className="flex items-center space-x-4 text-sm">
        <span className="text-gray-600">Sort by:</span>
        {[
          { field: 'departureDate' as SortField, label: 'Departure Date' },
          { field: 'arrivalDate' as SortField, label: 'Arrival Date' },
          { field: 'transitDays' as SortField, label: 'Transit Time' },
          { field: 'carrier' as SortField, label: 'Carrier' },
        ].map(({ field, label }) => (
          <button
            key={field}
            onClick={() => handleSort(field)}
            className={`flex items-center px-3 py-1 rounded-md transition-colors ${
              sortField === field
                ? 'bg-blue-100 text-blue-700'
                : 'text-gray-600 hover:bg-gray-100'
            }`}
          >
            {label}
            {sortField === field && (
              <ArrowsUpDownIcon className={`h-3 w-3 ml-1 ${sortOrder === 'desc' ? 'rotate-180' : ''}`} />
            )}
          </button>
        ))}
      </div>

      {/* Results List */}
      {sortedResults.length === 0 ? (
        <div className="text-center py-12 bg-white rounded-lg shadow">
          <TruckIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No routes found</h3>
          <p className="text-gray-600">
            Try adjusting your search criteria or selecting different ports.
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {sortedResults.map((result) => (
            <div key={result.id} className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getCarrierColor(result.carrier.code)}`}>
                      {result.carrier.code}
                    </span>
                    <h3 className="text-lg font-semibold text-gray-900">
                      {result.carrier.name}
                    </h3>
                  </div>
                  <p className="text-gray-600">
                    {result.vesselName} • {result.voyageNumber}
                    {result.serviceName && ` • ${result.serviceName}`}
                  </p>
                </div>
                
                <div className="text-right">
                  <div className="flex items-center text-sm text-gray-500 mb-1">
                    <ClockIcon className="h-4 w-4 mr-1" />
                    {result.transitDays} days
                  </div>
                  <div className="text-lg font-semibold text-gray-900">
                    {formatDate(result.departureDate)} → {formatDate(result.arrivalDate)}
                  </div>
                </div>
              </div>

              {/* Port Rotation */}
              <div className="border-t pt-4">
                <h4 className="text-sm font-medium text-gray-700 mb-3">Port Rotation</h4>
                <div className="flex items-center space-x-2 overflow-x-auto pb-2">
                  {result.portRotation.map((port, index) => (
                    <React.Fragment key={`${port.portCode}-${port.sequenceOrder}`}>
                      <div className="flex-shrink-0 text-center">
                        <div className="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-full mb-1">
                          <MapPinIcon className="h-4 w-4 text-blue-600" />
                        </div>
                        <div className="text-xs font-medium text-gray-900">{port.portCode}</div>
                        <div className="text-xs text-gray-500 max-w-20 truncate">{port.portName}</div>
                        {port.departureDate && (
                          <div className="text-xs text-gray-400 mt-1">
                            {formatDate(port.departureDate)}
                          </div>
                        )}
                      </div>
                      {index < result.portRotation.length - 1 && (
                        <div className="flex-shrink-0 w-8 h-px bg-gray-300"></div>
                      )}
                    </React.Fragment>
                  ))}
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex items-center justify-end space-x-3 mt-4 pt-4 border-t">
                {onSaveSearch && (
                  <button
                    onClick={() => onSaveSearch(searchResponse.searchId)}
                    className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
                  >
                    <BookmarkIcon className="h-4 w-4 mr-2" />
                    Save
                  </button>
                )}
                
                {onViewDetails && (
                  <button
                    onClick={() => onViewDetails(result)}
                    className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
                  >
                    <EyeIcon className="h-4 w-4 mr-2" />
                    View Details
                  </button>
                )}
                
                <button className="flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors">
                  Book Now
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default SearchResults;
