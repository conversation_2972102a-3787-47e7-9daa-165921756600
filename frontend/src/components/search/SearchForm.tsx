'use client';

import React, { useState } from 'react';
import { CalendarIcon, MagnifyingGlassIcon, ArrowsRightLeftIcon } from '@heroicons/react/24/outline';
import PortAutocomplete from './PortAutocomplete';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';

interface Port {
  id: string;
  code: string;
  name: string;
  countryName: string;
  displayName: string;
}

interface SearchFormData {
  originPort: Port | null;
  destinationPort: Port | null;
  departureDate: Date | null;
}

interface SearchFormProps {
  onSearch: (searchData: SearchFormData) => void;
  isLoading?: boolean;
}

const SearchForm: React.FC<SearchFormProps> = ({ onSearch, isLoading = false }) => {
  const [formData, setFormData] = useState<SearchFormData>({
    originPort: null,
    destinationPort: null,
    departureDate: null,
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleOriginChange = (port: Port | null) => {
    setFormData(prev => ({ ...prev, originPort: port }));
    if (errors.originPort) {
      setErrors(prev => ({ ...prev, originPort: '' }));
    }
  };

  const handleDestinationChange = (port: Port | null) => {
    setFormData(prev => ({ ...prev, destinationPort: port }));
    if (errors.destinationPort) {
      setErrors(prev => ({ ...prev, destinationPort: '' }));
    }
  };

  const handleDateChange = (date: Date | null) => {
    setFormData(prev => ({ ...prev, departureDate: date }));
    if (errors.departureDate) {
      setErrors(prev => ({ ...prev, departureDate: '' }));
    }
  };

  const handleSwapPorts = () => {
    setFormData(prev => ({
      ...prev,
      originPort: prev.destinationPort,
      destinationPort: prev.originPort,
    }));
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.originPort) {
      newErrors.originPort = 'Origin port is required';
    }

    if (!formData.destinationPort) {
      newErrors.destinationPort = 'Destination port is required';
    }

    if (!formData.departureDate) {
      newErrors.departureDate = 'Departure date is required';
    } else if (formData.departureDate < new Date()) {
      newErrors.departureDate = 'Departure date cannot be in the past';
    }

    if (formData.originPort && formData.destinationPort && 
        formData.originPort.id === formData.destinationPort.id) {
      newErrors.destinationPort = 'Destination port must be different from origin port';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (validateForm()) {
      onSearch(formData);
    }
  };

  const minDate = new Date();
  const maxDate = new Date();
  maxDate.setMonth(maxDate.getMonth() + 6); // Allow booking up to 6 months ahead

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Search Shipping Routes</h2>
        <p className="text-gray-600">Find the best shipping options across multiple carriers</p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Origin Port */}
          <div className="relative">
            <PortAutocomplete
              label="Origin Port"
              placeholder="Search origin port (e.g., Shanghai, SHG)"
              value={formData.originPort}
              onChange={handleOriginChange}
              error={errors.originPort}
              disabled={isLoading}
              required
            />
          </div>

          {/* Destination Port */}
          <div className="relative">
            <PortAutocomplete
              label="Destination Port"
              placeholder="Search destination port (e.g., Rotterdam, RTM)"
              value={formData.destinationPort}
              onChange={handleDestinationChange}
              error={errors.destinationPort}
              disabled={isLoading}
              required
            />
            
            {/* Swap Ports Button */}
            <button
              type="button"
              onClick={handleSwapPorts}
              disabled={isLoading}
              className="absolute top-8 -left-6 md:left-1/2 md:-translate-x-1/2 z-10 p-2 bg-white border border-gray-300 rounded-full shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              title="Swap origin and destination"
            >
              <ArrowsRightLeftIcon className="h-4 w-4 text-gray-600" />
            </button>
          </div>
        </div>

        {/* Departure Date */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Departure Date
            <span className="text-red-500 ml-1">*</span>
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <CalendarIcon className="h-5 w-5 text-gray-400" />
            </div>
            <DatePicker
              selected={formData.departureDate}
              onChange={handleDateChange}
              minDate={minDate}
              maxDate={maxDate}
              placeholderText="Select departure date"
              disabled={isLoading}
              className={`w-full pl-10 pr-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                errors.departureDate ? 'border-red-300' : 'border-gray-300'
              } ${isLoading ? 'bg-gray-50 cursor-not-allowed' : ''}`}
              dateFormat="MMM dd, yyyy"
              showPopperArrow={false}
            />
          </div>
          {errors.departureDate && (
            <p className="mt-1 text-sm text-red-600">{errors.departureDate}</p>
          )}
        </div>

        {/* Search Button */}
        <div className="flex justify-center">
          <button
            type="submit"
            disabled={isLoading}
            className="flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed min-w-[200px] justify-center"
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                Searching...
              </>
            ) : (
              <>
                <MagnifyingGlassIcon className="h-5 w-5 mr-2" />
                Search Routes
              </>
            )}
          </button>
        </div>
      </form>

      {/* Search Tips */}
      <div className="mt-6 p-4 bg-blue-50 rounded-md">
        <h3 className="text-sm font-medium text-blue-900 mb-2">Search Tips:</h3>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• You can search by port code (e.g., "SHG") or port name (e.g., "Shanghai")</li>
          <li>• Popular ports are shown when you click in the search field</li>
          <li>• Use the swap button to quickly reverse origin and destination</li>
          <li>• Departure dates can be selected up to 6 months in advance</li>
        </ul>
      </div>
    </div>
  );
};

export default SearchForm;
