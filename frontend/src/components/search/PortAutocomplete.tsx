'use client';

import React, { useState, useEffect, useRef } from 'react';
import { MagnifyingGlassIcon, MapPinIcon } from '@heroicons/react/24/outline';
import axios from 'axios';

interface Port {
  id: string;
  code: string;
  name: string;
  countryName: string;
  displayName: string;
}

interface PortAutocompleteProps {
  label: string;
  placeholder?: string;
  value?: Port | null;
  onChange: (port: Port | null) => void;
  error?: string;
  disabled?: boolean;
  required?: boolean;
}

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001';

const PortAutocomplete: React.FC<PortAutocompleteProps> = ({
  label,
  placeholder = 'Search for a port...',
  value,
  onChange,
  error,
  disabled = false,
  required = false,
}) => {
  const [query, setQuery] = useState('');
  const [ports, setPorts] = useState<Port[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [popularPorts, setPopularPorts] = useState<Port[]>([]);
  
  const inputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Load popular ports on mount
  useEffect(() => {
    const loadPopularPorts = async () => {
      try {
        const response = await axios.get(`${API_BASE_URL}/api/ports/popular?limit=8`);
        setPopularPorts(response.data.data.ports);
      } catch (error) {
        console.error('Error loading popular ports:', error);
      }
    };

    loadPopularPorts();
  }, []);

  // Search ports when query changes
  useEffect(() => {
    const searchPorts = async () => {
      if (query.length < 2) {
        setPorts([]);
        return;
      }

      setIsLoading(true);
      try {
        const response = await axios.get(
          `${API_BASE_URL}/api/ports/search?q=${encodeURIComponent(query)}&limit=10`
        );
        setPorts(response.data.data.ports);
      } catch (error) {
        console.error('Error searching ports:', error);
        setPorts([]);
      } finally {
        setIsLoading(false);
      }
    };

    const debounceTimer = setTimeout(searchPorts, 300);
    return () => clearTimeout(debounceTimer);
  }, [query]);

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newQuery = e.target.value;
    setQuery(newQuery);
    setIsOpen(true);

    // Clear selection if input doesn't match selected port
    if (value && !newQuery.toLowerCase().includes(value.code.toLowerCase()) && 
        !newQuery.toLowerCase().includes(value.name.toLowerCase())) {
      onChange(null);
    }
  };

  // Handle port selection
  const handlePortSelect = (port: Port) => {
    setQuery(port.displayName);
    onChange(port);
    setIsOpen(false);
    inputRef.current?.blur();
  };

  // Handle input focus
  const handleFocus = () => {
    setIsOpen(true);
    if (!query && popularPorts.length > 0) {
      setPorts(popularPorts);
    }
  };

  // Handle click outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Update input when value changes externally
  useEffect(() => {
    if (value) {
      setQuery(value.displayName);
    } else if (!isOpen) {
      setQuery('');
    }
  }, [value, isOpen]);

  const displayPorts = query.length < 2 ? popularPorts : ports;

  return (
    <div className="relative" ref={dropdownRef}>
      <label className="block text-sm font-medium text-gray-700 mb-2">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </label>
      
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
        </div>
        
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={handleInputChange}
          onFocus={handleFocus}
          placeholder={placeholder}
          disabled={disabled}
          className={`w-full pl-10 pr-3 py-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 bg-white placeholder-gray-500 ${
            error ? 'border-red-300' : 'border-gray-300'
          } ${disabled ? 'bg-gray-50 cursor-not-allowed text-gray-500' : ''}`}
        />
        
        {isLoading && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
          </div>
        )}
      </div>

      {error && (
        <p className="mt-1 text-sm text-red-600">{error}</p>
      )}

      {isOpen && (
        <div className="absolute z-10 mt-1 w-full bg-white shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none">
          {displayPorts.length === 0 && !isLoading && query.length >= 2 && (
            <div className="px-4 py-2 text-sm text-gray-500">
              No ports found for "{query}"
            </div>
          )}
          
          {displayPorts.length === 0 && !isLoading && query.length < 2 && (
            <div className="px-4 py-2 text-sm text-gray-500">
              Type at least 2 characters to search
            </div>
          )}

          {query.length < 2 && popularPorts.length > 0 && (
            <div className="px-4 py-2 text-xs font-medium text-gray-400 uppercase tracking-wide">
              Popular Ports
            </div>
          )}

          {displayPorts.map((port) => (
            <button
              key={port.id}
              onClick={() => handlePortSelect(port)}
              className="w-full text-left px-4 py-2 hover:bg-blue-50 focus:bg-blue-50 focus:outline-none"
            >
              <div className="flex items-center">
                <MapPinIcon className="h-4 w-4 text-gray-400 mr-2 flex-shrink-0" />
                <div>
                  <div className="text-sm font-medium text-gray-900">
                    {port.code} - {port.name}
                  </div>
                  <div className="text-xs text-gray-500">{port.countryName}</div>
                </div>
              </div>
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default PortAutocomplete;
