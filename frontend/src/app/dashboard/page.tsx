'use client';

import React from 'react';
import Link from 'next/link';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import { useAuth } from '@/contexts/AuthContext';
import { 
  MagnifyingGlassIcon, 
  ClockIcon, 
  TruckIcon, 
  ChartBarIcon,
  PlusIcon,
  ArrowRightIcon
} from '@heroicons/react/24/outline';

const DashboardPage: React.FC = () => {
  const { user } = useAuth();

  // Mock data for demonstration
  const recentSearches = [
    {
      id: '1',
      origin: 'Shanghai (SHG)',
      destination: 'Rotterdam (RTM)',
      date: '2024-01-15',
      results: 3,
    },
    {
      id: '2',
      origin: 'Los Angeles (LAX)',
      destination: 'Hamburg (HAM)',
      date: '2024-01-14',
      results: 2,
    },
    {
      id: '3',
      origin: 'Singapore (SIN)',
      destination: 'New York (NYC)',
      date: '2024-01-13',
      results: 4,
    },
  ];

  const stats = [
    {
      name: 'Total Searches',
      value: '24',
      icon: MagnifyingGlassIcon,
      change: '+12%',
      changeType: 'increase',
    },
    {
      name: 'Routes Found',
      value: '89',
      icon: TruckIcon,
      change: '+8%',
      changeType: 'increase',
    },
    {
      name: 'Avg Transit Time',
      value: '28 days',
      icon: ClockIcon,
      change: '-2 days',
      changeType: 'decrease',
    },
    {
      name: 'Carriers Compared',
      value: '4',
      icon: ChartBarIcon,
      change: 'All active',
      changeType: 'neutral',
    },
  ];

  return (
    <ProtectedRoute>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">
            Welcome back, {user?.firstName}!
          </h1>
          <p className="text-gray-600 mt-2">
            Here's your freight forwarding dashboard overview.
          </p>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          <Link
            href="/search"
            className="bg-blue-600 hover:bg-blue-700 text-white rounded-lg p-6 transition-colors group"
          >
            <div className="flex items-center">
              <MagnifyingGlassIcon className="h-8 w-8 mr-3" />
              <div>
                <h3 className="text-lg font-semibold">New Search</h3>
                <p className="text-blue-100">Find shipping routes</p>
              </div>
              <ArrowRightIcon className="h-5 w-5 ml-auto group-hover:translate-x-1 transition-transform" />
            </div>
          </Link>

          <Link
            href="/history"
            className="bg-white hover:bg-gray-50 border border-gray-200 rounded-lg p-6 transition-colors group"
          >
            <div className="flex items-center">
              <ClockIcon className="h-8 w-8 mr-3 text-gray-600" />
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Search History</h3>
                <p className="text-gray-600">View past searches</p>
              </div>
              <ArrowRightIcon className="h-5 w-5 ml-auto text-gray-400 group-hover:translate-x-1 transition-transform" />
            </div>
          </Link>

          <Link
            href="/profile"
            className="bg-white hover:bg-gray-50 border border-gray-200 rounded-lg p-6 transition-colors group"
          >
            <div className="flex items-center">
              <ChartBarIcon className="h-8 w-8 mr-3 text-gray-600" />
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Profile Settings</h3>
                <p className="text-gray-600">Manage your account</p>
              </div>
              <ArrowRightIcon className="h-5 w-5 ml-auto text-gray-400 group-hover:translate-x-1 transition-transform" />
            </div>
          </Link>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat) => (
            <div key={stat.name} className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <stat.icon className="h-8 w-8 text-gray-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">{stat.name}</p>
                  <p className="text-2xl font-semibold text-gray-900">{stat.value}</p>
                </div>
              </div>
              <div className="mt-4">
                <span
                  className={`text-sm font-medium ${
                    stat.changeType === 'increase'
                      ? 'text-green-600'
                      : stat.changeType === 'decrease'
                      ? 'text-red-600'
                      : 'text-gray-600'
                  }`}
                >
                  {stat.change}
                </span>
                <span className="text-sm text-gray-500 ml-1">from last month</span>
              </div>
            </div>
          ))}
        </div>

        {/* Recent Searches */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-900">Recent Searches</h2>
              <Link
                href="/history"
                className="text-blue-600 hover:text-blue-700 text-sm font-medium"
              >
                View all
              </Link>
            </div>
          </div>
          <div className="divide-y divide-gray-200">
            {recentSearches.map((search) => (
              <div key={search.id} className="px-6 py-4 hover:bg-gray-50">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      {search.origin} → {search.destination}
                    </p>
                    <p className="text-sm text-gray-500">
                      {new Date(search.date).toLocaleDateString('en-US', {
                        month: 'short',
                        day: 'numeric',
                        year: 'numeric',
                      })}
                    </p>
                  </div>
                  <div className="flex items-center space-x-4">
                    <span className="text-sm text-gray-600">
                      {search.results} routes found
                    </span>
                    <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">
                      View Results
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
          {recentSearches.length === 0 && (
            <div className="px-6 py-12 text-center">
              <MagnifyingGlassIcon className="h-12 w-12 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No searches yet</h3>
              <p className="text-gray-600 mb-4">
                Start by searching for shipping routes to see your history here.
              </p>
              <Link
                href="/search"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                Start Searching
              </Link>
            </div>
          )}
        </div>
      </div>
    </ProtectedRoute>
  );
};

export default DashboardPage;
