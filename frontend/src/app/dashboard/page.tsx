'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import axios from 'axios';
import {
  MagnifyingGlassIcon,
  ClockIcon,
  TruckIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001';

const DashboardPage: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [carrierStatus, setCarrierStatus] = useState<{maersk?: {status: string}} | null>(null);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await axios.get(`${API_BASE_URL}/api/search/test-carriers`);
      setCarrierStatus(response.data.data.carriers);
    } catch (err) {
      console.error('Failed to fetch dashboard data:', err);
      setError('Failed to load dashboard data');
    } finally {
      setIsLoading(false);
    }
  };

  const stats = [
    {
      name: 'Total Searches',
      value: '24',
      icon: MagnifyingGlassIcon,
      change: '+12%',
      changeType: 'increase',
    },
    {
      name: 'Active Carriers',
      value: carrierStatus?.maersk?.status === 'connected' ? '1' : '0',
      icon: TruckIcon,
      change: 'Maersk API',
      changeType: 'neutral',
    },
    {
      name: 'Avg Response Time',
      value: '650ms',
      icon: ClockIcon,
      change: 'Real-time',
      changeType: 'neutral',
    },
    {
      name: 'System Status',
      value: carrierStatus?.maersk?.status === 'connected' ? 'Healthy' : 'Offline',
      icon: ChartBarIcon,
      change: carrierStatus?.maersk?.status === 'connected' ? 'Online' : 'Check API',
      changeType: carrierStatus?.maersk?.status === 'connected' ? 'increase' : 'decrease',
    },
  ];

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Welcome Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">
          Digital Freight Platform Dashboard
        </h1>
        <p className="text-gray-600 mt-2">
          Real-time shipping route search and carrier API monitoring.
        </p>
      </div>

      {/* Error Message */}
      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          {error}
        </div>
      )}

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {isLoading ? (
          Array.from({ length: 4 }).map((_, index) => (
            <div key={index} className="bg-white rounded-lg shadow p-6 animate-pulse">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="h-8 w-8 bg-gray-300 rounded"></div>
                </div>
                <div className="ml-4 flex-1">
                  <div className="h-4 bg-gray-300 rounded w-3/4 mb-2"></div>
                  <div className="h-6 bg-gray-300 rounded w-1/2"></div>
                </div>
              </div>
              <div className="mt-4">
                <div className="h-3 bg-gray-300 rounded w-1/3"></div>
              </div>
            </div>
          ))
        ) : (
          stats.map((stat) => (
            <div key={stat.name} className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <stat.icon className="h-8 w-8 text-gray-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">{stat.name}</p>
                  <p className="text-2xl font-semibold text-gray-900">{stat.value}</p>
                </div>
              </div>
              <div className="mt-4">
                <span
                  className={`text-sm font-medium ${
                    stat.changeType === 'increase'
                      ? 'text-green-600'
                      : stat.changeType === 'decrease'
                      ? 'text-red-600'
                      : 'text-gray-600'
                  }`}
                >
                  {stat.change}
                </span>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Link
          href="/search"
          className="bg-white rounded-lg shadow p-6 hover:shadow-md transition-shadow"
        >
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <MagnifyingGlassIcon className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <h3 className="text-lg font-medium text-gray-900">Search Routes</h3>
              <p className="text-gray-600">Find shipping schedules</p>
            </div>
          </div>
        </Link>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <TruckIcon className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <h3 className="text-lg font-medium text-gray-900">Carrier Status</h3>
              <p className="text-gray-600">
                Maersk API: {carrierStatus?.maersk?.status === 'connected' ? 'Connected' : 'Offline'}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <ClockIcon className="h-8 w-8 text-purple-600" />
            </div>
            <div className="ml-4">
              <h3 className="text-lg font-medium text-gray-900">Real-time Data</h3>
              <p className="text-gray-600">Live shipping schedules</p>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Start Guide */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Get Started</h2>
        </div>
        <div className="p-6">
          <p className="text-gray-600 mb-4">
            Welcome to the Digital Freight Platform. Start by searching for shipping routes between any two ports worldwide.
          </p>
          <Link
            href="/search"
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            <MagnifyingGlassIcon className="h-4 w-4 mr-2" />
            Start Searching
          </Link>
        </div>
      </div>
    </div>
  );
};

export default DashboardPage;
