'use client';

import React, { useState } from 'react';
import SearchForm from '@/components/search/SearchForm';
import { TruckIcon, ClockIcon, MapPinIcon } from '@heroicons/react/24/outline';

interface Port {
  id: string;
  code: string;
  name: string;
  countryName: string;
  displayName: string;
}

interface SearchFormData {
  originPort: Port | null;
  destinationPort: Port | null;
  departureDate: Date | null;
}

// Mock search results for demonstration
const mockSearchResults = [
  {
    id: '1',
    carrier: { name: 'Maersk Line', code: 'MAEU' },
    vesselName: 'Maersk Shanghai',
    voyageNumber: 'MS2401',
    departureDate: '2024-02-15',
    arrivalDate: '2024-03-20',
    transitDays: 34,
    portRotation: [
      { portCode: 'SHG', portName: 'Shanghai', sequenceOrder: 1, departureDate: '2024-02-15' },
      { portCode: 'SIN', portName: 'Singapore', sequenceOrder: 2, arrivalDate: '2024-02-22', departureDate: '2024-02-23' },
      { portCode: 'RTM', portName: 'Rotterdam', sequenceOrder: 3, arrivalDate: '2024-03-20' },
    ],
  },
  {
    id: '2',
    carrier: { name: '<PERSON><PERSON>g-<PERSON>', code: 'HLCU' },
    vesselName: 'Hamburg Express',
    voyageNumber: 'HE2401',
    departureDate: '2024-02-18',
    arrivalDate: '2024-03-25',
    transitDays: 36,
    portRotation: [
      { portCode: 'SHG', portName: 'Shanghai', sequenceOrder: 1, departureDate: '2024-02-18' },
      { portCode: 'HKG', portName: 'Hong Kong', sequenceOrder: 2, arrivalDate: '2024-02-20', departureDate: '2024-02-21' },
      { portCode: 'SIN', portName: 'Singapore', sequenceOrder: 3, arrivalDate: '2024-02-25', departureDate: '2024-02-26' },
      { portCode: 'RTM', portName: 'Rotterdam', sequenceOrder: 4, arrivalDate: '2024-03-25' },
    ],
  },
];

const SearchPage: React.FC = () => {
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);

  const handleSearch = async (searchData: SearchFormData) => {
    setIsLoading(true);
    setHasSearched(true);

    // Simulate API call
    setTimeout(() => {
      // For now, return mock results
      setSearchResults(mockSearchResults);
      setIsLoading(false);
    }, 2000);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Page Header */}
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          Find Your Perfect Shipping Route
        </h1>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Search across multiple carriers to find the best shipping options for your freight forwarding needs.
        </p>
      </div>

      {/* Search Form */}
      <div className="mb-8">
        <SearchForm onSearch={handleSearch} isLoading={isLoading} />
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Searching across multiple carriers...</p>
        </div>
      )}

      {/* Search Results */}
      {!isLoading && hasSearched && (
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold text-gray-900">
              Search Results ({searchResults.length})
            </h2>
            {searchResults.length > 0 && (
              <button className="text-blue-600 hover:text-blue-700 font-medium">
                Export Results
              </button>
            )}
          </div>

          {searchResults.length === 0 ? (
            <div className="text-center py-12 bg-white rounded-lg shadow">
              <TruckIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No routes found</h3>
              <p className="text-gray-600">
                Try adjusting your search criteria or selecting different ports.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {searchResults.map((result) => (
                <div key={result.id} className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                  <div className="flex items-start justify-between mb-4">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">
                        {result.carrier.name}
                      </h3>
                      <p className="text-gray-600">
                        {result.vesselName} • {result.voyageNumber}
                      </p>
                    </div>
                    <div className="text-right">
                      <div className="flex items-center text-sm text-gray-500 mb-1">
                        <ClockIcon className="h-4 w-4 mr-1" />
                        {result.transitDays} days
                      </div>
                      <div className="text-lg font-semibold text-gray-900">
                        {formatDate(result.departureDate)} → {formatDate(result.arrivalDate)}
                      </div>
                    </div>
                  </div>

                  {/* Port Rotation */}
                  <div className="border-t pt-4">
                    <h4 className="text-sm font-medium text-gray-700 mb-3">Port Rotation</h4>
                    <div className="flex items-center space-x-2 overflow-x-auto pb-2">
                      {result.portRotation.map((port: any, index: number) => (
                        <React.Fragment key={port.portCode}>
                          <div className="flex-shrink-0 text-center">
                            <div className="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-full mb-1">
                              <MapPinIcon className="h-4 w-4 text-blue-600" />
                            </div>
                            <div className="text-xs font-medium text-gray-900">{port.portCode}</div>
                            <div className="text-xs text-gray-500">{port.portName}</div>
                            {port.departureDate && (
                              <div className="text-xs text-gray-400 mt-1">
                                {formatDate(port.departureDate)}
                              </div>
                            )}
                          </div>
                          {index < result.portRotation.length - 1 && (
                            <div className="flex-shrink-0 w-8 h-px bg-gray-300"></div>
                          )}
                        </React.Fragment>
                      ))}
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex items-center justify-end space-x-3 mt-4 pt-4 border-t">
                    <button className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors">
                      View Details
                    </button>
                    <button className="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors">
                      Book Now
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* No Search Yet */}
      {!hasSearched && !isLoading && (
        <div className="text-center py-12">
          <TruckIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Ready to Search</h3>
          <p className="text-gray-600">
            Enter your origin, destination, and departure date above to find shipping routes.
          </p>
        </div>
      )}
    </div>
  );
};

export default SearchPage;
