'use client';

import React, { useState } from 'react';
import axios from 'axios';
import SearchForm from '@/components/search/SearchForm';
import SearchResults, { SearchResponse } from '@/components/search/SearchResults';
import { TruckIcon } from '@heroicons/react/24/outline';

interface Port {
  id: string;
  code: string;
  name: string;
  countryName: string;
  displayName: string;
}

interface SearchFormData {
  originPort: Port | null;
  destinationPort: Port | null;
  departureDate: Date | null;
}

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001';

const SearchPage: React.FC = () => {
  const [searchResponse, setSearchResponse] = useState<SearchResponse | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string>('');

  const handleSearch = async (searchData: SearchFormData) => {
    if (!searchData.originPort || !searchData.destinationPort || !searchData.departureDate) {
      setError('Please fill in all search fields');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      const response = await axios.post(`${API_BASE_URL}/api/search/routes`, {
        originPortId: searchData.originPort.id,
        destinationPortId: searchData.destinationPort.id,
        departureDate: searchData.departureDate.toISOString().split('T')[0],
        carriers: ['MAEU'], // Start with Maersk only
      });

      setSearchResponse(response.data.data);
    } catch (err: any) {
      console.error('Search error:', err);
      setError(err.response?.data?.message || 'Search failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleExport = async (searchId: string, format: string) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/search/${searchId}/export?format=${format}`, {
        responseType: 'blob',
      });

      const blob = new Blob([response.data], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `search-results-${searchId}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (err) {
      console.error('Export error:', err);
      setError('Failed to export results');
    }
  };

  const handleViewDetails = (result: any) => {
    // TODO: Implement detailed view modal or navigation
    console.log('View details for:', result);
  };

  const handleSaveSearch = async (searchId: string) => {
    // TODO: Implement save search functionality
    console.log('Save search:', searchId);
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Page Header */}
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          Find Your Perfect Shipping Route
        </h1>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Search across multiple carriers to find the best shipping options for your freight forwarding needs.
        </p>
      </div>

      {/* Search Form */}
      <div className="mb-8">
        <SearchForm onSearch={handleSearch} isLoading={isLoading} />
      </div>

      {/* Error Message */}
      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          {error}
        </div>
      )}

      {/* Search Results */}
      <SearchResults
        searchResponse={searchResponse}
        isLoading={isLoading}
        onExport={handleExport}
        onViewDetails={handleViewDetails}
        onSaveSearch={handleSaveSearch}
      />
    </div>
  );
};

export default SearchPage;
