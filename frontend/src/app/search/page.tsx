'use client';

import React, { useState } from 'react';
import axios from 'axios';
import SearchForm from '@/components/search/SearchForm';
import SearchResults, { SearchResponse } from '@/components/search/SearchResults';
import { TruckIcon } from '@heroicons/react/24/outline';

interface Port {
  id: string;
  code: string;
  name: string;
  countryName: string;
  displayName: string;
}

interface SearchFormData {
  originPort: Port | null;
  destinationPort: Port | null;
  departureDate: Date | null;
}

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001';

const SearchPage: React.FC = () => {
  const [searchResponse, setSearchResponse] = useState<SearchResponse | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string>('');

  const handleSearch = async (searchData: SearchFormData) => {
    if (!searchData.originPort || !searchData.destinationPort || !searchData.departureDate) {
      setError('Please fill in all search fields');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      const response = await axios.post(`${API_BASE_URL}/api/search/routes`, {
        originPortId: searchData.originPort.id,
        destinationPortId: searchData.destinationPort.id,
        departureDate: searchData.departureDate.toISOString().split('T')[0],
        carriers: ['MAEU'], // Start with Maersk only
      });

      setSearchResponse(response.data.data);
    } catch (err: any) {
      let errorMessage = 'Search failed. Please try again.';

      if (err.response?.data?.message) {
        errorMessage = err.response.data.message;
      } else if (err.code === 'NETWORK_ERROR') {
        errorMessage = 'Network error. Please check your internet connection.';
      } else if (err.code === 'ECONNABORTED') {
        errorMessage = 'Search request timed out. Please try again.';
      }

      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };



  const [selectedResult, setSelectedResult] = useState<any>(null);

  const handleViewDetails = (result: any) => {
    setSelectedResult(result);
  };

  const closeDetails = () => {
    setSelectedResult(null);
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Page Header */}
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          Find Your Perfect Shipping Route
        </h1>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Search across multiple carriers to find the best shipping options for your freight forwarding needs.
        </p>
      </div>

      {/* Search Form */}
      <div className="mb-8">
        <SearchForm onSearch={handleSearch} isLoading={isLoading} />
      </div>

      {/* Error Message */}
      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          {error}
        </div>
      )}

      {/* Search Results */}
      <SearchResults
        searchResponse={searchResponse}
        isLoading={isLoading}
        onViewDetails={handleViewDetails}
      />
    </div>
  );
};

export default SearchPage;
