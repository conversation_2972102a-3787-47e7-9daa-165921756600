exports.id=819,exports.ids=[819],exports.modules={1135:()=>{},1644:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},3213:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>d,h:()=>c});var s=r(687),o=r(3210),a=r(1060);let n=(0,o.createContext)(void 0),i=process.env.NEXT_PUBLIC_API_URL||"http://localhost:5001",l=a.A.create({baseURL:i,headers:{"Content-Type":"application/json"}}),d=({children:e})=>{let[t,r]=(0,o.useState)(null),[a,i]=(0,o.useState)(null),[d,c]=(0,o.useState)(!0);(0,o.useEffect)(()=>{let e=l.interceptors.request.use(e=>(a&&(e.headers.Authorization=`Bearer ${a}`),e));return()=>l.interceptors.request.eject(e)},[a]),(0,o.useEffect)(()=>{(async()=>{try{let e=localStorage.getItem("auth_token"),t=localStorage.getItem("auth_user");if(e&&t){i(e),r(JSON.parse(t));try{let t=await l.get("/api/auth/profile",{headers:{Authorization:`Bearer ${e}`}});r(t.data.data.user)}catch(e){localStorage.removeItem("auth_token"),localStorage.removeItem("auth_user"),i(null),r(null)}}}catch(e){console.error("Error loading user:",e)}finally{c(!1)}})()},[]);let h=async(e,t)=>{try{c(!0);let{user:s,token:o}=(await l.post("/api/auth/login",{email:e,password:t})).data.data;r(s),i(o),localStorage.setItem("auth_token",o),localStorage.setItem("auth_user",JSON.stringify(s))}catch(e){throw Error(e.response?.data?.message||"Login failed")}finally{c(!1)}},m=async e=>{try{c(!0);let{user:t,token:s}=(await l.post("/api/auth/register",e)).data.data;r(t),i(s),localStorage.setItem("auth_token",s),localStorage.setItem("auth_user",JSON.stringify(t))}catch(e){throw Error(e.response?.data?.message||"Registration failed")}finally{c(!1)}},u=async e=>{try{c(!0);let t=(await l.put("/api/auth/profile",e)).data.data.user;r(t),localStorage.setItem("auth_user",JSON.stringify(t))}catch(e){throw Error(e.response?.data?.message||"Profile update failed")}finally{c(!1)}};return(0,s.jsx)(n.Provider,{value:{user:t,token:a,isLoading:d,isAuthenticated:!!t&&!!a,login:h,register:m,logout:()=>{r(null),i(null),localStorage.removeItem("auth_token"),localStorage.removeItem("auth_user"),a&&l.post("/api/auth/logout").catch(()=>{})},updateProfile:u},children:e})},c=()=>{let e=(0,o.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,metadata:()=>l});var s=r(7413),o=r(7339),a=r.n(o);r(1135);var n=r(9131),i=r(8926);let l={title:"Digital Freight Platform",description:"Unified shipping route search across multiple carriers"};function d({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:`${a().className} antialiased bg-gray-50`,children:(0,s.jsx)(n.AuthProvider,{children:(0,s.jsxs)("div",{className:"min-h-screen flex flex-col",children:[(0,s.jsx)(i.default,{}),(0,s.jsx)("main",{className:"flex-1",children:e})]})})})})}},5564:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},5736:(e,t,r)=>{"use strict";r.d(t,{default:()=>x});var s=r(687),o=r(3210),a=r(5814),n=r.n(a),i=r(3213),l=r(430),d=r(4908),c=r(1426),h=r(4944),m=r(1836),u=r(6510);let x=()=>{let{user:e,isAuthenticated:t,logout:r}=(0,i.h)(),[a,x]=(0,o.useState)(!1),[f,p]=(0,o.useState)(!1),g=()=>{r(),p(!1)};return(0,s.jsxs)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:[(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsxs)(n(),{href:"/",className:"flex items-center space-x-2",children:[(0,s.jsx)(l.A,{className:"h-8 w-8 text-blue-600"}),(0,s.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"Digital Freight"})]})}),(0,s.jsxs)("nav",{className:"hidden md:flex items-center space-x-8",children:[(0,s.jsx)(n(),{href:"/search",className:"text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors",children:"Search Routes"}),t&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(n(),{href:"/dashboard",className:"text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors",children:"Dashboard"}),(0,s.jsx)(n(),{href:"/history",className:"text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors",children:"Search History"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[t&&e?(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsxs)("button",{onClick:()=>p(!f),className:"flex items-center space-x-2 text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors",children:[(0,s.jsx)(d.A,{className:"h-5 w-5"}),(0,s.jsx)("span",{className:"hidden sm:block",children:e.firstName})]}),f&&(0,s.jsxs)("div",{className:"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200",children:[(0,s.jsxs)("div",{className:"px-4 py-2 text-sm text-gray-500 border-b border-gray-100",children:[(0,s.jsxs)("div",{className:"font-medium text-gray-900",children:[e.firstName," ",e.lastName]}),(0,s.jsx)("div",{className:"truncate",children:e.email}),e.companyName&&(0,s.jsx)("div",{className:"text-xs text-gray-400",children:e.companyName})]}),(0,s.jsxs)(n(),{href:"/profile",className:"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",onClick:()=>p(!1),children:[(0,s.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Profile Settings"]}),(0,s.jsxs)("button",{onClick:g,className:"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[(0,s.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Sign Out"]})]})]}):(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(n(),{href:"/auth",className:"text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors",children:"Sign In"}),(0,s.jsx)(n(),{href:"/auth?mode=register",className:"bg-blue-600 text-white hover:bg-blue-700 px-4 py-2 rounded-md text-sm font-medium transition-colors",children:"Get Started"})]}),(0,s.jsx)("button",{onClick:()=>x(!a),className:"md:hidden p-2 rounded-md text-gray-700 hover:text-blue-600 hover:bg-gray-100",children:a?(0,s.jsx)(m.A,{className:"h-6 w-6"}):(0,s.jsx)(u.A,{className:"h-6 w-6"})})]})]}),a&&(0,s.jsx)("div",{className:"md:hidden border-t border-gray-200 py-4",children:(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(n(),{href:"/search",className:"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md",onClick:()=>x(!1),children:"Search Routes"}),t&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(n(),{href:"/dashboard",className:"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md",onClick:()=>x(!1),children:"Dashboard"}),(0,s.jsx)(n(),{href:"/history",className:"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md",onClick:()=>x(!1),children:"Search History"}),(0,s.jsx)(n(),{href:"/profile",className:"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md",onClick:()=>x(!1),children:"Profile Settings"}),(0,s.jsx)("button",{onClick:()=>{g(),x(!1)},className:"block w-full text-left px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md",children:"Sign Out"})]})]})})]}),f&&(0,s.jsx)("div",{className:"fixed inset-0 z-40",onClick:()=>p(!1)})]})}},7063:(e,t,r)=>{Promise.resolve().then(r.bind(r,8926)),Promise.resolve().then(r.bind(r,9131))},8926:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/vo/digital-freight/frontend/src/components/layout/Header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/vo/digital-freight/frontend/src/components/layout/Header.tsx","default")},9131:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>o});var s=r(2907);let o=(0,s.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/vo/digital-freight/frontend/src/contexts/AuthContext.tsx","AuthProvider");(0,s.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/vo/digital-freight/frontend/src/contexts/AuthContext.tsx","useAuth"),(0,s.registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/vo/digital-freight/frontend/src/contexts/AuthContext.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/vo/digital-freight/frontend/src/contexts/AuthContext.tsx","default")},9300:(e,t,r)=>{Promise.resolve().then(r.bind(r,5736)),Promise.resolve().then(r.bind(r,3213))}};