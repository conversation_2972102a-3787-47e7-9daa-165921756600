{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/vo/digital-freight/frontend/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport axios from 'axios';\nimport {\n  MagnifyingGlassIcon,\n  ClockIcon,\n  TruckIcon,\n  ChartBarIcon\n} from '@heroicons/react/24/outline';\n\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001';\n\nconst DashboardPage: React.FC = () => {\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [carrierStatus, setCarrierStatus] = useState<{maersk?: {status: string}} | null>(null);\n\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n\n  const fetchDashboardData = async () => {\n    try {\n      setIsLoading(true);\n      setError(null);\n\n      const response = await axios.get(`${API_BASE_URL}/api/search/test-carriers`);\n      setCarrierStatus(response.data.data.carriers);\n    } catch (err) {\n      console.error('Failed to fetch dashboard data:', err);\n      setError('Failed to load dashboard data');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const stats = [\n    {\n      name: 'Total Searches',\n      value: '24',\n      icon: MagnifyingGlassIcon,\n      change: '+12%',\n      changeType: 'increase',\n    },\n    {\n      name: 'Active Carriers',\n      value: carrierStatus?.maersk?.status === 'connected' ? '1' : '0',\n      icon: TruckIcon,\n      change: 'Maersk API',\n      changeType: 'neutral',\n    },\n    {\n      name: 'Avg Response Time',\n      value: '650ms',\n      icon: ClockIcon,\n      change: 'Real-time',\n      changeType: 'neutral',\n    },\n    {\n      name: 'System Status',\n      value: carrierStatus?.maersk?.status === 'connected' ? 'Healthy' : 'Offline',\n      icon: ChartBarIcon,\n      change: carrierStatus?.maersk?.status === 'connected' ? 'Online' : 'Check API',\n      changeType: carrierStatus?.maersk?.status === 'connected' ? 'increase' : 'decrease',\n    },\n  ];\n\n  return (\n    <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n      {/* Welcome Header */}\n      <div className=\"mb-8\">\n        <h1 className=\"text-3xl font-bold text-gray-900\">\n          Digital Freight Platform Dashboard\n        </h1>\n        <p className=\"text-gray-600 mt-2\">\n          Real-time shipping route search and carrier API monitoring.\n        </p>\n      </div>\n\n      {/* Error Message */}\n      {error && (\n        <div className=\"mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md\">\n          {error}\n        </div>\n      )}\n\n      {/* Stats Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n        {isLoading ? (\n          Array.from({ length: 4 }).map((_, index) => (\n            <div key={index} className=\"bg-white rounded-lg shadow p-6 animate-pulse\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <div className=\"h-8 w-8 bg-gray-300 rounded\"></div>\n                </div>\n                <div className=\"ml-4 flex-1\">\n                  <div className=\"h-4 bg-gray-300 rounded w-3/4 mb-2\"></div>\n                  <div className=\"h-6 bg-gray-300 rounded w-1/2\"></div>\n                </div>\n              </div>\n              <div className=\"mt-4\">\n                <div className=\"h-3 bg-gray-300 rounded w-1/3\"></div>\n              </div>\n            </div>\n          ))\n        ) : (\n          stats.map((stat) => (\n            <div key={stat.name} className=\"bg-white rounded-lg shadow p-6\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <stat.icon className=\"h-8 w-8 text-gray-600\" />\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-600\">{stat.name}</p>\n                  <p className=\"text-2xl font-semibold text-gray-900\">{stat.value}</p>\n                </div>\n              </div>\n              <div className=\"mt-4\">\n                <span\n                  className={`text-sm font-medium ${\n                    stat.changeType === 'increase'\n                      ? 'text-green-600'\n                      : stat.changeType === 'decrease'\n                      ? 'text-red-600'\n                      : 'text-gray-600'\n                  }`}\n                >\n                  {stat.change}\n                </span>\n              </div>\n            </div>\n          ))\n        )}\n      </div>\n\n      {/* Quick Actions */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\">\n        <Link\n          href=\"/search\"\n          className=\"bg-white rounded-lg shadow p-6 hover:shadow-md transition-shadow\"\n        >\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <MagnifyingGlassIcon className=\"h-8 w-8 text-blue-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <h3 className=\"text-lg font-medium text-gray-900\">Search Routes</h3>\n              <p className=\"text-gray-600\">Find shipping schedules</p>\n            </div>\n          </div>\n        </Link>\n\n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <TruckIcon className=\"h-8 w-8 text-green-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <h3 className=\"text-lg font-medium text-gray-900\">Carrier Status</h3>\n              <p className=\"text-gray-600\">\n                Maersk API: {carrierStatus?.maersk?.status === 'connected' ? 'Connected' : 'Offline'}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <ClockIcon className=\"h-8 w-8 text-purple-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <h3 className=\"text-lg font-medium text-gray-900\">Real-time Data</h3>\n              <p className=\"text-gray-600\">Live shipping schedules</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Quick Start Guide */}\n      <div className=\"bg-white rounded-lg shadow\">\n        <div className=\"px-6 py-4 border-b border-gray-200\">\n          <h2 className=\"text-lg font-semibold text-gray-900\">Get Started</h2>\n        </div>\n        <div className=\"p-6\">\n          <p className=\"text-gray-600 mb-4\">\n            Welcome to the Digital Freight Platform. Start by searching for shipping routes between any two ports worldwide.\n          </p>\n          <Link\n            href=\"/search\"\n            className=\"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors\"\n          >\n            <MagnifyingGlassIcon className=\"h-4 w-4 mr-2\" />\n            Start Searching\n          </Link>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default DashboardPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AALA;;;;;;AAYA,MAAM,eAAe,QAAQ,GAAG,CAAC,mBAAmB,IAAI;AAExD,MAAM,gBAA0B;IAC9B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsC;IAEvF,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,qBAAqB;QACzB,IAAI;YACF,aAAa;YACb,SAAS;YAET,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,GAAG,aAAa,yBAAyB,CAAC;YAC3E,iBAAiB,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ;QAC9C,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,mCAAmC;YACjD,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,QAAQ;QACZ;YACE,MAAM;YACN,OAAO;YACP,MAAM,qOAAA,CAAA,sBAAmB;YACzB,QAAQ;YACR,YAAY;QACd;QACA;YACE,MAAM;YACN,OAAO,eAAe,QAAQ,WAAW,cAAc,MAAM;YAC7D,MAAM,iNAAA,CAAA,YAAS;YACf,QAAQ;YACR,YAAY;QACd;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM,iNAAA,CAAA,YAAS;YACf,QAAQ;YACR,YAAY;QACd;QACA;YACE,MAAM;YACN,OAAO,eAAe,QAAQ,WAAW,cAAc,YAAY;YACnE,MAAM,uNAAA,CAAA,eAAY;YAClB,QAAQ,eAAe,QAAQ,WAAW,cAAc,WAAW;YACnE,YAAY,eAAe,QAAQ,WAAW,cAAc,aAAa;QAC3E;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;kCAGjD,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;YAMnC,uBACC,8OAAC;gBAAI,WAAU;0BACZ;;;;;;0BAKL,8OAAC;gBAAI,WAAU;0BACZ,YACC,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBAChC,8OAAC;wBAAgB,WAAU;;0CACzB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;;;;;;;;;;kDAEjB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;;0CAGnB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;;;;;;;;;;;uBAXT;;;;gCAgBZ,MAAM,GAAG,CAAC,CAAC,qBACT,8OAAC;wBAAoB,WAAU;;0CAC7B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;;;;;;kDAEvB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAqC,KAAK,IAAI;;;;;;0DAC3D,8OAAC;gDAAE,WAAU;0DAAwC,KAAK,KAAK;;;;;;;;;;;;;;;;;;0CAGnE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,WAAW,CAAC,oBAAoB,EAC9B,KAAK,UAAU,KAAK,aAChB,mBACA,KAAK,UAAU,KAAK,aACpB,iBACA,iBACJ;8CAED,KAAK,MAAM;;;;;;;;;;;;uBApBR,KAAK,IAAI;;;;;;;;;;0BA6BzB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,qOAAA,CAAA,sBAAmB;wCAAC,WAAU;;;;;;;;;;;8CAEjC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAoC;;;;;;sDAClD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;kCAKnC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,iNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;8CAEvB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAoC;;;;;;sDAClD,8OAAC;4CAAE,WAAU;;gDAAgB;gDACd,eAAe,QAAQ,WAAW,cAAc,cAAc;;;;;;;;;;;;;;;;;;;;;;;;kCAMnF,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,iNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;8CAEvB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAoC;;;;;;sDAClD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOrC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;;;;;;kCAEtD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAGlC,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC,qOAAA,CAAA,sBAAmB;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AAO5D;uCAEe", "debugId": null}}, {"offset": {"line": 511, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/vo/digital-freight/frontend/node_modules/%40heroicons/react/24/outline/esm/MagnifyingGlassIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction MagnifyingGlassIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(MagnifyingGlassIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,oBAAoB,EAC3B,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 553, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/vo/digital-freight/frontend/node_modules/%40heroicons/react/24/outline/esm/ClockIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction ClockIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ClockIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,UAAU,EACjB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 595, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/vo/digital-freight/frontend/node_modules/%40heroicons/react/24/outline/esm/ChartBarIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction ChartBarIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ChartBarIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,aAAa,EACpB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}]}