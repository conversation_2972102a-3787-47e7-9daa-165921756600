{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/vo/digital-freight/frontend/src/components/search/PortAutocomplete.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useRef } from 'react';\nimport { MagnifyingGlassIcon, MapPinIcon } from '@heroicons/react/24/outline';\nimport axios from 'axios';\n\ninterface Port {\n  id: string;\n  code: string;\n  name: string;\n  countryName: string;\n  displayName: string;\n}\n\ninterface PortAutocompleteProps {\n  label: string;\n  placeholder?: string;\n  value?: Port | null;\n  onChange: (port: Port | null) => void;\n  error?: string;\n  disabled?: boolean;\n  required?: boolean;\n}\n\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001';\n\nconst PortAutocomplete: React.FC<PortAutocompleteProps> = ({\n  label,\n  placeholder = 'Search for a port...',\n  value,\n  onChange,\n  error,\n  disabled = false,\n  required = false,\n}) => {\n  const [query, setQuery] = useState('');\n  const [ports, setPorts] = useState<Port[]>([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [isOpen, setIsOpen] = useState(false);\n  const [popularPorts, setPopularPorts] = useState<Port[]>([]);\n  \n  const inputRef = useRef<HTMLInputElement>(null);\n  const dropdownRef = useRef<HTMLDivElement>(null);\n\n  // Load popular ports on mount\n  useEffect(() => {\n    const loadPopularPorts = async () => {\n      try {\n        const response = await axios.get(`${API_BASE_URL}/api/ports/popular?limit=8`);\n        setPopularPorts(response.data.data.ports);\n      } catch (error) {\n        console.error('Error loading popular ports:', error);\n      }\n    };\n\n    loadPopularPorts();\n  }, []);\n\n  // Search ports when query changes\n  useEffect(() => {\n    const searchPorts = async () => {\n      if (query.length < 2) {\n        setPorts([]);\n        return;\n      }\n\n      setIsLoading(true);\n      try {\n        const response = await axios.get(\n          `${API_BASE_URL}/api/ports/search?q=${encodeURIComponent(query)}&limit=10`\n        );\n        setPorts(response.data.data.ports);\n      } catch (error) {\n        console.error('Error searching ports:', error);\n        setPorts([]);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    const debounceTimer = setTimeout(searchPorts, 300);\n    return () => clearTimeout(debounceTimer);\n  }, [query]);\n\n  // Handle input change\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const newQuery = e.target.value;\n    setQuery(newQuery);\n    setIsOpen(true);\n\n    // Clear selection if input doesn't match selected port\n    if (value && !newQuery.toLowerCase().includes(value.code.toLowerCase()) && \n        !newQuery.toLowerCase().includes(value.name.toLowerCase())) {\n      onChange(null);\n    }\n  };\n\n  // Handle port selection\n  const handlePortSelect = (port: Port) => {\n    setQuery(port.displayName);\n    onChange(port);\n    setIsOpen(false);\n    inputRef.current?.blur();\n  };\n\n  // Handle input focus\n  const handleFocus = () => {\n    setIsOpen(true);\n    if (!query && popularPorts.length > 0) {\n      setPorts(popularPorts);\n    }\n  };\n\n  // Handle click outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setIsOpen(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n\n  // Update input when value changes externally\n  useEffect(() => {\n    if (value) {\n      setQuery(value.displayName);\n    } else if (!isOpen) {\n      setQuery('');\n    }\n  }, [value, isOpen]);\n\n  const displayPorts = query.length < 2 ? popularPorts : ports;\n\n  return (\n    <div className=\"relative\" ref={dropdownRef}>\n      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n        {label}\n        {required && <span className=\"text-red-500 ml-1\">*</span>}\n      </label>\n      \n      <div className=\"relative\">\n        <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n          <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400\" />\n        </div>\n        \n        <input\n          ref={inputRef}\n          type=\"text\"\n          value={query}\n          onChange={handleInputChange}\n          onFocus={handleFocus}\n          placeholder={placeholder}\n          disabled={disabled}\n          className={`w-full pl-10 pr-3 py-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 bg-white placeholder-gray-500 ${\n            error ? 'border-red-300' : 'border-gray-300'\n          } ${disabled ? 'bg-gray-50 cursor-not-allowed text-gray-500' : ''}`}\n        />\n        \n        {isLoading && (\n          <div className=\"absolute inset-y-0 right-0 pr-3 flex items-center\">\n            <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500\"></div>\n          </div>\n        )}\n      </div>\n\n      {error && (\n        <p className=\"mt-1 text-sm text-red-600\">{error}</p>\n      )}\n\n      {isOpen && (\n        <div className=\"absolute z-10 mt-1 w-full bg-white shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none\">\n          {displayPorts.length === 0 && !isLoading && query.length >= 2 && (\n            <div className=\"px-4 py-2 text-sm text-gray-500\">\n              No ports found for \"{query}\"\n            </div>\n          )}\n          \n          {displayPorts.length === 0 && !isLoading && query.length < 2 && (\n            <div className=\"px-4 py-2 text-sm text-gray-500\">\n              Type at least 2 characters to search\n            </div>\n          )}\n\n          {query.length < 2 && popularPorts.length > 0 && (\n            <div className=\"px-4 py-2 text-xs font-medium text-gray-400 uppercase tracking-wide\">\n              Popular Ports\n            </div>\n          )}\n\n          {displayPorts.map((port) => (\n            <button\n              key={port.id}\n              onClick={() => handlePortSelect(port)}\n              className=\"w-full text-left px-4 py-2 hover:bg-blue-50 focus:bg-blue-50 focus:outline-none\"\n            >\n              <div className=\"flex items-center\">\n                <MapPinIcon className=\"h-4 w-4 text-gray-400 mr-2 flex-shrink-0\" />\n                <div>\n                  <div className=\"text-sm font-medium text-gray-900\">\n                    {port.code} - {port.name}\n                  </div>\n                  <div className=\"text-xs text-gray-500\">{port.countryName}</div>\n                </div>\n              </div>\n            </button>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default PortAutocomplete;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAwBA,MAAM,eAAe,QAAQ,GAAG,CAAC,mBAAmB,IAAI;AAExD,MAAM,mBAAoD,CAAC,EACzD,KAAK,EACL,cAAc,sBAAsB,EACpC,KAAK,EACL,QAAQ,EACR,KAAK,EACL,WAAW,KAAK,EAChB,WAAW,KAAK,EACjB;IACC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAE3D,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,8BAA8B;IAC9B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,IAAI;gBACF,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,GAAG,aAAa,0BAA0B,CAAC;gBAC5E,gBAAgB,SAAS,IAAI,CAAC,IAAI,CAAC,KAAK;YAC1C,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,gCAAgC;YAChD;QACF;QAEA;IACF,GAAG,EAAE;IAEL,kCAAkC;IAClC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc;YAClB,IAAI,MAAM,MAAM,GAAG,GAAG;gBACpB,SAAS,EAAE;gBACX;YACF;YAEA,aAAa;YACb,IAAI;gBACF,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,GAAG,CAC9B,GAAG,aAAa,oBAAoB,EAAE,mBAAmB,OAAO,SAAS,CAAC;gBAE5E,SAAS,SAAS,IAAI,CAAC,IAAI,CAAC,KAAK;YACnC,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,SAAS,EAAE;YACb,SAAU;gBACR,aAAa;YACf;QACF;QAEA,MAAM,gBAAgB,WAAW,aAAa;QAC9C,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;KAAM;IAEV,sBAAsB;IACtB,MAAM,oBAAoB,CAAC;QACzB,MAAM,WAAW,EAAE,MAAM,CAAC,KAAK;QAC/B,SAAS;QACT,UAAU;QAEV,uDAAuD;QACvD,IAAI,SAAS,CAAC,SAAS,WAAW,GAAG,QAAQ,CAAC,MAAM,IAAI,CAAC,WAAW,OAChE,CAAC,SAAS,WAAW,GAAG,QAAQ,CAAC,MAAM,IAAI,CAAC,WAAW,KAAK;YAC9D,SAAS;QACX;IACF;IAEA,wBAAwB;IACxB,MAAM,mBAAmB,CAAC;QACxB,SAAS,KAAK,WAAW;QACzB,SAAS;QACT,UAAU;QACV,SAAS,OAAO,EAAE;IACpB;IAEA,qBAAqB;IACrB,MAAM,cAAc;QAClB,UAAU;QACV,IAAI,CAAC,SAAS,aAAa,MAAM,GAAG,GAAG;YACrC,SAAS;QACX;IACF;IAEA,uBAAuB;IACvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAC9E,UAAU;YACZ;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;IACzD,GAAG,EAAE;IAEL,6CAA6C;IAC7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,OAAO;YACT,SAAS,MAAM,WAAW;QAC5B,OAAO,IAAI,CAAC,QAAQ;YAClB,SAAS;QACX;IACF,GAAG;QAAC;QAAO;KAAO;IAElB,MAAM,eAAe,MAAM,MAAM,GAAG,IAAI,eAAe;IAEvD,qBACE,8OAAC;QAAI,WAAU;QAAW,KAAK;;0BAC7B,8OAAC;gBAAM,WAAU;;oBACd;oBACA,0BAAY,8OAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;0BAGnD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,qOAAA,CAAA,sBAAmB;4BAAC,WAAU;;;;;;;;;;;kCAGjC,8OAAC;wBACC,KAAK;wBACL,MAAK;wBACL,OAAO;wBACP,UAAU;wBACV,SAAS;wBACT,aAAa;wBACb,UAAU;wBACV,WAAW,CAAC,yKAAyK,EACnL,QAAQ,mBAAmB,kBAC5B,CAAC,EAAE,WAAW,gDAAgD,IAAI;;;;;;oBAGpE,2BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;YAKpB,uBACC,8OAAC;gBAAE,WAAU;0BAA6B;;;;;;YAG3C,wBACC,8OAAC;gBAAI,WAAU;;oBACZ,aAAa,MAAM,KAAK,KAAK,CAAC,aAAa,MAAM,MAAM,IAAI,mBAC1D,8OAAC;wBAAI,WAAU;;4BAAkC;4BAC1B;4BAAM;;;;;;;oBAI9B,aAAa,MAAM,KAAK,KAAK,CAAC,aAAa,MAAM,MAAM,GAAG,mBACzD,8OAAC;wBAAI,WAAU;kCAAkC;;;;;;oBAKlD,MAAM,MAAM,GAAG,KAAK,aAAa,MAAM,GAAG,mBACzC,8OAAC;wBAAI,WAAU;kCAAsE;;;;;;oBAKtF,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC;4BAEC,SAAS,IAAM,iBAAiB;4BAChC,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,mNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;;oDACZ,KAAK,IAAI;oDAAC;oDAAI,KAAK,IAAI;;;;;;;0DAE1B,8OAAC;gDAAI,WAAU;0DAAyB,KAAK,WAAW;;;;;;;;;;;;;;;;;;2BAVvD,KAAK,EAAE;;;;;;;;;;;;;;;;;AAmB1B;uCAEe", "debugId": null}}, {"offset": {"line": 294, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/vo/digital-freight/frontend/src/components/search/SearchForm.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { CalendarIcon, MagnifyingGlassIcon, ArrowsRightLeftIcon } from '@heroicons/react/24/outline';\nimport PortAutocomplete from './PortAutocomplete';\nimport DatePicker from 'react-datepicker';\nimport 'react-datepicker/dist/react-datepicker.css';\n\ninterface Port {\n  id: string;\n  code: string;\n  name: string;\n  countryName: string;\n  displayName: string;\n}\n\ninterface SearchFormData {\n  originPort: Port | null;\n  destinationPort: Port | null;\n  departureDate: Date | null;\n}\n\ninterface SearchFormProps {\n  onSearch: (searchData: SearchFormData) => void;\n  isLoading?: boolean;\n}\n\nconst SearchForm: React.FC<SearchFormProps> = ({ onSearch, isLoading = false }) => {\n  const [formData, setFormData] = useState<SearchFormData>({\n    originPort: null,\n    destinationPort: null,\n    departureDate: null,\n  });\n  const [errors, setErrors] = useState<Record<string, string>>({});\n\n  const handleOriginChange = (port: Port | null) => {\n    setFormData(prev => ({ ...prev, originPort: port }));\n    if (errors.originPort) {\n      setErrors(prev => ({ ...prev, originPort: '' }));\n    }\n  };\n\n  const handleDestinationChange = (port: Port | null) => {\n    setFormData(prev => ({ ...prev, destinationPort: port }));\n    if (errors.destinationPort) {\n      setErrors(prev => ({ ...prev, destinationPort: '' }));\n    }\n  };\n\n  const handleDateChange = (date: Date | null) => {\n    setFormData(prev => ({ ...prev, departureDate: date }));\n    if (errors.departureDate) {\n      setErrors(prev => ({ ...prev, departureDate: '' }));\n    }\n  };\n\n  const handleSwapPorts = () => {\n    setFormData(prev => ({\n      ...prev,\n      originPort: prev.destinationPort,\n      destinationPort: prev.originPort,\n    }));\n  };\n\n  const validateForm = (): boolean => {\n    const newErrors: Record<string, string> = {};\n\n    if (!formData.originPort) {\n      newErrors.originPort = 'Origin port is required';\n    }\n\n    if (!formData.destinationPort) {\n      newErrors.destinationPort = 'Destination port is required';\n    }\n\n    if (!formData.departureDate) {\n      newErrors.departureDate = 'Departure date is required';\n    } else if (formData.departureDate < new Date()) {\n      newErrors.departureDate = 'Departure date cannot be in the past';\n    }\n\n    if (formData.originPort && formData.destinationPort && \n        formData.originPort.id === formData.destinationPort.id) {\n      newErrors.destinationPort = 'Destination port must be different from origin port';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (validateForm()) {\n      onSearch(formData);\n    }\n  };\n\n  const minDate = new Date();\n  const maxDate = new Date();\n  maxDate.setMonth(maxDate.getMonth() + 6); // Allow booking up to 6 months ahead\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-lg p-6\">\n      <div className=\"mb-6\">\n        <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">Search Shipping Routes</h2>\n        <p className=\"text-gray-600\">Find the best shipping options across multiple carriers</p>\n      </div>\n\n      <form onSubmit={handleSubmit} className=\"space-y-6\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 relative\">\n          {/* Origin Port */}\n          <div>\n            <PortAutocomplete\n              label=\"Origin Port\"\n              placeholder=\"Search origin port (e.g., Shanghai, SHG)\"\n              value={formData.originPort}\n              onChange={handleOriginChange}\n              error={errors.originPort}\n              disabled={isLoading}\n              required\n            />\n          </div>\n\n          {/* Swap Ports Button - Centered between the two fields */}\n          <div className=\"absolute left-1/2 top-8 transform -translate-x-1/2 z-20 hidden md:block\">\n            <button\n              type=\"button\"\n              onClick={handleSwapPorts}\n              disabled={isLoading}\n              className=\"p-3 bg-white border-2 border-blue-200 text-blue-600 hover:text-blue-800 hover:bg-blue-50 hover:border-blue-300 rounded-full shadow-md transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed\"\n              title=\"Swap origin and destination\"\n            >\n              <ArrowsRightLeftIcon className=\"h-5 w-5\" />\n            </button>\n          </div>\n\n          {/* Mobile Swap Button */}\n          <div className=\"md:hidden flex justify-center -my-2\">\n            <button\n              type=\"button\"\n              onClick={handleSwapPorts}\n              disabled={isLoading}\n              className=\"p-2 bg-blue-50 border border-blue-200 text-blue-600 hover:text-blue-800 hover:bg-blue-100 rounded-full transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed\"\n              title=\"Swap origin and destination\"\n            >\n              <ArrowsRightLeftIcon className=\"h-4 w-4\" />\n            </button>\n          </div>\n\n          {/* Destination Port */}\n          <div>\n            <PortAutocomplete\n              label=\"Destination Port\"\n              placeholder=\"Search destination port (e.g., Rotterdam, RTM)\"\n              value={formData.destinationPort}\n              onChange={handleDestinationChange}\n              error={errors.destinationPort}\n              disabled={isLoading}\n              required\n            />\n          </div>\n        </div>\n\n        {/* Departure Date */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            Departure Date\n            <span className=\"text-red-500 ml-1\">*</span>\n          </label>\n          <div className=\"relative\">\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n              <CalendarIcon className=\"h-5 w-5 text-gray-400\" />\n            </div>\n            <DatePicker\n              selected={formData.departureDate}\n              onChange={handleDateChange}\n              minDate={minDate}\n              maxDate={maxDate}\n              placeholderText=\"Select departure date\"\n              disabled={isLoading}\n              className={`w-full pl-10 pr-3 py-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 bg-white ${\n                errors.departureDate ? 'border-red-300' : 'border-gray-300'\n              } ${isLoading ? 'bg-gray-50 cursor-not-allowed' : ''}`}\n              dateFormat=\"MMM dd, yyyy\"\n              showPopperArrow={false}\n            />\n          </div>\n          {errors.departureDate && (\n            <p className=\"mt-1 text-sm text-red-600\">{errors.departureDate}</p>\n          )}\n        </div>\n\n        {/* Search Button */}\n        <div className=\"flex justify-center\">\n          <button\n            type=\"submit\"\n            disabled={isLoading}\n            className=\"flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed min-w-[200px] justify-center\"\n          >\n            {isLoading ? (\n              <>\n                <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2\"></div>\n                Searching...\n              </>\n            ) : (\n              <>\n                <MagnifyingGlassIcon className=\"h-5 w-5 mr-2\" />\n                Search Routes\n              </>\n            )}\n          </button>\n        </div>\n      </form>\n    </div>\n  );\n};\n\nexport default SearchForm;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AACA;AALA;;;;;;;AA2BA,MAAM,aAAwC,CAAC,EAAE,QAAQ,EAAE,YAAY,KAAK,EAAE;IAC5E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;QACvD,YAAY;QACZ,iBAAiB;QACjB,eAAe;IACjB;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAE9D,MAAM,qBAAqB,CAAC;QAC1B,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,YAAY;YAAK,CAAC;QAClD,IAAI,OAAO,UAAU,EAAE;YACrB,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,YAAY;gBAAG,CAAC;QAChD;IACF;IAEA,MAAM,0BAA0B,CAAC;QAC/B,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,iBAAiB;YAAK,CAAC;QACvD,IAAI,OAAO,eAAe,EAAE;YAC1B,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,iBAAiB;gBAAG,CAAC;QACrD;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,eAAe;YAAK,CAAC;QACrD,IAAI,OAAO,aAAa,EAAE;YACxB,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,eAAe;gBAAG,CAAC;QACnD;IACF;IAEA,MAAM,kBAAkB;QACtB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,YAAY,KAAK,eAAe;gBAChC,iBAAiB,KAAK,UAAU;YAClC,CAAC;IACH;IAEA,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,IAAI,CAAC,SAAS,UAAU,EAAE;YACxB,UAAU,UAAU,GAAG;QACzB;QAEA,IAAI,CAAC,SAAS,eAAe,EAAE;YAC7B,UAAU,eAAe,GAAG;QAC9B;QAEA,IAAI,CAAC,SAAS,aAAa,EAAE;YAC3B,UAAU,aAAa,GAAG;QAC5B,OAAO,IAAI,SAAS,aAAa,GAAG,IAAI,QAAQ;YAC9C,UAAU,aAAa,GAAG;QAC5B;QAEA,IAAI,SAAS,UAAU,IAAI,SAAS,eAAe,IAC/C,SAAS,UAAU,CAAC,EAAE,KAAK,SAAS,eAAe,CAAC,EAAE,EAAE;YAC1D,UAAU,eAAe,GAAG;QAC9B;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAEhB,IAAI,gBAAgB;YAClB,SAAS;QACX;IACF;IAEA,MAAM,UAAU,IAAI;IACpB,MAAM,UAAU,IAAI;IACpB,QAAQ,QAAQ,CAAC,QAAQ,QAAQ,KAAK,IAAI,qCAAqC;IAE/E,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAG/B,8OAAC;gBAAK,UAAU;gBAAc,WAAU;;kCACtC,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;0CACC,cAAA,8OAAC,gJAAA,CAAA,UAAgB;oCACf,OAAM;oCACN,aAAY;oCACZ,OAAO,SAAS,UAAU;oCAC1B,UAAU;oCACV,OAAO,OAAO,UAAU;oCACxB,UAAU;oCACV,QAAQ;;;;;;;;;;;0CAKZ,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,MAAK;oCACL,SAAS;oCACT,UAAU;oCACV,WAAU;oCACV,OAAM;8CAEN,cAAA,8OAAC,qOAAA,CAAA,sBAAmB;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAKnC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,MAAK;oCACL,SAAS;oCACT,UAAU;oCACV,WAAU;oCACV,OAAM;8CAEN,cAAA,8OAAC,qOAAA,CAAA,sBAAmB;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAKnC,8OAAC;0CACC,cAAA,8OAAC,gJAAA,CAAA,UAAgB;oCACf,OAAM;oCACN,aAAY;oCACZ,OAAO,SAAS,eAAe;oCAC/B,UAAU;oCACV,OAAO,OAAO,eAAe;oCAC7B,UAAU;oCACV,QAAQ;;;;;;;;;;;;;;;;;kCAMd,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;;oCAA+C;kDAE9D,8OAAC;wCAAK,WAAU;kDAAoB;;;;;;;;;;;;0CAEtC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,uNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;kDAE1B,8OAAC,0JAAA,CAAA,UAAU;wCACT,UAAU,SAAS,aAAa;wCAChC,UAAU;wCACV,SAAS;wCACT,SAAS;wCACT,iBAAgB;wCAChB,UAAU;wCACV,WAAW,CAAC,oJAAoJ,EAC9J,OAAO,aAAa,GAAG,mBAAmB,kBAC3C,CAAC,EAAE,YAAY,kCAAkC,IAAI;wCACtD,YAAW;wCACX,iBAAiB;;;;;;;;;;;;4BAGpB,OAAO,aAAa,kBACnB,8OAAC;gCAAE,WAAU;0CAA6B,OAAO,aAAa;;;;;;;;;;;;kCAKlE,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,MAAK;4BACL,UAAU;4BACV,WAAU;sCAET,0BACC;;kDACE,8OAAC;wCAAI,WAAU;;;;;;oCAAuE;;6DAIxF;;kDACE,8OAAC,qOAAA,CAAA,sBAAmB;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AAShE;uCAEe", "debugId": null}}, {"offset": {"line": 649, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/vo/digital-freight/frontend/src/components/search/SearchResults.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport {\n  TruckIcon,\n  ClockIcon,\n  MapPinIcon,\n  EyeIcon,\n  XMarkIcon\n} from '@heroicons/react/24/outline';\n\nexport interface SearchResult {\n  id: string;\n  carrier: {\n    name: string;\n    code: string;\n  };\n  vesselName: string;\n  voyageNumber: string;\n  serviceName?: string;\n  departureDate: string;\n  arrivalDate: string;\n  transitDays: number;\n  portRotation: Array<{\n    portCode: string;\n    portName: string;\n    country: string;\n    sequenceOrder: number;\n    arrivalDate?: string;\n    departureDate?: string;\n  }>;\n  rawApiResponse?: any;\n}\n\nexport interface SearchResponse {\n  searchId: string;\n  results: SearchResult[];\n  totalResults: number;\n  searchDurationMs: number;\n  carriers: string[];\n  metadata: {\n    originPort: {\n      code: string;\n      name: string;\n      country: string;\n    };\n    destinationPort: {\n      code: string;\n      name: string;\n      country: string;\n    };\n    departureDate: string;\n    searchTimestamp: string;\n  };\n}\n\ninterface SearchResultsProps {\n  searchResponse: SearchResponse | null;\n  isLoading: boolean;\n  onViewDetails?: (result: SearchResult) => void;\n}\n\nconst SearchResults: React.FC<SearchResultsProps> = ({\n  searchResponse,\n  isLoading,\n  onViewDetails,\n}) => {\n  const [selectedResult, setSelectedResult] = useState<SearchResult | null>(null);\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      month: 'short',\n      day: 'numeric',\n      year: 'numeric',\n    });\n  };\n\n  const formatDuration = (ms: number) => {\n    if (ms < 1000) return `${ms}ms`;\n    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;\n    return `${(ms / 60000).toFixed(1)}min`;\n  };\n\n  const handleViewDetails = (result: SearchResult) => {\n    setSelectedResult(result);\n    onViewDetails?.(result);\n  };\n\n  const closeDetails = () => {\n    setSelectedResult(null);\n  };\n\n  const getCarrierColor = (carrierCode: string) => {\n    const colors: Record<string, string> = {\n      MAEU: 'bg-blue-600 text-white',\n      HLCU: 'bg-orange-600 text-white',\n      CMDU: 'bg-green-600 text-white',\n      MSCU: 'bg-purple-600 text-white',\n    };\n    return colors[carrierCode] || 'bg-gray-600 text-white';\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"text-center py-12\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n        <p className=\"text-gray-600\">Searching across multiple carriers...</p>\n        <p className=\"text-sm text-gray-500 mt-2\">This may take a few moments</p>\n      </div>\n    );\n  }\n\n  if (!searchResponse) {\n    return (\n      <div className=\"text-center py-12\">\n        <TruckIcon className=\"h-16 w-16 text-gray-300 mx-auto mb-4\" />\n        <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Ready to Search</h3>\n        <p className=\"text-gray-600\">\n          Enter your search criteria above to find shipping routes.\n        </p>\n      </div>\n    );\n  }\n\n  if (searchResponse.results.length === 0) {\n    return (\n      <div className=\"text-center py-12\">\n        <TruckIcon className=\"h-16 w-16 text-gray-300 mx-auto mb-4\" />\n        <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No Routes Found</h3>\n        <p className=\"text-gray-600\">\n          No shipping schedules found for your search criteria. Try different ports or dates.\n        </p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Search Summary */}\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <div className=\"mb-4\">\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">\n            Search Results ({searchResponse.totalResults})\n          </h2>\n          <p className=\"text-gray-600 mb-1\">\n            <span className=\"font-medium\">{searchResponse.metadata.originPort.name}</span> ({searchResponse.metadata.originPort.code}) → {' '}\n            <span className=\"font-medium\">{searchResponse.metadata.destinationPort.name}</span> ({searchResponse.metadata.destinationPort.code})\n          </p>\n          <p className=\"text-sm text-gray-500\">\n            Departure: {formatDate(searchResponse.metadata.departureDate)} •\n            Search completed in {formatDuration(searchResponse.searchDurationMs)} •\n            {searchResponse.carriers.length} carrier(s)\n          </p>\n        </div>\n      </div>\n\n      {/* Results List */}\n      <div className=\"space-y-4\">\n        {searchResponse.results.map((result) => (\n          <div key={result.id} className=\"bg-white rounded-lg shadow hover:shadow-md transition-shadow duration-200\">\n            <div className=\"p-6\">\n              <div className=\"flex items-start justify-between mb-4\">\n                <div className=\"flex-1\">\n                  <div className=\"flex items-center space-x-3 mb-2\">\n                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${getCarrierColor(result.carrier.code)}`}>\n                      {result.carrier.name}\n                    </span>\n                    {result.serviceName && (\n                      <span className=\"px-2 py-1 bg-gray-100 text-gray-700 rounded text-sm\">\n                        {result.serviceName}\n                      </span>\n                    )}\n                  </div>\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-1\">\n                    {result.vesselName}\n                  </h3>\n                  <p className=\"text-gray-600\">\n                    Voyage: {result.voyageNumber}\n                  </p>\n                </div>\n                <button\n                  onClick={() => handleViewDetails(result)}\n                  className=\"flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200\"\n                >\n                  <EyeIcon className=\"h-4 w-4 mr-2\" />\n                  View Details\n                </button>\n              </div>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                <div className=\"flex items-center space-x-2\">\n                  <MapPinIcon className=\"h-5 w-5 text-gray-400\" />\n                  <div>\n                    <p className=\"text-sm font-medium text-gray-900\">Departure</p>\n                    <p className=\"text-sm text-gray-600\">{formatDate(result.departureDate)}</p>\n                  </div>\n                </div>\n                <div className=\"flex items-center space-x-2\">\n                  <MapPinIcon className=\"h-5 w-5 text-gray-400\" />\n                  <div>\n                    <p className=\"text-sm font-medium text-gray-900\">Arrival</p>\n                    <p className=\"text-sm text-gray-600\">{formatDate(result.arrivalDate)}</p>\n                  </div>\n                </div>\n                <div className=\"flex items-center space-x-2\">\n                  <ClockIcon className=\"h-5 w-5 text-gray-400\" />\n                  <div>\n                    <p className=\"text-sm font-medium text-gray-900\">Transit Time</p>\n                    <p className=\"text-sm text-gray-600\">{result.transitDays} days</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* Details Modal */}\n      {selectedResult && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n          <div className=\"bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto\">\n            <div className=\"p-6\">\n              <div className=\"flex items-center justify-between mb-6\">\n                <h2 className=\"text-2xl font-bold text-gray-900\">Route Details</h2>\n                <button\n                  onClick={closeDetails}\n                  className=\"p-2 hover:bg-gray-100 rounded-full transition-colors duration-200\"\n                >\n                  <XMarkIcon className=\"h-6 w-6 text-gray-500\" />\n                </button>\n              </div>\n\n              <div className=\"space-y-6\">\n                {/* Vessel Information */}\n                <div className=\"bg-gray-50 rounded-lg p-4\">\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-3\">Vessel Information</h3>\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-700\">Vessel Name</p>\n                      <p className=\"text-lg text-gray-900\">{selectedResult.vesselName}</p>\n                    </div>\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-700\">Voyage Number</p>\n                      <p className=\"text-lg text-gray-900\">{selectedResult.voyageNumber}</p>\n                    </div>\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-700\">Carrier</p>\n                      <p className=\"text-lg text-gray-900\">{selectedResult.carrier.name}</p>\n                    </div>\n                    {selectedResult.serviceName && (\n                      <div>\n                        <p className=\"text-sm font-medium text-gray-700\">Service</p>\n                        <p className=\"text-lg text-gray-900\">{selectedResult.serviceName}</p>\n                      </div>\n                    )}\n                  </div>\n                </div>\n\n                {/* Schedule Information */}\n                <div className=\"bg-gray-50 rounded-lg p-4\">\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-3\">Schedule</h3>\n                  <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-700\">Departure Date</p>\n                      <p className=\"text-lg text-gray-900\">{formatDate(selectedResult.departureDate)}</p>\n                    </div>\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-700\">Arrival Date</p>\n                      <p className=\"text-lg text-gray-900\">{formatDate(selectedResult.arrivalDate)}</p>\n                    </div>\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-700\">Transit Time</p>\n                      <p className=\"text-lg text-gray-900\">{selectedResult.transitDays} days</p>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Port Rotation */}\n                {selectedResult.portRotation && selectedResult.portRotation.length > 0 && (\n                  <div className=\"bg-gray-50 rounded-lg p-4\">\n                    <h3 className=\"text-lg font-semibold text-gray-900 mb-3\">Port Rotation</h3>\n                    <div className=\"space-y-3\">\n                      {selectedResult.portRotation.map((port, index) => (\n                        <div key={index} className=\"flex items-center justify-between p-3 bg-white rounded border\">\n                          <div>\n                            <p className=\"font-medium text-gray-900\">{port.portName}</p>\n                            <p className=\"text-sm text-gray-600\">{port.portCode} • {port.country}</p>\n                          </div>\n                          <div className=\"text-right\">\n                            {port.arrivalDate && (\n                              <p className=\"text-sm text-gray-600\">Arrival: {formatDate(port.arrivalDate)}</p>\n                            )}\n                            {port.departureDate && (\n                              <p className=\"text-sm text-gray-600\">Departure: {formatDate(port.departureDate)}</p>\n                            )}\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default SearchResults;"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AA8DA,MAAM,gBAA8C,CAAC,EACnD,cAAc,EACd,SAAS,EACT,aAAa,EACd;IACC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IAE1E,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,OAAO;YACP,KAAK;YACL,MAAM;QACR;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,KAAK,MAAM,OAAO,GAAG,GAAG,EAAE,CAAC;QAC/B,IAAI,KAAK,OAAO,OAAO,GAAG,CAAC,KAAK,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QACnD,OAAO,GAAG,CAAC,KAAK,KAAK,EAAE,OAAO,CAAC,GAAG,GAAG,CAAC;IACxC;IAEA,MAAM,oBAAoB,CAAC;QACzB,kBAAkB;QAClB,gBAAgB;IAClB;IAEA,MAAM,eAAe;QACnB,kBAAkB;IACpB;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,SAAiC;YACrC,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,OAAO,MAAM,CAAC,YAAY,IAAI;IAChC;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;oBAAE,WAAU;8BAAgB;;;;;;8BAC7B,8OAAC;oBAAE,WAAU;8BAA6B;;;;;;;;;;;;IAGhD;IAEA,IAAI,CAAC,gBAAgB;QACnB,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,iNAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;8BACrB,8OAAC;oBAAG,WAAU;8BAAyC;;;;;;8BACvD,8OAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;IAKnC;IAEA,IAAI,eAAe,OAAO,CAAC,MAAM,KAAK,GAAG;QACvC,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,iNAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;8BACrB,8OAAC;oBAAG,WAAU;8BAAyC;;;;;;8BACvD,8OAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;IAKnC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;gCAAwC;gCACnC,eAAe,YAAY;gCAAC;;;;;;;sCAE/C,8OAAC;4BAAE,WAAU;;8CACX,8OAAC;oCAAK,WAAU;8CAAe,eAAe,QAAQ,CAAC,UAAU,CAAC,IAAI;;;;;;gCAAQ;gCAAG,eAAe,QAAQ,CAAC,UAAU,CAAC,IAAI;gCAAC;gCAAK;8CAC9H,8OAAC;oCAAK,WAAU;8CAAe,eAAe,QAAQ,CAAC,eAAe,CAAC,IAAI;;;;;;gCAAQ;gCAAG,eAAe,QAAQ,CAAC,eAAe,CAAC,IAAI;gCAAC;;;;;;;sCAErI,8OAAC;4BAAE,WAAU;;gCAAwB;gCACvB,WAAW,eAAe,QAAQ,CAAC,aAAa;gCAAE;gCACzC,eAAe,eAAe,gBAAgB;gCAAE;gCACpE,eAAe,QAAQ,CAAC,MAAM;gCAAC;;;;;;;;;;;;;;;;;;0BAMtC,8OAAC;gBAAI,WAAU;0BACZ,eAAe,OAAO,CAAC,GAAG,CAAC,CAAC,uBAC3B,8OAAC;wBAAoB,WAAU;kCAC7B,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAW,CAAC,2CAA2C,EAAE,gBAAgB,OAAO,OAAO,CAAC,IAAI,GAAG;sEAClG,OAAO,OAAO,CAAC,IAAI;;;;;;wDAErB,OAAO,WAAW,kBACjB,8OAAC;4DAAK,WAAU;sEACb,OAAO,WAAW;;;;;;;;;;;;8DAIzB,8OAAC;oDAAG,WAAU;8DACX,OAAO,UAAU;;;;;;8DAEpB,8OAAC;oDAAE,WAAU;;wDAAgB;wDAClB,OAAO,YAAY;;;;;;;;;;;;;sDAGhC,8OAAC;4CACC,SAAS,IAAM,kBAAkB;4CACjC,WAAU;;8DAEV,8OAAC,6MAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;8CAKxC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,mNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAAoC;;;;;;sEACjD,8OAAC;4DAAE,WAAU;sEAAyB,WAAW,OAAO,aAAa;;;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,mNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAAoC;;;;;;sEACjD,8OAAC;4DAAE,WAAU;sEAAyB,WAAW,OAAO,WAAW;;;;;;;;;;;;;;;;;;sDAGvE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;8DACrB,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAAoC;;;;;;sEACjD,8OAAC;4DAAE,WAAU;;gEAAyB,OAAO,WAAW;gEAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBAjDzD,OAAO,EAAE;;;;;;;;;;YA2DtB,gCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,8OAAC;wCACC,SAAS;wCACT,WAAU;kDAEV,cAAA,8OAAC,iNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAIzB,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAoC;;;;;;0EACjD,8OAAC;gEAAE,WAAU;0EAAyB,eAAe,UAAU;;;;;;;;;;;;kEAEjE,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAoC;;;;;;0EACjD,8OAAC;gEAAE,WAAU;0EAAyB,eAAe,YAAY;;;;;;;;;;;;kEAEnE,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAoC;;;;;;0EACjD,8OAAC;gEAAE,WAAU;0EAAyB,eAAe,OAAO,CAAC,IAAI;;;;;;;;;;;;oDAElE,eAAe,WAAW,kBACzB,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAoC;;;;;;0EACjD,8OAAC;gEAAE,WAAU;0EAAyB,eAAe,WAAW;;;;;;;;;;;;;;;;;;;;;;;;kDAOxE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAoC;;;;;;0EACjD,8OAAC;gEAAE,WAAU;0EAAyB,WAAW,eAAe,aAAa;;;;;;;;;;;;kEAE/E,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAoC;;;;;;0EACjD,8OAAC;gEAAE,WAAU;0EAAyB,WAAW,eAAe,WAAW;;;;;;;;;;;;kEAE7E,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAoC;;;;;;0EACjD,8OAAC;gEAAE,WAAU;;oEAAyB,eAAe,WAAW;oEAAC;;;;;;;;;;;;;;;;;;;;;;;;;oCAMtE,eAAe,YAAY,IAAI,eAAe,YAAY,CAAC,MAAM,GAAG,mBACnE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,8OAAC;gDAAI,WAAU;0DACZ,eAAe,YAAY,CAAC,GAAG,CAAC,CAAC,MAAM,sBACtC,8OAAC;wDAAgB,WAAU;;0EACzB,8OAAC;;kFACC,8OAAC;wEAAE,WAAU;kFAA6B,KAAK,QAAQ;;;;;;kFACvD,8OAAC;wEAAE,WAAU;;4EAAyB,KAAK,QAAQ;4EAAC;4EAAI,KAAK,OAAO;;;;;;;;;;;;;0EAEtE,8OAAC;gEAAI,WAAU;;oEACZ,KAAK,WAAW,kBACf,8OAAC;wEAAE,WAAU;;4EAAwB;4EAAU,WAAW,KAAK,WAAW;;;;;;;oEAE3E,KAAK,aAAa,kBACjB,8OAAC;wEAAE,WAAU;;4EAAwB;4EAAY,WAAW,KAAK,aAAa;;;;;;;;;;;;;;uDAV1E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyBlC;uCAEe", "debugId": null}}, {"offset": {"line": 1502, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/vo/digital-freight/frontend/src/app/search/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport axios from 'axios';\nimport SearchForm from '@/components/search/SearchForm';\nimport SearchResults, { SearchResponse } from '@/components/search/SearchResults';\nimport { TruckIcon } from '@heroicons/react/24/outline';\n\ninterface Port {\n  id: string;\n  code: string;\n  name: string;\n  countryName: string;\n  displayName: string;\n}\n\ninterface SearchFormData {\n  originPort: Port | null;\n  destinationPort: Port | null;\n  departureDate: Date | null;\n}\n\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001';\n\nconst SearchPage: React.FC = () => {\n  const [searchResponse, setSearchResponse] = useState<SearchResponse | null>(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string>('');\n\n  const handleSearch = async (searchData: SearchFormData) => {\n    if (!searchData.originPort || !searchData.destinationPort || !searchData.departureDate) {\n      setError('Please fill in all search fields');\n      return;\n    }\n\n    setIsLoading(true);\n    setError('');\n\n    try {\n      const response = await axios.post(`${API_BASE_URL}/api/search/routes`, {\n        originPortId: searchData.originPort.id,\n        destinationPortId: searchData.destinationPort.id,\n        departureDate: searchData.departureDate.toISOString().split('T')[0],\n        carriers: ['MAEU'], // Start with Maersk only\n      });\n\n      setSearchResponse(response.data.data);\n    } catch (err: any) {\n      let errorMessage = 'Search failed. Please try again.';\n\n      if (err.response?.data?.message) {\n        errorMessage = err.response.data.message;\n      } else if (err.code === 'NETWORK_ERROR') {\n        errorMessage = 'Network error. Please check your internet connection.';\n      } else if (err.code === 'ECONNABORTED') {\n        errorMessage = 'Search request timed out. Please try again.';\n      }\n\n      setError(errorMessage);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n\n\n  const [selectedResult, setSelectedResult] = useState<any>(null);\n\n  const handleViewDetails = (result: any) => {\n    setSelectedResult(result);\n  };\n\n  const closeDetails = () => {\n    setSelectedResult(null);\n  };\n\n  return (\n    <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n      {/* Page Header */}\n      <div className=\"text-center mb-8\">\n        <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">\n          Find Your Perfect Shipping Route\n        </h1>\n        <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n          Search across multiple carriers to find the best shipping options for your freight forwarding needs.\n        </p>\n      </div>\n\n      {/* Search Form */}\n      <div className=\"mb-8\">\n        <SearchForm onSearch={handleSearch} isLoading={isLoading} />\n      </div>\n\n      {/* Error Message */}\n      {error && (\n        <div className=\"mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md\">\n          {error}\n        </div>\n      )}\n\n      {/* Search Results */}\n      <SearchResults\n        searchResponse={searchResponse}\n        isLoading={isLoading}\n        onViewDetails={handleViewDetails}\n      />\n    </div>\n  );\n};\n\nexport default SearchPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAsBA,MAAM,eAAe,QAAQ,GAAG,CAAC,mBAAmB,IAAI;AAExD,MAAM,aAAuB;IAC3B,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;IAC5E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE3C,MAAM,eAAe,OAAO;QAC1B,IAAI,CAAC,WAAW,UAAU,IAAI,CAAC,WAAW,eAAe,IAAI,CAAC,WAAW,aAAa,EAAE;YACtF,SAAS;YACT;QACF;QAEA,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,GAAG,aAAa,kBAAkB,CAAC,EAAE;gBACrE,cAAc,WAAW,UAAU,CAAC,EAAE;gBACtC,mBAAmB,WAAW,eAAe,CAAC,EAAE;gBAChD,eAAe,WAAW,aAAa,CAAC,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gBACnE,UAAU;oBAAC;iBAAO;YACpB;YAEA,kBAAkB,SAAS,IAAI,CAAC,IAAI;QACtC,EAAE,OAAO,KAAU;YACjB,IAAI,eAAe;YAEnB,IAAI,IAAI,QAAQ,EAAE,MAAM,SAAS;gBAC/B,eAAe,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO;YAC1C,OAAO,IAAI,IAAI,IAAI,KAAK,iBAAiB;gBACvC,eAAe;YACjB,OAAO,IAAI,IAAI,IAAI,KAAK,gBAAgB;gBACtC,eAAe;YACjB;YAEA,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAIA,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAE1D,MAAM,oBAAoB,CAAC;QACzB,kBAAkB;IACpB;IAEA,MAAM,eAAe;QACnB,kBAAkB;IACpB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAGtD,8OAAC;wBAAE,WAAU;kCAA0C;;;;;;;;;;;;0BAMzD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0IAAA,CAAA,UAAU;oBAAC,UAAU;oBAAc,WAAW;;;;;;;;;;;YAIhD,uBACC,8OAAC;gBAAI,WAAU;0BACZ;;;;;;0BAKL,8OAAC,6IAAA,CAAA,UAAa;gBACZ,gBAAgB;gBAChB,WAAW;gBACX,eAAe;;;;;;;;;;;;AAIvB;uCAEe", "debugId": null}}]}