(()=>{var e={};e.id=365,e.ids=[365],e.modules={440:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>a});var t=r(1658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1449:(e,s,r)=>{Promise.resolve().then(r.bind(r,2239))},1630:e=>{"use strict";e.exports=require("http")},1697:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var t=r(5239),a=r(8088),o=r(8170),i=r.n(o),l=r(893),n={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);r.d(s,n);let d={children:["",{children:["auth",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,8305)),"/home/<USER>/vo/digital-freight/frontend/src/app/auth/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"/home/<USER>/vo/digital-freight/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/home/<USER>/vo/digital-freight/frontend/src/app/auth/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/auth/page",pathname:"/auth",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},1820:e=>{"use strict";e.exports=require("os")},2239:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>u});var t=r(687),a=r(3210),o=r(5773),i=r(3213);let l=a.forwardRef(function({title:e,titleId:s,...r},t){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},r),e?a.createElement("title",{id:s},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88"}))});var n=r(6524);let d=({onSuccess:e,onSwitchToRegister:s})=>{let{login:r,isLoading:o}=(0,i.h)(),[d,c]=(0,a.useState)({email:"",password:""}),[u,m]=(0,a.useState)(!1),[p,x]=(0,a.useState)(""),h=e=>{let{name:s,value:r}=e.target;c(e=>({...e,[s]:r})),p&&x("")},f=async s=>{if(s.preventDefault(),x(""),!d.email||!d.password)return void x("Please fill in all fields");if(!d.email.includes("@"))return void x("Please enter a valid email address");try{await r(d.email,d.password),e?.()}catch(e){x(e.message||"Login failed")}};return(0,t.jsx)("div",{className:"w-full max-w-md mx-auto",children:(0,t.jsxs)("div",{className:"bg-white shadow-lg rounded-lg p-8",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("h2",{className:"text-3xl font-bold text-gray-900",children:"Welcome Back"}),(0,t.jsx)("p",{className:"text-gray-600 mt-2",children:"Sign in to your Digital Freight account"})]}),(0,t.jsxs)("form",{onSubmit:f,className:"space-y-6",children:[p&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md",children:p}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address"}),(0,t.jsx)("input",{type:"email",id:"email",name:"email",value:d.email,onChange:h,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter your email",disabled:o,required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-2",children:"Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{type:u?"text":"password",id:"password",name:"password",value:d.password,onChange:h,className:"w-full px-3 py-2 pr-10 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter your password",disabled:o,required:!0}),(0,t.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>m(!u),disabled:o,children:u?(0,t.jsx)(l,{className:"h-5 w-5 text-gray-400"}):(0,t.jsx)(n.A,{className:"h-5 w-5 text-gray-400"})})]})]}),(0,t.jsx)("button",{type:"submit",disabled:o,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:o?(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Signing in..."]}):"Sign In"})]}),(0,t.jsx)("div",{className:"mt-6 text-center",children:(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["Don't have an account?"," ",(0,t.jsx)("button",{type:"button",onClick:s,className:"font-medium text-blue-600 hover:text-blue-500 focus:outline-none focus:underline",disabled:o,children:"Sign up here"})]})})]})})},c=({onSuccess:e,onSwitchToLogin:s})=>{let{register:r,isLoading:o}=(0,i.h)(),[d,c]=(0,a.useState)({firstName:"",lastName:"",email:"",companyName:"",password:"",confirmPassword:""}),[u,m]=(0,a.useState)(!1),[p,x]=(0,a.useState)(!1),[h,f]=(0,a.useState)(""),b=e=>{let{name:s,value:r}=e.target;c(e=>({...e,[s]:r})),h&&f("")},g=()=>d.firstName&&d.lastName&&d.email&&d.password?d.email.includes("@")?d.password.length<8?"Password must be at least 8 characters long":d.password!==d.confirmPassword?"Passwords do not match":null:"Please enter a valid email address":"Please fill in all required fields",w=async s=>{s.preventDefault(),f("");let t=g();if(t)return void f(t);try{await r({firstName:d.firstName,lastName:d.lastName,email:d.email,password:d.password,companyName:d.companyName||void 0}),e?.()}catch(e){f(e.message||"Registration failed")}};return(0,t.jsx)("div",{className:"w-full max-w-md mx-auto",children:(0,t.jsxs)("div",{className:"bg-white shadow-lg rounded-lg p-8",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("h2",{className:"text-3xl font-bold text-gray-900",children:"Create Account"}),(0,t.jsx)("p",{className:"text-gray-600 mt-2",children:"Join Digital Freight Platform"})]}),(0,t.jsxs)("form",{onSubmit:w,className:"space-y-6",children:[h&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md",children:h}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"firstName",className:"block text-sm font-medium text-gray-700 mb-2",children:"First Name *"}),(0,t.jsx)("input",{type:"text",id:"firstName",name:"firstName",value:d.firstName,onChange:b,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"John",disabled:o,required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"lastName",className:"block text-sm font-medium text-gray-700 mb-2",children:"Last Name *"}),(0,t.jsx)("input",{type:"text",id:"lastName",name:"lastName",value:d.lastName,onChange:b,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Doe",disabled:o,required:!0})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address *"}),(0,t.jsx)("input",{type:"email",id:"email",name:"email",value:d.email,onChange:b,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"<EMAIL>",disabled:o,required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"companyName",className:"block text-sm font-medium text-gray-700 mb-2",children:"Company Name"}),(0,t.jsx)("input",{type:"text",id:"companyName",name:"companyName",value:d.companyName,onChange:b,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Your freight company",disabled:o})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-2",children:"Password *"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{type:u?"text":"password",id:"password",name:"password",value:d.password,onChange:b,className:"w-full px-3 py-2 pr-10 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"At least 8 characters",disabled:o,required:!0}),(0,t.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>m(!u),disabled:o,children:u?(0,t.jsx)(l,{className:"h-5 w-5 text-gray-400"}):(0,t.jsx)(n.A,{className:"h-5 w-5 text-gray-400"})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 mb-2",children:"Confirm Password *"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{type:p?"text":"password",id:"confirmPassword",name:"confirmPassword",value:d.confirmPassword,onChange:b,className:"w-full px-3 py-2 pr-10 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Confirm your password",disabled:o,required:!0}),(0,t.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>x(!p),disabled:o,children:p?(0,t.jsx)(l,{className:"h-5 w-5 text-gray-400"}):(0,t.jsx)(n.A,{className:"h-5 w-5 text-gray-400"})})]})]}),(0,t.jsx)("button",{type:"submit",disabled:o,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:o?(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Creating account..."]}):"Create Account"})]}),(0,t.jsx)("div",{className:"mt-6 text-center",children:(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["Already have an account?"," ",(0,t.jsx)("button",{type:"button",onClick:s,className:"font-medium text-blue-600 hover:text-blue-500 focus:outline-none focus:underline",disabled:o,children:"Sign in here"})]})})]})})},u=()=>{let e=(0,o.useRouter)(),s=(0,o.useSearchParams)(),{isAuthenticated:r}=(0,i.h)(),[l,n]=(0,a.useState)("login");(0,a.useEffect)(()=>{"register"===s.get("mode")&&n("register")},[s]),(0,a.useEffect)(()=>{r&&e.push("/dashboard")},[r,e]);let u=()=>{e.push("/dashboard")};return r?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,t.jsx)("div",{className:"max-w-md w-full space-y-8",children:"login"===l?(0,t.jsx)(d,{onSuccess:u,onSwitchToRegister:()=>{n("register"),e.replace("/auth?mode=register")}}):(0,t.jsx)(c,{onSuccess:u,onSwitchToLogin:()=>{n("login"),e.replace("/auth")}})})})}},2412:e=>{"use strict";e.exports=require("assert")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},3997:e=>{"use strict";e.exports=require("tty")},4075:e=>{"use strict";e.exports=require("zlib")},4735:e=>{"use strict";e.exports=require("events")},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},6524:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});var t=r(3210);let a=t.forwardRef(function({title:e,titleId:s,...r},a){return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":s},r),e?t.createElement("title",{id:s},e):null,t.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),t.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))})},7910:e=>{"use strict";e.exports=require("stream")},8297:(e,s,r)=>{Promise.resolve().then(r.bind(r,8305))},8305:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/vo/digital-freight/frontend/src/app/auth/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/vo/digital-freight/frontend/src/app/auth/page.tsx","default")},8354:e=>{"use strict";e.exports=require("util")},9021:e=>{"use strict";e.exports=require("fs")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[447,521,658,819],()=>r(1697));module.exports=t})();