"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[20],{9003:(e,t,n)=>{n.d(t,{ie:()=>C,we:()=>B});var l,r=n(2115),o=n(5710),u=n(5155),i=n(6301);n(7650);var a=n(4945);let s="active",c="selected",f="ArrowLeft",d="ArrowRight",h="ArrowUp",g="ArrowDown",m={...l||(l=n.t(r,2))},p=!1,v=0,w=()=>"floating-ui-"+Math.random().toString(36).slice(2,6)+v++,x=m.useId||function(){let[e,t]=r.useState(()=>p?w():void 0);return(0,o.OS)(()=>{null==e&&t(w())},[]),r.useEffect(()=>{p=!0},[]),e},C=r.forwardRef(function(e,t){let{context:{placement:n,elements:{floating:l},middlewareData:{arrow:a,shift:s}},width:c=14,height:f=7,tipRadius:d=0,strokeWidth:h=0,staticOffset:g,stroke:m,d:p,style:{transform:v,...w}={},...C}=e,R=x(),[M,S]=r.useState(!1);if((0,o.OS)(()=>{l&&"rtl"===(0,i.L9)(l).direction&&S(!0)},[l]),!l)return null;let[k,b]=n.split("-"),y="top"===k||"bottom"===k,E=g;(y&&null!=s&&s.x||!y&&null!=s&&s.y)&&(E=null);let j=2*h,O=j/2,W=c/2*(-(d/8)+1),q=f/2*d/4,A=!!p,I=E&&"end"===b?"bottom":"top",_=E&&"end"===b?"right":"left";E&&M&&(_="end"===b?"left":"right");let B=(null==a?void 0:a.x)!=null?E||a.x:"",D=(null==a?void 0:a.y)!=null?E||a.y:"",L=p||"M0,0 H"+c+(" L"+(c-W))+","+(f-q)+(" Q"+c/2+","+f+" "+W)+","+(f-q)+" Z",N={top:A?"rotate(180deg)":"",left:A?"rotate(90deg)":"rotate(-90deg)",bottom:A?"":"rotate(180deg)",right:A?"rotate(-90deg)":"rotate(90deg)"}[k];return(0,u.jsxs)("svg",{...C,"aria-hidden":!0,ref:t,width:A?c:c+j,height:c,viewBox:"0 0 "+c+" "+(f>c?f:c),style:{position:"absolute",pointerEvents:"none",[_]:B,[I]:D,[k]:y||A?"100%":"calc(100% - "+j/2+"px)",transform:[N,v].filter(e=>!!e).join(" "),...w},children:[j>0&&(0,u.jsx)("path",{clipPath:"url(#"+R+")",fill:"none",stroke:m,strokeWidth:j+ +!p,d:L}),(0,u.jsx)("path",{stroke:j&&!p?C.fill:"none",d:L}),(0,u.jsx)("clipPath",{id:R,children:(0,u.jsx)("rect",{x:-O,y:O*(A?-1:1),width:c+j,height:c})})]})}),R=r.createContext(null),M=r.createContext(null),S=()=>{var e;return(null==(e=r.useContext(R))?void 0:e.id)||null},k=()=>r.useContext(M),b=()=>{},y=r.createContext({delay:0,initialDelay:0,timeoutMs:0,currentId:null,setCurrentId:b,setState:b,isInstantPhase:!1});let E=0,j={inert:new WeakMap,"aria-hidden":new WeakMap,none:new WeakMap};function O(e){return"inert"===e?j.inert:"aria-hidden"===e?j["aria-hidden"]:j.none}let W=new WeakSet,q={},A=0,I=e=>e&&(e.host||I(e.parentNode)),_=(e,t)=>t.map(t=>{if(e.contains(t))return t;let n=I(t);return e.contains(n)?n:null}).filter(e=>null!=e);function B(e){void 0===e&&(e={});let{nodeId:t}=e,n=function(e){let{open:t=!1,onOpenChange:n,elements:l}=e,u=x(),i=r.useRef({}),[a]=r.useState(()=>(function(){let e=new Map;return{emit(t,n){var l;null==(l=e.get(t))||l.forEach(e=>e(n))},on(t,n){e.has(t)||e.set(t,new Set),e.get(t).add(n)},off(t,n){var l;null==(l=e.get(t))||l.delete(n)}}})()),s=null!=S(),[c,f]=r.useState(l.reference),d=(0,o.Jt)((e,t,l)=>{i.current.openEvent=e?t:void 0,a.emit("openchange",{open:e,event:t,reason:l,nested:s}),null==n||n(e,t,l)}),h=r.useMemo(()=>({setPositionReference:f}),[]),g=r.useMemo(()=>({reference:c||l.reference||null,floating:l.floating||null,domReference:l.reference}),[c,l.reference,l.floating]);return r.useMemo(()=>({dataRef:i,open:t,onOpenChange:d,elements:g,events:a,floatingId:u,refs:h}),[t,d,g,a,u,h])}({...e,elements:{reference:null,floating:null,...e.elements}}),l=e.rootContext||n,u=l.elements,[s,c]=r.useState(null),[f,d]=r.useState(null),h=(null==u?void 0:u.domReference)||s,g=r.useRef(null),m=k();(0,o.OS)(()=>{h&&(g.current=h)},[h]);let p=(0,a.we)({...e,elements:{...u,...f&&{reference:f}}}),v=r.useCallback(e=>{let t=(0,i.vq)(e)?{getBoundingClientRect:()=>e.getBoundingClientRect(),getClientRects:()=>e.getClientRects(),contextElement:e}:e;d(t),p.refs.setReference(t)},[p.refs]),w=r.useCallback(e=>{((0,i.vq)(e)||null===e)&&(g.current=e,c(e)),((0,i.vq)(p.refs.reference.current)||null===p.refs.reference.current||null!==e&&!(0,i.vq)(e))&&p.refs.setReference(e)},[p.refs]),C=r.useMemo(()=>({...p.refs,setReference:w,setPositionReference:v,domReference:g}),[p.refs,w,v]),R=r.useMemo(()=>({...p.elements,domReference:h}),[p.elements,h]),M=r.useMemo(()=>({...p,...l,refs:C,elements:R,nodeId:t}),[p,C,R,t,l]);return(0,o.OS)(()=>{l.dataRef.current.floatingContext=M;let e=null==m?void 0:m.nodesRef.current.find(e=>e.id===t);e&&(e.context=M)}),r.useMemo(()=>({...p,context:M,refs:C,elements:R}),[p,C,R,M])}function D(e,t,n){switch(e){case"vertical":return t;case"horizontal":return n;default:return t||n}}}}]);