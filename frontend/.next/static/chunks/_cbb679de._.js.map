{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/vo/digital-freight/frontend/src/components/auth/ProtectedRoute.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useRouter } from 'next/navigation';\nimport { useEffect } from 'react';\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode;\n  fallback?: React.ReactNode;\n  redirectTo?: string;\n}\n\nconst ProtectedRoute: React.FC<ProtectedRouteProps> = ({ \n  children, \n  fallback,\n  redirectTo = '/auth'\n}) => {\n  const { isAuthenticated, isLoading } = useAuth();\n  const router = useRouter();\n\n  useEffect(() => {\n    if (!isLoading && !isAuthenticated) {\n      router.push(redirectTo);\n    }\n  }, [isAuthenticated, isLoading, router, redirectTo]);\n\n  // Show loading spinner while checking authentication\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  // Show fallback or redirect if not authenticated\n  if (!isAuthenticated) {\n    return fallback ? <>{fallback}</> : null;\n  }\n\n  // Render children if authenticated\n  return <>{children}</>;\n};\n\nexport default ProtectedRoute;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;;;AALA;;;;AAaA,MAAM,iBAAgD,CAAC,EACrD,QAAQ,EACR,QAAQ,EACR,aAAa,OAAO,EACrB;;IACC,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC7C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,CAAC,aAAa,CAAC,iBAAiB;gBAClC,OAAO,IAAI,CAAC;YACd;QACF;mCAAG;QAAC;QAAiB;QAAW;QAAQ;KAAW;IAEnD,qDAAqD;IACrD,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,iDAAiD;IACjD,IAAI,CAAC,iBAAiB;QACpB,OAAO,yBAAW;sBAAG;4BAAe;IACtC;IAEA,mCAAmC;IACnC,qBAAO;kBAAG;;AACZ;GA9BM;;QAKmC,kIAAA,CAAA,UAAO;QAC/B,qIAAA,CAAA,YAAS;;;KANpB;uCAgCS", "debugId": null}}, {"offset": {"line": 83, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/vo/digital-freight/frontend/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport ProtectedRoute from '@/components/auth/ProtectedRoute';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { \n  MagnifyingGlassIcon, \n  ClockIcon, \n  TruckIcon, \n  ChartBarIcon,\n  PlusIcon,\n  ArrowRightIcon\n} from '@heroicons/react/24/outline';\n\nconst DashboardPage: React.FC = () => {\n  const { user } = useAuth();\n\n  // Mock data for demonstration\n  const recentSearches = [\n    {\n      id: '1',\n      origin: 'Shanghai (SHG)',\n      destination: 'Rotterdam (RTM)',\n      date: '2024-01-15',\n      results: 3,\n    },\n    {\n      id: '2',\n      origin: 'Los Angeles (LAX)',\n      destination: 'Hamburg (HAM)',\n      date: '2024-01-14',\n      results: 2,\n    },\n    {\n      id: '3',\n      origin: 'Singapore (SIN)',\n      destination: 'New York (NYC)',\n      date: '2024-01-13',\n      results: 4,\n    },\n  ];\n\n  const stats = [\n    {\n      name: 'Total Searches',\n      value: '24',\n      icon: MagnifyingGlassIcon,\n      change: '+12%',\n      changeType: 'increase',\n    },\n    {\n      name: 'Routes Found',\n      value: '89',\n      icon: TruckIcon,\n      change: '+8%',\n      changeType: 'increase',\n    },\n    {\n      name: 'Avg Transit Time',\n      value: '28 days',\n      icon: ClockIcon,\n      change: '-2 days',\n      changeType: 'decrease',\n    },\n    {\n      name: 'Carriers Compared',\n      value: '4',\n      icon: ChartBarIcon,\n      change: 'All active',\n      changeType: 'neutral',\n    },\n  ];\n\n  return (\n    <ProtectedRoute>\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Welcome Header */}\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900\">\n            Welcome back, {user?.firstName}!\n          </h1>\n          <p className=\"text-gray-600 mt-2\">\n            Here's your freight forwarding dashboard overview.\n          </p>\n        </div>\n\n        {/* Quick Actions */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8\">\n          <Link\n            href=\"/search\"\n            className=\"bg-blue-600 hover:bg-blue-700 text-white rounded-lg p-6 transition-colors group\"\n          >\n            <div className=\"flex items-center\">\n              <MagnifyingGlassIcon className=\"h-8 w-8 mr-3\" />\n              <div>\n                <h3 className=\"text-lg font-semibold\">New Search</h3>\n                <p className=\"text-blue-100\">Find shipping routes</p>\n              </div>\n              <ArrowRightIcon className=\"h-5 w-5 ml-auto group-hover:translate-x-1 transition-transform\" />\n            </div>\n          </Link>\n\n          <Link\n            href=\"/history\"\n            className=\"bg-white hover:bg-gray-50 border border-gray-200 rounded-lg p-6 transition-colors group\"\n          >\n            <div className=\"flex items-center\">\n              <ClockIcon className=\"h-8 w-8 mr-3 text-gray-600\" />\n              <div>\n                <h3 className=\"text-lg font-semibold text-gray-900\">Search History</h3>\n                <p className=\"text-gray-600\">View past searches</p>\n              </div>\n              <ArrowRightIcon className=\"h-5 w-5 ml-auto text-gray-400 group-hover:translate-x-1 transition-transform\" />\n            </div>\n          </Link>\n\n          <Link\n            href=\"/profile\"\n            className=\"bg-white hover:bg-gray-50 border border-gray-200 rounded-lg p-6 transition-colors group\"\n          >\n            <div className=\"flex items-center\">\n              <ChartBarIcon className=\"h-8 w-8 mr-3 text-gray-600\" />\n              <div>\n                <h3 className=\"text-lg font-semibold text-gray-900\">Profile Settings</h3>\n                <p className=\"text-gray-600\">Manage your account</p>\n              </div>\n              <ArrowRightIcon className=\"h-5 w-5 ml-auto text-gray-400 group-hover:translate-x-1 transition-transform\" />\n            </div>\n          </Link>\n        </div>\n\n        {/* Stats Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n          {stats.map((stat) => (\n            <div key={stat.name} className=\"bg-white rounded-lg shadow p-6\">\n              <div className=\"flex items-center\">\n                <div className=\"flex-shrink-0\">\n                  <stat.icon className=\"h-8 w-8 text-gray-600\" />\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-600\">{stat.name}</p>\n                  <p className=\"text-2xl font-semibold text-gray-900\">{stat.value}</p>\n                </div>\n              </div>\n              <div className=\"mt-4\">\n                <span\n                  className={`text-sm font-medium ${\n                    stat.changeType === 'increase'\n                      ? 'text-green-600'\n                      : stat.changeType === 'decrease'\n                      ? 'text-red-600'\n                      : 'text-gray-600'\n                  }`}\n                >\n                  {stat.change}\n                </span>\n                <span className=\"text-sm text-gray-500 ml-1\">from last month</span>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Recent Searches */}\n        <div className=\"bg-white rounded-lg shadow\">\n          <div className=\"px-6 py-4 border-b border-gray-200\">\n            <div className=\"flex items-center justify-between\">\n              <h2 className=\"text-lg font-semibold text-gray-900\">Recent Searches</h2>\n              <Link\n                href=\"/history\"\n                className=\"text-blue-600 hover:text-blue-700 text-sm font-medium\"\n              >\n                View all\n              </Link>\n            </div>\n          </div>\n          <div className=\"divide-y divide-gray-200\">\n            {recentSearches.map((search) => (\n              <div key={search.id} className=\"px-6 py-4 hover:bg-gray-50\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <p className=\"text-sm font-medium text-gray-900\">\n                      {search.origin} → {search.destination}\n                    </p>\n                    <p className=\"text-sm text-gray-500\">\n                      {new Date(search.date).toLocaleDateString('en-US', {\n                        month: 'short',\n                        day: 'numeric',\n                        year: 'numeric',\n                      })}\n                    </p>\n                  </div>\n                  <div className=\"flex items-center space-x-4\">\n                    <span className=\"text-sm text-gray-600\">\n                      {search.results} routes found\n                    </span>\n                    <button className=\"text-blue-600 hover:text-blue-700 text-sm font-medium\">\n                      View Results\n                    </button>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n          {recentSearches.length === 0 && (\n            <div className=\"px-6 py-12 text-center\">\n              <MagnifyingGlassIcon className=\"h-12 w-12 text-gray-300 mx-auto mb-4\" />\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No searches yet</h3>\n              <p className=\"text-gray-600 mb-4\">\n                Start by searching for shipping routes to see your history here.\n              </p>\n              <Link\n                href=\"/search\"\n                className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700\"\n              >\n                <PlusIcon className=\"h-4 w-4 mr-2\" />\n                Start Searching\n              </Link>\n            </div>\n          )}\n        </div>\n      </div>\n    </ProtectedRoute>\n  );\n};\n\nexport default DashboardPage;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AANA;;;;;AAeA,MAAM,gBAA0B;;IAC9B,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEvB,8BAA8B;IAC9B,MAAM,iBAAiB;QACrB;YACE,IAAI;YACJ,QAAQ;YACR,aAAa;YACb,MAAM;YACN,SAAS;QACX;QACA;YACE,IAAI;YACJ,QAAQ;YACR,aAAa;YACb,MAAM;YACN,SAAS;QACX;QACA;YACE,IAAI;YACJ,QAAQ;YACR,aAAa;YACb,MAAM;YACN,SAAS;QACX;KACD;IAED,MAAM,QAAQ;QACZ;YACE,MAAM;YACN,OAAO;YACP,MAAM,wOAAA,CAAA,sBAAmB;YACzB,QAAQ;YACR,YAAY;QACd;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM,oNAAA,CAAA,YAAS;YACf,QAAQ;YACR,YAAY;QACd;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM,oNAAA,CAAA,YAAS;YACf,QAAQ;YACR,YAAY;QACd;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM,0NAAA,CAAA,eAAY;YAClB,QAAQ;YACR,YAAY;QACd;KACD;IAED,qBACE,6LAAC,+IAAA,CAAA,UAAc;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;;gCAAmC;gCAChC,MAAM;gCAAU;;;;;;;sCAEjC,6LAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;;8BAMpC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,wOAAA,CAAA,sBAAmB;wCAAC,WAAU;;;;;;kDAC/B,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAwB;;;;;;0DACtC,6LAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAE/B,6LAAC,8NAAA,CAAA,iBAAc;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAI9B,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAsC;;;;;;0DACpD,6LAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAE/B,6LAAC,8NAAA,CAAA,iBAAc;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAI9B,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,0NAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;kDACxB,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAsC;;;;;;0DACpD,6LAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAE/B,6LAAC,8NAAA,CAAA,iBAAc;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8BAMhC,6LAAC;oBAAI,WAAU;8BACZ,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC;4BAAoB,WAAU;;8CAC7B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;;;;;;sDAEvB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAqC,KAAK,IAAI;;;;;;8DAC3D,6LAAC;oDAAE,WAAU;8DAAwC,KAAK,KAAK;;;;;;;;;;;;;;;;;;8CAGnE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,WAAW,CAAC,oBAAoB,EAC9B,KAAK,UAAU,KAAK,aAChB,mBACA,KAAK,UAAU,KAAK,aACpB,iBACA,iBACJ;sDAED,KAAK,MAAM;;;;;;sDAEd,6LAAC;4CAAK,WAAU;sDAA6B;;;;;;;;;;;;;2BAtBvC,KAAK,IAAI;;;;;;;;;;8BA6BvB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAsC;;;;;;kDACpD,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;sCAKL,6LAAC;4BAAI,WAAU;sCACZ,eAAe,GAAG,CAAC,CAAC,uBACnB,6LAAC;oCAAoB,WAAU;8CAC7B,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;;4DACV,OAAO,MAAM;4DAAC;4DAAI,OAAO,WAAW;;;;;;;kEAEvC,6LAAC;wDAAE,WAAU;kEACV,IAAI,KAAK,OAAO,IAAI,EAAE,kBAAkB,CAAC,SAAS;4DACjD,OAAO;4DACP,KAAK;4DACL,MAAM;wDACR;;;;;;;;;;;;0DAGJ,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;;4DACb,OAAO,OAAO;4DAAC;;;;;;;kEAElB,6LAAC;wDAAO,WAAU;kEAAwD;;;;;;;;;;;;;;;;;;mCAlBtE,OAAO,EAAE;;;;;;;;;;wBA0BtB,eAAe,MAAM,KAAK,mBACzB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,wOAAA,CAAA,sBAAmB;oCAAC,WAAU;;;;;;8CAC/B,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,6LAAC,kNAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrD;GAjNM;;QACa,kIAAA,CAAA,UAAO;;;KADpB;uCAmNS", "debugId": null}}, {"offset": {"line": 660, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/vo/digital-freight/frontend/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 667, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/vo/digital-freight/frontend/node_modules/%40heroicons/react/24/outline/esm/MagnifyingGlassIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction MagnifyingGlassIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(MagnifyingGlassIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,oBAAoB,EAC3B,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 709, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/vo/digital-freight/frontend/node_modules/%40heroicons/react/24/outline/esm/ClockIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction ClockIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ClockIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,UAAU,EACjB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 751, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/vo/digital-freight/frontend/node_modules/%40heroicons/react/24/outline/esm/ChartBarIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction ChartBarIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ChartBarIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,aAAa,EACpB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 793, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/vo/digital-freight/frontend/node_modules/%40heroicons/react/24/outline/esm/PlusIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction PlusIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 4.5v15m7.5-7.5h-15\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(PlusIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,SAAS,EAChB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 835, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/vo/digital-freight/frontend/node_modules/%40heroicons/react/24/outline/esm/ArrowRightIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction ArrowRightIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ArrowRightIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,eAAe,EACtB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}]}