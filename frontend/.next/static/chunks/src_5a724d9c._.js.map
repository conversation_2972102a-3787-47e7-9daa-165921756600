{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/vo/digital-freight/frontend/src/components/search/PortAutocomplete.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useRef } from 'react';\nimport { MagnifyingGlassIcon, MapPinIcon } from '@heroicons/react/24/outline';\nimport axios from 'axios';\n\ninterface Port {\n  id: string;\n  code: string;\n  name: string;\n  countryName: string;\n  displayName: string;\n}\n\ninterface PortAutocompleteProps {\n  label: string;\n  placeholder?: string;\n  value?: Port | null;\n  onChange: (port: Port | null) => void;\n  error?: string;\n  disabled?: boolean;\n  required?: boolean;\n}\n\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001';\n\nconst PortAutocomplete: React.FC<PortAutocompleteProps> = ({\n  label,\n  placeholder = 'Search for a port...',\n  value,\n  onChange,\n  error,\n  disabled = false,\n  required = false,\n}) => {\n  const [query, setQuery] = useState('');\n  const [ports, setPorts] = useState<Port[]>([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [isOpen, setIsOpen] = useState(false);\n  const [popularPorts, setPopularPorts] = useState<Port[]>([]);\n  \n  const inputRef = useRef<HTMLInputElement>(null);\n  const dropdownRef = useRef<HTMLDivElement>(null);\n\n  // Load popular ports on mount\n  useEffect(() => {\n    const loadPopularPorts = async () => {\n      try {\n        const response = await axios.get(`${API_BASE_URL}/api/ports/popular?limit=8`);\n        setPopularPorts(response.data.data.ports);\n      } catch (error) {\n        console.error('Error loading popular ports:', error);\n      }\n    };\n\n    loadPopularPorts();\n  }, []);\n\n  // Search ports when query changes\n  useEffect(() => {\n    const searchPorts = async () => {\n      if (query.length < 2) {\n        setPorts([]);\n        return;\n      }\n\n      setIsLoading(true);\n      try {\n        const response = await axios.get(\n          `${API_BASE_URL}/api/ports/search?q=${encodeURIComponent(query)}&limit=10`\n        );\n        setPorts(response.data.data.ports);\n      } catch (error) {\n        console.error('Error searching ports:', error);\n        setPorts([]);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    const debounceTimer = setTimeout(searchPorts, 300);\n    return () => clearTimeout(debounceTimer);\n  }, [query]);\n\n  // Handle input change\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const newQuery = e.target.value;\n    setQuery(newQuery);\n    setIsOpen(true);\n\n    // Clear selection if input doesn't match selected port\n    if (value && !newQuery.toLowerCase().includes(value.code.toLowerCase()) && \n        !newQuery.toLowerCase().includes(value.name.toLowerCase())) {\n      onChange(null);\n    }\n  };\n\n  // Handle port selection\n  const handlePortSelect = (port: Port) => {\n    setQuery(port.displayName);\n    onChange(port);\n    setIsOpen(false);\n    inputRef.current?.blur();\n  };\n\n  // Handle input focus\n  const handleFocus = () => {\n    setIsOpen(true);\n    if (!query && popularPorts.length > 0) {\n      setPorts(popularPorts);\n    }\n  };\n\n  // Handle click outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setIsOpen(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n\n  // Update input when value changes externally\n  useEffect(() => {\n    if (value) {\n      setQuery(value.displayName);\n    } else if (!isOpen) {\n      setQuery('');\n    }\n  }, [value, isOpen]);\n\n  const displayPorts = query.length < 2 ? popularPorts : ports;\n\n  return (\n    <div className=\"relative\" ref={dropdownRef}>\n      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n        {label}\n        {required && <span className=\"text-red-500 ml-1\">*</span>}\n      </label>\n      \n      <div className=\"relative\">\n        <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n          <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400\" />\n        </div>\n        \n        <input\n          ref={inputRef}\n          type=\"text\"\n          value={query}\n          onChange={handleInputChange}\n          onFocus={handleFocus}\n          placeholder={placeholder}\n          disabled={disabled}\n          className={`w-full pl-10 pr-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${\n            error ? 'border-red-300' : 'border-gray-300'\n          } ${disabled ? 'bg-gray-50 cursor-not-allowed' : ''}`}\n        />\n        \n        {isLoading && (\n          <div className=\"absolute inset-y-0 right-0 pr-3 flex items-center\">\n            <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500\"></div>\n          </div>\n        )}\n      </div>\n\n      {error && (\n        <p className=\"mt-1 text-sm text-red-600\">{error}</p>\n      )}\n\n      {isOpen && (\n        <div className=\"absolute z-10 mt-1 w-full bg-white shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none\">\n          {displayPorts.length === 0 && !isLoading && query.length >= 2 && (\n            <div className=\"px-4 py-2 text-sm text-gray-500\">\n              No ports found for \"{query}\"\n            </div>\n          )}\n          \n          {displayPorts.length === 0 && !isLoading && query.length < 2 && (\n            <div className=\"px-4 py-2 text-sm text-gray-500\">\n              Type at least 2 characters to search\n            </div>\n          )}\n\n          {query.length < 2 && popularPorts.length > 0 && (\n            <div className=\"px-4 py-2 text-xs font-medium text-gray-400 uppercase tracking-wide\">\n              Popular Ports\n            </div>\n          )}\n\n          {displayPorts.map((port) => (\n            <button\n              key={port.id}\n              onClick={() => handlePortSelect(port)}\n              className=\"w-full text-left px-4 py-2 hover:bg-blue-50 focus:bg-blue-50 focus:outline-none\"\n            >\n              <div className=\"flex items-center\">\n                <MapPinIcon className=\"h-4 w-4 text-gray-400 mr-2 flex-shrink-0\" />\n                <div>\n                  <div className=\"text-sm font-medium text-gray-900\">\n                    {port.code} - {port.name}\n                  </div>\n                  <div className=\"text-xs text-gray-500\">{port.countryName}</div>\n                </div>\n              </div>\n            </button>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default PortAutocomplete;\n"], "names": [], "mappings": ";;;AAwBqB;;AAtBrB;AACA;AAAA;AACA;;;AAJA;;;;AAwBA,MAAM,eAAe,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI;AAExD,MAAM,mBAAoD,CAAC,EACzD,KAAK,EACL,cAAc,sBAAsB,EACpC,KAAK,EACL,QAAQ,EACR,KAAK,EACL,WAAW,KAAK,EAChB,WAAW,KAAK,EACjB;;IACC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAE3D,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,8BAA8B;IAC9B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM;+DAAmB;oBACvB,IAAI;wBACF,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,GAAG,aAAa,0BAA0B,CAAC;wBAC5E,gBAAgB,SAAS,IAAI,CAAC,IAAI,CAAC,KAAK;oBAC1C,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,gCAAgC;oBAChD;gBACF;;YAEA;QACF;qCAAG,EAAE;IAEL,kCAAkC;IAClC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM;0DAAc;oBAClB,IAAI,MAAM,MAAM,GAAG,GAAG;wBACpB,SAAS,EAAE;wBACX;oBACF;oBAEA,aAAa;oBACb,IAAI;wBACF,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CAC9B,GAAG,aAAa,oBAAoB,EAAE,mBAAmB,OAAO,SAAS,CAAC;wBAE5E,SAAS,SAAS,IAAI,CAAC,IAAI,CAAC,KAAK;oBACnC,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,0BAA0B;wBACxC,SAAS,EAAE;oBACb,SAAU;wBACR,aAAa;oBACf;gBACF;;YAEA,MAAM,gBAAgB,WAAW,aAAa;YAC9C;8CAAO,IAAM,aAAa;;QAC5B;qCAAG;QAAC;KAAM;IAEV,sBAAsB;IACtB,MAAM,oBAAoB,CAAC;QACzB,MAAM,WAAW,EAAE,MAAM,CAAC,KAAK;QAC/B,SAAS;QACT,UAAU;QAEV,uDAAuD;QACvD,IAAI,SAAS,CAAC,SAAS,WAAW,GAAG,QAAQ,CAAC,MAAM,IAAI,CAAC,WAAW,OAChE,CAAC,SAAS,WAAW,GAAG,QAAQ,CAAC,MAAM,IAAI,CAAC,WAAW,KAAK;YAC9D,SAAS;QACX;IACF;IAEA,wBAAwB;IACxB,MAAM,mBAAmB,CAAC;QACxB,SAAS,KAAK,WAAW;QACzB,SAAS;QACT,UAAU;QACV,SAAS,OAAO,EAAE;IACpB;IAEA,qBAAqB;IACrB,MAAM,cAAc;QAClB,UAAU;QACV,IAAI,CAAC,SAAS,aAAa,MAAM,GAAG,GAAG;YACrC,SAAS;QACX;IACF;IAEA,uBAAuB;IACvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM;iEAAqB,CAAC;oBAC1B,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;wBAC9E,UAAU;oBACZ;gBACF;;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;8CAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;;QACzD;qCAAG,EAAE;IAEL,6CAA6C;IAC7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,OAAO;gBACT,SAAS,MAAM,WAAW;YAC5B,OAAO,IAAI,CAAC,QAAQ;gBAClB,SAAS;YACX;QACF;qCAAG;QAAC;QAAO;KAAO;IAElB,MAAM,eAAe,MAAM,MAAM,GAAG,IAAI,eAAe;IAEvD,qBACE,6LAAC;QAAI,WAAU;QAAW,KAAK;;0BAC7B,6LAAC;gBAAM,WAAU;;oBACd;oBACA,0BAAY,6LAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;0BAGnD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,wOAAA,CAAA,sBAAmB;4BAAC,WAAU;;;;;;;;;;;kCAGjC,6LAAC;wBACC,KAAK;wBACL,MAAK;wBACL,OAAO;wBACP,UAAU;wBACV,SAAS;wBACT,aAAa;wBACb,UAAU;wBACV,WAAW,CAAC,6HAA6H,EACvI,QAAQ,mBAAmB,kBAC5B,CAAC,EAAE,WAAW,kCAAkC,IAAI;;;;;;oBAGtD,2BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;YAKpB,uBACC,6LAAC;gBAAE,WAAU;0BAA6B;;;;;;YAG3C,wBACC,6LAAC;gBAAI,WAAU;;oBACZ,aAAa,MAAM,KAAK,KAAK,CAAC,aAAa,MAAM,MAAM,IAAI,mBAC1D,6LAAC;wBAAI,WAAU;;4BAAkC;4BAC1B;4BAAM;;;;;;;oBAI9B,aAAa,MAAM,KAAK,KAAK,CAAC,aAAa,MAAM,MAAM,GAAG,mBACzD,6LAAC;wBAAI,WAAU;kCAAkC;;;;;;oBAKlD,MAAM,MAAM,GAAG,KAAK,aAAa,MAAM,GAAG,mBACzC,6LAAC;wBAAI,WAAU;kCAAsE;;;;;;oBAKtF,aAAa,GAAG,CAAC,CAAC,qBACjB,6LAAC;4BAEC,SAAS,IAAM,iBAAiB;4BAChC,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,sNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;;oDACZ,KAAK,IAAI;oDAAC;oDAAI,KAAK,IAAI;;;;;;;0DAE1B,6LAAC;gDAAI,WAAU;0DAAyB,KAAK,WAAW;;;;;;;;;;;;;;;;;;2BAVvD,KAAK,EAAE;;;;;;;;;;;;;;;;;AAmB1B;GA3LM;KAAA;uCA6LS", "debugId": null}}, {"offset": {"line": 322, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/vo/digital-freight/frontend/src/components/search/SearchForm.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { CalendarIcon, MagnifyingGlassIcon, ArrowsRightLeftIcon } from '@heroicons/react/24/outline';\nimport PortAutocomplete from './PortAutocomplete';\nimport DatePicker from 'react-datepicker';\nimport 'react-datepicker/dist/react-datepicker.css';\n\ninterface Port {\n  id: string;\n  code: string;\n  name: string;\n  countryName: string;\n  displayName: string;\n}\n\ninterface SearchFormData {\n  originPort: Port | null;\n  destinationPort: Port | null;\n  departureDate: Date | null;\n}\n\ninterface SearchFormProps {\n  onSearch: (searchData: SearchFormData) => void;\n  isLoading?: boolean;\n}\n\nconst SearchForm: React.FC<SearchFormProps> = ({ onSearch, isLoading = false }) => {\n  const [formData, setFormData] = useState<SearchFormData>({\n    originPort: null,\n    destinationPort: null,\n    departureDate: null,\n  });\n  const [errors, setErrors] = useState<Record<string, string>>({});\n\n  const handleOriginChange = (port: Port | null) => {\n    setFormData(prev => ({ ...prev, originPort: port }));\n    if (errors.originPort) {\n      setErrors(prev => ({ ...prev, originPort: '' }));\n    }\n  };\n\n  const handleDestinationChange = (port: Port | null) => {\n    setFormData(prev => ({ ...prev, destinationPort: port }));\n    if (errors.destinationPort) {\n      setErrors(prev => ({ ...prev, destinationPort: '' }));\n    }\n  };\n\n  const handleDateChange = (date: Date | null) => {\n    setFormData(prev => ({ ...prev, departureDate: date }));\n    if (errors.departureDate) {\n      setErrors(prev => ({ ...prev, departureDate: '' }));\n    }\n  };\n\n  const handleSwapPorts = () => {\n    setFormData(prev => ({\n      ...prev,\n      originPort: prev.destinationPort,\n      destinationPort: prev.originPort,\n    }));\n  };\n\n  const validateForm = (): boolean => {\n    const newErrors: Record<string, string> = {};\n\n    if (!formData.originPort) {\n      newErrors.originPort = 'Origin port is required';\n    }\n\n    if (!formData.destinationPort) {\n      newErrors.destinationPort = 'Destination port is required';\n    }\n\n    if (!formData.departureDate) {\n      newErrors.departureDate = 'Departure date is required';\n    } else if (formData.departureDate < new Date()) {\n      newErrors.departureDate = 'Departure date cannot be in the past';\n    }\n\n    if (formData.originPort && formData.destinationPort && \n        formData.originPort.id === formData.destinationPort.id) {\n      newErrors.destinationPort = 'Destination port must be different from origin port';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (validateForm()) {\n      onSearch(formData);\n    }\n  };\n\n  const minDate = new Date();\n  const maxDate = new Date();\n  maxDate.setMonth(maxDate.getMonth() + 6); // Allow booking up to 6 months ahead\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-lg p-6\">\n      <div className=\"mb-6\">\n        <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">Search Shipping Routes</h2>\n        <p className=\"text-gray-600\">Find the best shipping options across multiple carriers</p>\n      </div>\n\n      <form onSubmit={handleSubmit} className=\"space-y-6\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          {/* Origin Port */}\n          <div className=\"relative\">\n            <PortAutocomplete\n              label=\"Origin Port\"\n              placeholder=\"Search origin port (e.g., Shanghai, SHG)\"\n              value={formData.originPort}\n              onChange={handleOriginChange}\n              error={errors.originPort}\n              disabled={isLoading}\n              required\n            />\n          </div>\n\n          {/* Destination Port */}\n          <div className=\"relative\">\n            <PortAutocomplete\n              label=\"Destination Port\"\n              placeholder=\"Search destination port (e.g., Rotterdam, RTM)\"\n              value={formData.destinationPort}\n              onChange={handleDestinationChange}\n              error={errors.destinationPort}\n              disabled={isLoading}\n              required\n            />\n            \n            {/* Swap Ports Button */}\n            <button\n              type=\"button\"\n              onClick={handleSwapPorts}\n              disabled={isLoading}\n              className=\"absolute top-8 -left-6 md:left-1/2 md:-translate-x-1/2 z-10 p-2 bg-white border border-gray-300 rounded-full shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n              title=\"Swap origin and destination\"\n            >\n              <ArrowsRightLeftIcon className=\"h-4 w-4 text-gray-600\" />\n            </button>\n          </div>\n        </div>\n\n        {/* Departure Date */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            Departure Date\n            <span className=\"text-red-500 ml-1\">*</span>\n          </label>\n          <div className=\"relative\">\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n              <CalendarIcon className=\"h-5 w-5 text-gray-400\" />\n            </div>\n            <DatePicker\n              selected={formData.departureDate}\n              onChange={handleDateChange}\n              minDate={minDate}\n              maxDate={maxDate}\n              placeholderText=\"Select departure date\"\n              disabled={isLoading}\n              className={`w-full pl-10 pr-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${\n                errors.departureDate ? 'border-red-300' : 'border-gray-300'\n              } ${isLoading ? 'bg-gray-50 cursor-not-allowed' : ''}`}\n              dateFormat=\"MMM dd, yyyy\"\n              showPopperArrow={false}\n            />\n          </div>\n          {errors.departureDate && (\n            <p className=\"mt-1 text-sm text-red-600\">{errors.departureDate}</p>\n          )}\n        </div>\n\n        {/* Search Button */}\n        <div className=\"flex justify-center\">\n          <button\n            type=\"submit\"\n            disabled={isLoading}\n            className=\"flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed min-w-[200px] justify-center\"\n          >\n            {isLoading ? (\n              <>\n                <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2\"></div>\n                Searching...\n              </>\n            ) : (\n              <>\n                <MagnifyingGlassIcon className=\"h-5 w-5 mr-2\" />\n                Search Routes\n              </>\n            )}\n          </button>\n        </div>\n      </form>\n\n      {/* Search Tips */}\n      <div className=\"mt-6 p-4 bg-blue-50 rounded-md\">\n        <h3 className=\"text-sm font-medium text-blue-900 mb-2\">Search Tips:</h3>\n        <ul className=\"text-sm text-blue-700 space-y-1\">\n          <li>• You can search by port code (e.g., \"SHG\") or port name (e.g., \"Shanghai\")</li>\n          <li>• Popular ports are shown when you click in the search field</li>\n          <li>• Use the swap button to quickly reverse origin and destination</li>\n          <li>• Departure dates can be selected up to 6 months in advance</li>\n        </ul>\n      </div>\n    </div>\n  );\n};\n\nexport default SearchForm;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AACA;;;AALA;;;;;;AA2BA,MAAM,aAAwC,CAAC,EAAE,QAAQ,EAAE,YAAY,KAAK,EAAE;;IAC5E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;QACvD,YAAY;QACZ,iBAAiB;QACjB,eAAe;IACjB;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAE9D,MAAM,qBAAqB,CAAC;QAC1B,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,YAAY;YAAK,CAAC;QAClD,IAAI,OAAO,UAAU,EAAE;YACrB,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,YAAY;gBAAG,CAAC;QAChD;IACF;IAEA,MAAM,0BAA0B,CAAC;QAC/B,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,iBAAiB;YAAK,CAAC;QACvD,IAAI,OAAO,eAAe,EAAE;YAC1B,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,iBAAiB;gBAAG,CAAC;QACrD;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,eAAe;YAAK,CAAC;QACrD,IAAI,OAAO,aAAa,EAAE;YACxB,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,eAAe;gBAAG,CAAC;QACnD;IACF;IAEA,MAAM,kBAAkB;QACtB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,YAAY,KAAK,eAAe;gBAChC,iBAAiB,KAAK,UAAU;YAClC,CAAC;IACH;IAEA,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,IAAI,CAAC,SAAS,UAAU,EAAE;YACxB,UAAU,UAAU,GAAG;QACzB;QAEA,IAAI,CAAC,SAAS,eAAe,EAAE;YAC7B,UAAU,eAAe,GAAG;QAC9B;QAEA,IAAI,CAAC,SAAS,aAAa,EAAE;YAC3B,UAAU,aAAa,GAAG;QAC5B,OAAO,IAAI,SAAS,aAAa,GAAG,IAAI,QAAQ;YAC9C,UAAU,aAAa,GAAG;QAC5B;QAEA,IAAI,SAAS,UAAU,IAAI,SAAS,eAAe,IAC/C,SAAS,UAAU,CAAC,EAAE,KAAK,SAAS,eAAe,CAAC,EAAE,EAAE;YAC1D,UAAU,eAAe,GAAG;QAC9B;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAEhB,IAAI,gBAAgB;YAClB,SAAS;QACX;IACF;IAEA,MAAM,UAAU,IAAI;IACpB,MAAM,UAAU,IAAI;IACpB,QAAQ,QAAQ,CAAC,QAAQ,QAAQ,KAAK,IAAI,qCAAqC;IAE/E,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAG/B,6LAAC;gBAAK,UAAU;gBAAc,WAAU;;kCACtC,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,mJAAA,CAAA,UAAgB;oCACf,OAAM;oCACN,aAAY;oCACZ,OAAO,SAAS,UAAU;oCAC1B,UAAU;oCACV,OAAO,OAAO,UAAU;oCACxB,UAAU;oCACV,QAAQ;;;;;;;;;;;0CAKZ,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,mJAAA,CAAA,UAAgB;wCACf,OAAM;wCACN,aAAY;wCACZ,OAAO,SAAS,eAAe;wCAC/B,UAAU;wCACV,OAAO,OAAO,eAAe;wCAC7B,UAAU;wCACV,QAAQ;;;;;;kDAIV,6LAAC;wCACC,MAAK;wCACL,SAAS;wCACT,UAAU;wCACV,WAAU;wCACV,OAAM;kDAEN,cAAA,6LAAC,wOAAA,CAAA,sBAAmB;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAMrC,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;;oCAA+C;kDAE9D,6LAAC;wCAAK,WAAU;kDAAoB;;;;;;;;;;;;0CAEtC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,0NAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;kDAE1B,6LAAC,6JAAA,CAAA,UAAU;wCACT,UAAU,SAAS,aAAa;wCAChC,UAAU;wCACV,SAAS;wCACT,SAAS;wCACT,iBAAgB;wCAChB,UAAU;wCACV,WAAW,CAAC,6HAA6H,EACvI,OAAO,aAAa,GAAG,mBAAmB,kBAC3C,CAAC,EAAE,YAAY,kCAAkC,IAAI;wCACtD,YAAW;wCACX,iBAAiB;;;;;;;;;;;;4BAGpB,OAAO,aAAa,kBACnB,6LAAC;gCAAE,WAAU;0CAA6B,OAAO,aAAa;;;;;;;;;;;;kCAKlE,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,MAAK;4BACL,UAAU;4BACV,WAAU;sCAET,0BACC;;kDACE,6LAAC;wCAAI,WAAU;;;;;;oCAAuE;;6DAIxF;;kDACE,6LAAC,wOAAA,CAAA,sBAAmB;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAS1D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC;0CAAG;;;;;;0CACJ,6LAAC;0CAAG;;;;;;0CACJ,6LAAC;0CAAG;;;;;;0CACJ,6LAAC;0CAAG;;;;;;;;;;;;;;;;;;;;;;;;AAKd;GAzLM;KAAA;uCA2LS", "debugId": null}}, {"offset": {"line": 712, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/vo/digital-freight/frontend/src/components/search/SearchResults.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { \n  TruckIcon, \n  ClockIcon, \n  MapPinIcon, \n  ArrowDownTrayIcon,\n  FunnelIcon,\n  ArrowsUpDownIcon,\n  EyeIcon,\n  BookmarkIcon\n} from '@heroicons/react/24/outline';\n\nexport interface SearchResult {\n  id: string;\n  carrier: {\n    name: string;\n    code: string;\n  };\n  vesselName: string;\n  voyageNumber: string;\n  serviceName?: string;\n  departureDate: string;\n  arrivalDate: string;\n  transitDays: number;\n  portRotation: Array<{\n    portCode: string;\n    portName: string;\n    country: string;\n    sequenceOrder: number;\n    arrivalDate?: string;\n    departureDate?: string;\n  }>;\n}\n\nexport interface SearchResponse {\n  searchId: string;\n  results: SearchResult[];\n  totalResults: number;\n  searchDurationMs: number;\n  carriers: string[];\n  metadata: {\n    originPort: {\n      code: string;\n      name: string;\n      country: string;\n    };\n    destinationPort: {\n      code: string;\n      name: string;\n      country: string;\n    };\n    departureDate: string;\n    searchTimestamp: string;\n  };\n}\n\ninterface SearchResultsProps {\n  searchResponse: SearchResponse | null;\n  isLoading: boolean;\n  onExport?: (searchId: string, format: string) => void;\n  onViewDetails?: (result: SearchResult) => void;\n  onSaveSearch?: (searchId: string) => void;\n}\n\ntype SortField = 'departureDate' | 'arrivalDate' | 'transitDays' | 'carrier';\ntype SortOrder = 'asc' | 'desc';\n\nconst SearchResults: React.FC<SearchResultsProps> = ({\n  searchResponse,\n  isLoading,\n  onExport,\n  onViewDetails,\n  onSaveSearch,\n}) => {\n  const [sortField, setSortField] = useState<SortField>('departureDate');\n  const [sortOrder, setSortOrder] = useState<SortOrder>('asc');\n  const [showFilters, setShowFilters] = useState(false);\n  const [filters, setFilters] = useState({\n    maxTransitDays: '',\n    minTransitDays: '',\n    carriers: [] as string[],\n  });\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      month: 'short',\n      day: 'numeric',\n      year: 'numeric',\n    });\n  };\n\n  const formatDuration = (ms: number) => {\n    if (ms < 1000) return `${ms}ms`;\n    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;\n    return `${(ms / 60000).toFixed(1)}min`;\n  };\n\n  const handleSort = (field: SortField) => {\n    if (sortField === field) {\n      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');\n    } else {\n      setSortField(field);\n      setSortOrder('asc');\n    }\n  };\n\n  const getSortedResults = () => {\n    if (!searchResponse) return [];\n    \n    const results = [...searchResponse.results];\n    \n    results.sort((a, b) => {\n      let aValue: any, bValue: any;\n\n      switch (sortField) {\n        case 'departureDate':\n          aValue = new Date(a.departureDate).getTime();\n          bValue = new Date(b.departureDate).getTime();\n          break;\n        case 'arrivalDate':\n          aValue = new Date(a.arrivalDate).getTime();\n          bValue = new Date(b.arrivalDate).getTime();\n          break;\n        case 'transitDays':\n          aValue = a.transitDays;\n          bValue = b.transitDays;\n          break;\n        case 'carrier':\n          aValue = a.carrier.name;\n          bValue = b.carrier.name;\n          break;\n        default:\n          return 0;\n      }\n\n      if (sortOrder === 'desc') {\n        return bValue > aValue ? 1 : bValue < aValue ? -1 : 0;\n      } else {\n        return aValue > bValue ? 1 : aValue < bValue ? -1 : 0;\n      }\n    });\n\n    return results;\n  };\n\n  const getCarrierColor = (carrierCode: string) => {\n    const colors: Record<string, string> = {\n      MAEU: 'bg-blue-100 text-blue-800',\n      HLCU: 'bg-orange-100 text-orange-800',\n      CMDU: 'bg-green-100 text-green-800',\n      MSCU: 'bg-purple-100 text-purple-800',\n    };\n    return colors[carrierCode] || 'bg-gray-100 text-gray-800';\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"text-center py-12\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n        <p className=\"text-gray-600\">Searching across multiple carriers...</p>\n        <p className=\"text-sm text-gray-500 mt-2\">This may take a few moments</p>\n      </div>\n    );\n  }\n\n  if (!searchResponse) {\n    return (\n      <div className=\"text-center py-12\">\n        <TruckIcon className=\"h-16 w-16 text-gray-300 mx-auto mb-4\" />\n        <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Ready to Search</h3>\n        <p className=\"text-gray-600\">\n          Enter your search criteria above to find shipping routes.\n        </p>\n      </div>\n    );\n  }\n\n  const sortedResults = getSortedResults();\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Search Summary */}\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <div>\n            <h2 className=\"text-2xl font-bold text-gray-900\">\n              Search Results ({searchResponse.totalResults})\n            </h2>\n            <p className=\"text-gray-600\">\n              {searchResponse.metadata.originPort.code} ({searchResponse.metadata.originPort.name}) → {' '}\n              {searchResponse.metadata.destinationPort.code} ({searchResponse.metadata.destinationPort.name})\n            </p>\n            <p className=\"text-sm text-gray-500\">\n              Departure: {formatDate(searchResponse.metadata.departureDate)} • \n              Search completed in {formatDuration(searchResponse.searchDurationMs)} • \n              {searchResponse.carriers.length} carrier(s)\n            </p>\n          </div>\n          \n          <div className=\"flex items-center space-x-3\">\n            <button\n              onClick={() => setShowFilters(!showFilters)}\n              className=\"flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50\"\n            >\n              <FunnelIcon className=\"h-4 w-4 mr-2\" />\n              Filters\n            </button>\n            \n            {onExport && (\n              <button\n                onClick={() => onExport(searchResponse.searchId, 'csv')}\n                className=\"flex items-center px-3 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700\"\n              >\n                <ArrowDownTrayIcon className=\"h-4 w-4 mr-2\" />\n                Export CSV\n              </button>\n            )}\n          </div>\n        </div>\n\n        {/* Carrier Summary */}\n        <div className=\"flex flex-wrap gap-2\">\n          {searchResponse.carriers.map(carrierCode => (\n            <span\n              key={carrierCode}\n              className={`px-2 py-1 text-xs font-medium rounded-full ${getCarrierColor(carrierCode)}`}\n            >\n              {carrierCode}\n            </span>\n          ))}\n        </div>\n      </div>\n\n      {/* Filters Panel */}\n      {showFilters && (\n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Filter Results</h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Min Transit Days\n              </label>\n              <input\n                type=\"number\"\n                value={filters.minTransitDays}\n                onChange={(e) => setFilters(prev => ({ ...prev, minTransitDays: e.target.value }))}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                placeholder=\"e.g., 20\"\n              />\n            </div>\n            \n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Max Transit Days\n              </label>\n              <input\n                type=\"number\"\n                value={filters.maxTransitDays}\n                onChange={(e) => setFilters(prev => ({ ...prev, maxTransitDays: e.target.value }))}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                placeholder=\"e.g., 40\"\n              />\n            </div>\n            \n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Carriers\n              </label>\n              <select\n                multiple\n                value={filters.carriers}\n                onChange={(e) => setFilters(prev => ({ \n                  ...prev, \n                  carriers: Array.from(e.target.selectedOptions, option => option.value)\n                }))}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              >\n                {searchResponse.carriers.map(carrier => (\n                  <option key={carrier} value={carrier}>{carrier}</option>\n                ))}\n              </select>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Sort Controls */}\n      <div className=\"flex items-center space-x-4 text-sm\">\n        <span className=\"text-gray-600\">Sort by:</span>\n        {[\n          { field: 'departureDate' as SortField, label: 'Departure Date' },\n          { field: 'arrivalDate' as SortField, label: 'Arrival Date' },\n          { field: 'transitDays' as SortField, label: 'Transit Time' },\n          { field: 'carrier' as SortField, label: 'Carrier' },\n        ].map(({ field, label }) => (\n          <button\n            key={field}\n            onClick={() => handleSort(field)}\n            className={`flex items-center px-3 py-1 rounded-md transition-colors ${\n              sortField === field\n                ? 'bg-blue-100 text-blue-700'\n                : 'text-gray-600 hover:bg-gray-100'\n            }`}\n          >\n            {label}\n            {sortField === field && (\n              <ArrowsUpDownIcon className={`h-3 w-3 ml-1 ${sortOrder === 'desc' ? 'rotate-180' : ''}`} />\n            )}\n          </button>\n        ))}\n      </div>\n\n      {/* Results List */}\n      {sortedResults.length === 0 ? (\n        <div className=\"text-center py-12 bg-white rounded-lg shadow\">\n          <TruckIcon className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No routes found</h3>\n          <p className=\"text-gray-600\">\n            Try adjusting your search criteria or selecting different ports.\n          </p>\n        </div>\n      ) : (\n        <div className=\"space-y-4\">\n          {sortedResults.map((result) => (\n            <div key={result.id} className=\"bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow\">\n              <div className=\"flex items-start justify-between mb-4\">\n                <div className=\"flex-1\">\n                  <div className=\"flex items-center space-x-3 mb-2\">\n                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getCarrierColor(result.carrier.code)}`}>\n                      {result.carrier.code}\n                    </span>\n                    <h3 className=\"text-lg font-semibold text-gray-900\">\n                      {result.carrier.name}\n                    </h3>\n                  </div>\n                  <p className=\"text-gray-600\">\n                    {result.vesselName} • {result.voyageNumber}\n                    {result.serviceName && ` • ${result.serviceName}`}\n                  </p>\n                </div>\n                \n                <div className=\"text-right\">\n                  <div className=\"flex items-center text-sm text-gray-500 mb-1\">\n                    <ClockIcon className=\"h-4 w-4 mr-1\" />\n                    {result.transitDays} days\n                  </div>\n                  <div className=\"text-lg font-semibold text-gray-900\">\n                    {formatDate(result.departureDate)} → {formatDate(result.arrivalDate)}\n                  </div>\n                </div>\n              </div>\n\n              {/* Port Rotation */}\n              <div className=\"border-t pt-4\">\n                <h4 className=\"text-sm font-medium text-gray-700 mb-3\">Port Rotation</h4>\n                <div className=\"flex items-center space-x-2 overflow-x-auto pb-2\">\n                  {result.portRotation.map((port, index) => (\n                    <React.Fragment key={`${port.portCode}-${port.sequenceOrder}`}>\n                      <div className=\"flex-shrink-0 text-center\">\n                        <div className=\"flex items-center justify-center w-8 h-8 bg-blue-100 rounded-full mb-1\">\n                          <MapPinIcon className=\"h-4 w-4 text-blue-600\" />\n                        </div>\n                        <div className=\"text-xs font-medium text-gray-900\">{port.portCode}</div>\n                        <div className=\"text-xs text-gray-500 max-w-20 truncate\">{port.portName}</div>\n                        {port.departureDate && (\n                          <div className=\"text-xs text-gray-400 mt-1\">\n                            {formatDate(port.departureDate)}\n                          </div>\n                        )}\n                      </div>\n                      {index < result.portRotation.length - 1 && (\n                        <div className=\"flex-shrink-0 w-8 h-px bg-gray-300\"></div>\n                      )}\n                    </React.Fragment>\n                  ))}\n                </div>\n              </div>\n\n              {/* Action Buttons */}\n              <div className=\"flex items-center justify-end space-x-3 mt-4 pt-4 border-t\">\n                {onSaveSearch && (\n                  <button\n                    onClick={() => onSaveSearch(searchResponse.searchId)}\n                    className=\"flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors\"\n                  >\n                    <BookmarkIcon className=\"h-4 w-4 mr-2\" />\n                    Save\n                  </button>\n                )}\n                \n                {onViewDetails && (\n                  <button\n                    onClick={() => onViewDetails(result)}\n                    className=\"flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors\"\n                  >\n                    <EyeIcon className=\"h-4 w-4 mr-2\" />\n                    View Details\n                  </button>\n                )}\n                \n                <button className=\"flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors\">\n                  Book Now\n                </button>\n              </div>\n            </div>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default SearchResults;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AAqEA,MAAM,gBAA8C,CAAC,EACnD,cAAc,EACd,SAAS,EACT,QAAQ,EACR,aAAa,EACb,YAAY,EACb;;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa;IACtD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa;IACtD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACrC,gBAAgB;QAChB,gBAAgB;QAChB,UAAU,EAAE;IACd;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,OAAO;YACP,KAAK;YACL,MAAM;QACR;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,KAAK,MAAM,OAAO,GAAG,GAAG,EAAE,CAAC;QAC/B,IAAI,KAAK,OAAO,OAAO,GAAG,CAAC,KAAK,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QACnD,OAAO,GAAG,CAAC,KAAK,KAAK,EAAE,OAAO,CAAC,GAAG,GAAG,CAAC;IACxC;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,cAAc,OAAO;YACvB,aAAa,cAAc,QAAQ,SAAS;QAC9C,OAAO;YACL,aAAa;YACb,aAAa;QACf;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,CAAC,gBAAgB,OAAO,EAAE;QAE9B,MAAM,UAAU;eAAI,eAAe,OAAO;SAAC;QAE3C,QAAQ,IAAI,CAAC,CAAC,GAAG;YACf,IAAI,QAAa;YAEjB,OAAQ;gBACN,KAAK;oBACH,SAAS,IAAI,KAAK,EAAE,aAAa,EAAE,OAAO;oBAC1C,SAAS,IAAI,KAAK,EAAE,aAAa,EAAE,OAAO;oBAC1C;gBACF,KAAK;oBACH,SAAS,IAAI,KAAK,EAAE,WAAW,EAAE,OAAO;oBACxC,SAAS,IAAI,KAAK,EAAE,WAAW,EAAE,OAAO;oBACxC;gBACF,KAAK;oBACH,SAAS,EAAE,WAAW;oBACtB,SAAS,EAAE,WAAW;oBACtB;gBACF,KAAK;oBACH,SAAS,EAAE,OAAO,CAAC,IAAI;oBACvB,SAAS,EAAE,OAAO,CAAC,IAAI;oBACvB;gBACF;oBACE,OAAO;YACX;YAEA,IAAI,cAAc,QAAQ;gBACxB,OAAO,SAAS,SAAS,IAAI,SAAS,SAAS,CAAC,IAAI;YACtD,OAAO;gBACL,OAAO,SAAS,SAAS,IAAI,SAAS,SAAS,CAAC,IAAI;YACtD;QACF;QAEA,OAAO;IACT;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,SAAiC;YACrC,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;QACR;QACA,OAAO,MAAM,CAAC,YAAY,IAAI;IAChC;IAEA,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAE,WAAU;8BAAgB;;;;;;8BAC7B,6LAAC;oBAAE,WAAU;8BAA6B;;;;;;;;;;;;IAGhD;IAEA,IAAI,CAAC,gBAAgB;QACnB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,oNAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;8BACrB,6LAAC;oBAAG,WAAU;8BAAyC;;;;;;8BACvD,6LAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;IAKnC;IAEA,MAAM,gBAAgB;IAEtB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;;4CAAmC;4CAC9B,eAAe,YAAY;4CAAC;;;;;;;kDAE/C,6LAAC;wCAAE,WAAU;;4CACV,eAAe,QAAQ,CAAC,UAAU,CAAC,IAAI;4CAAC;4CAAG,eAAe,QAAQ,CAAC,UAAU,CAAC,IAAI;4CAAC;4CAAK;4CACxF,eAAe,QAAQ,CAAC,eAAe,CAAC,IAAI;4CAAC;4CAAG,eAAe,QAAQ,CAAC,eAAe,CAAC,IAAI;4CAAC;;;;;;;kDAEhG,6LAAC;wCAAE,WAAU;;4CAAwB;4CACvB,WAAW,eAAe,QAAQ,CAAC,aAAa;4CAAE;4CACzC,eAAe,eAAe,gBAAgB;4CAAE;4CACpE,eAAe,QAAQ,CAAC,MAAM;4CAAC;;;;;;;;;;;;;0CAIpC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,eAAe,CAAC;wCAC/B,WAAU;;0DAEV,6LAAC,sNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;oCAIxC,0BACC,6LAAC;wCACC,SAAS,IAAM,SAAS,eAAe,QAAQ,EAAE;wCACjD,WAAU;;0DAEV,6LAAC,oOAAA,CAAA,oBAAiB;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;kCAQtD,6LAAC;wBAAI,WAAU;kCACZ,eAAe,QAAQ,CAAC,GAAG,CAAC,CAAA,4BAC3B,6LAAC;gCAEC,WAAW,CAAC,2CAA2C,EAAE,gBAAgB,cAAc;0CAEtF;+BAHI;;;;;;;;;;;;;;;;YAUZ,6BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCACC,MAAK;wCACL,OAAO,QAAQ,cAAc;wCAC7B,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,gBAAgB,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCAChF,WAAU;wCACV,aAAY;;;;;;;;;;;;0CAIhB,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCACC,MAAK;wCACL,OAAO,QAAQ,cAAc;wCAC7B,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,gBAAgB,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCAChF,WAAU;wCACV,aAAY;;;;;;;;;;;;0CAIhB,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCACC,QAAQ;wCACR,OAAO,QAAQ,QAAQ;wCACvB,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;oDACnC,GAAG,IAAI;oDACP,UAAU,MAAM,IAAI,CAAC,EAAE,MAAM,CAAC,eAAe,EAAE,CAAA,SAAU,OAAO,KAAK;gDACvE,CAAC;wCACD,WAAU;kDAET,eAAe,QAAQ,CAAC,GAAG,CAAC,CAAA,wBAC3B,6LAAC;gDAAqB,OAAO;0DAAU;+CAA1B;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASzB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAK,WAAU;kCAAgB;;;;;;oBAC/B;wBACC;4BAAE,OAAO;4BAA8B,OAAO;wBAAiB;wBAC/D;4BAAE,OAAO;4BAA4B,OAAO;wBAAe;wBAC3D;4BAAE,OAAO;4BAA4B,OAAO;wBAAe;wBAC3D;4BAAE,OAAO;4BAAwB,OAAO;wBAAU;qBACnD,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,iBACrB,6LAAC;4BAEC,SAAS,IAAM,WAAW;4BAC1B,WAAW,CAAC,yDAAyD,EACnE,cAAc,QACV,8BACA,mCACJ;;gCAED;gCACA,cAAc,uBACb,6LAAC,kOAAA,CAAA,mBAAgB;oCAAC,WAAW,CAAC,aAAa,EAAE,cAAc,SAAS,eAAe,IAAI;;;;;;;2BAVpF;;;;;;;;;;;YAiBV,cAAc,MAAM,KAAK,kBACxB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oNAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;kCACrB,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;qCAK/B,6LAAC;gBAAI,WAAU;0BACZ,cAAc,GAAG,CAAC,CAAC,uBAClB,6LAAC;wBAAoB,WAAU;;0CAC7B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAW,CAAC,2CAA2C,EAAE,gBAAgB,OAAO,OAAO,CAAC,IAAI,GAAG;kEAClG,OAAO,OAAO,CAAC,IAAI;;;;;;kEAEtB,6LAAC;wDAAG,WAAU;kEACX,OAAO,OAAO,CAAC,IAAI;;;;;;;;;;;;0DAGxB,6LAAC;gDAAE,WAAU;;oDACV,OAAO,UAAU;oDAAC;oDAAI,OAAO,YAAY;oDACzC,OAAO,WAAW,IAAI,CAAC,GAAG,EAAE,OAAO,WAAW,EAAE;;;;;;;;;;;;;kDAIrD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDACpB,OAAO,WAAW;oDAAC;;;;;;;0DAEtB,6LAAC;gDAAI,WAAU;;oDACZ,WAAW,OAAO,aAAa;oDAAE;oDAAI,WAAW,OAAO,WAAW;;;;;;;;;;;;;;;;;;;0CAMzE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,6LAAC;wCAAI,WAAU;kDACZ,OAAO,YAAY,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC9B,6LAAC,6JAAA,CAAA,UAAK,CAAC,QAAQ;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC,sNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;;;;;;0EAExB,6LAAC;gEAAI,WAAU;0EAAqC,KAAK,QAAQ;;;;;;0EACjE,6LAAC;gEAAI,WAAU;0EAA2C,KAAK,QAAQ;;;;;;4DACtE,KAAK,aAAa,kBACjB,6LAAC;gEAAI,WAAU;0EACZ,WAAW,KAAK,aAAa;;;;;;;;;;;;oDAInC,QAAQ,OAAO,YAAY,CAAC,MAAM,GAAG,mBACpC,6LAAC;wDAAI,WAAU;;;;;;;+CAdE,GAAG,KAAK,QAAQ,CAAC,CAAC,EAAE,KAAK,aAAa,EAAE;;;;;;;;;;;;;;;;0CAsBnE,6LAAC;gCAAI,WAAU;;oCACZ,8BACC,6LAAC;wCACC,SAAS,IAAM,aAAa,eAAe,QAAQ;wCACnD,WAAU;;0DAEV,6LAAC,0NAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;oCAK5C,+BACC,6LAAC;wCACC,SAAS,IAAM,cAAc;wCAC7B,WAAU;;0DAEV,6LAAC,gNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAKxC,6LAAC;wCAAO,WAAU;kDAAwH;;;;;;;;;;;;;uBA5EpI,OAAO,EAAE;;;;;;;;;;;;;;;;AAsF/B;GAvVM;KAAA;uCAyVS", "debugId": null}}, {"offset": {"line": 1485, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/vo/digital-freight/frontend/src/app/search/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport axios from 'axios';\nimport SearchForm from '@/components/search/SearchForm';\nimport SearchResults, { SearchResponse } from '@/components/search/SearchResults';\nimport { TruckIcon } from '@heroicons/react/24/outline';\n\ninterface Port {\n  id: string;\n  code: string;\n  name: string;\n  countryName: string;\n  displayName: string;\n}\n\ninterface SearchFormData {\n  originPort: Port | null;\n  destinationPort: Port | null;\n  departureDate: Date | null;\n}\n\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001';\n\nconst SearchPage: React.FC = () => {\n  const [searchResponse, setSearchResponse] = useState<SearchResponse | null>(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string>('');\n\n  const handleSearch = async (searchData: SearchFormData) => {\n    if (!searchData.originPort || !searchData.destinationPort || !searchData.departureDate) {\n      setError('Please fill in all search fields');\n      return;\n    }\n\n    setIsLoading(true);\n    setError('');\n\n    try {\n      const response = await axios.post(`${API_BASE_URL}/api/search/routes`, {\n        originPortId: searchData.originPort.id,\n        destinationPortId: searchData.destinationPort.id,\n        departureDate: searchData.departureDate.toISOString().split('T')[0],\n        carriers: ['MAEU'], // Start with Maersk only\n      });\n\n      setSearchResponse(response.data.data);\n    } catch (err: any) {\n      console.error('Search error:', err);\n      setError(err.response?.data?.message || 'Search failed. Please try again.');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleExport = async (searchId: string, format: string) => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/api/search/${searchId}/export?format=${format}`, {\n        responseType: 'blob',\n      });\n\n      const blob = new Blob([response.data], { type: 'text/csv' });\n      const url = window.URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = `search-results-${searchId}.csv`;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      window.URL.revokeObjectURL(url);\n    } catch (err) {\n      console.error('Export error:', err);\n      setError('Failed to export results');\n    }\n  };\n\n  const handleViewDetails = (result: any) => {\n    // TODO: Implement detailed view modal or navigation\n    console.log('View details for:', result);\n  };\n\n  const handleSaveSearch = async (searchId: string) => {\n    // TODO: Implement save search functionality\n    console.log('Save search:', searchId);\n  };\n\n  return (\n    <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n      {/* Page Header */}\n      <div className=\"text-center mb-8\">\n        <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">\n          Find Your Perfect Shipping Route\n        </h1>\n        <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n          Search across multiple carriers to find the best shipping options for your freight forwarding needs.\n        </p>\n      </div>\n\n      {/* Search Form */}\n      <div className=\"mb-8\">\n        <SearchForm onSearch={handleSearch} isLoading={isLoading} />\n      </div>\n\n      {/* Error Message */}\n      {error && (\n        <div className=\"mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md\">\n          {error}\n        </div>\n      )}\n\n      {/* Search Results */}\n      <SearchResults\n        searchResponse={searchResponse}\n        isLoading={isLoading}\n        onExport={handleExport}\n        onViewDetails={handleViewDetails}\n        onSaveSearch={handleSaveSearch}\n      />\n    </div>\n  );\n};\n\nexport default SearchPage;\n"], "names": [], "mappings": ";;;AAsBqB;;AApBrB;AACA;AACA;AACA;;;AALA;;;;;AAsBA,MAAM,eAAe,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI;AAExD,MAAM,aAAuB;;IAC3B,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyB;IAC5E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAE3C,MAAM,eAAe,OAAO;QAC1B,IAAI,CAAC,WAAW,UAAU,IAAI,CAAC,WAAW,eAAe,IAAI,CAAC,WAAW,aAAa,EAAE;YACtF,SAAS;YACT;QACF;QAEA,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,GAAG,aAAa,kBAAkB,CAAC,EAAE;gBACrE,cAAc,WAAW,UAAU,CAAC,EAAE;gBACtC,mBAAmB,WAAW,eAAe,CAAC,EAAE;gBAChD,eAAe,WAAW,aAAa,CAAC,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gBACnE,UAAU;oBAAC;iBAAO;YACpB;YAEA,kBAAkB,SAAS,IAAI,CAAC,IAAI;QACtC,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,SAAS,IAAI,QAAQ,EAAE,MAAM,WAAW;QAC1C,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,eAAe,OAAO,UAAkB;QAC5C,IAAI;YACF,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,GAAG,aAAa,YAAY,EAAE,SAAS,eAAe,EAAE,QAAQ,EAAE;gBACjG,cAAc;YAChB;YAEA,MAAM,OAAO,IAAI,KAAK;gBAAC,SAAS,IAAI;aAAC,EAAE;gBAAE,MAAM;YAAW;YAC1D,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;YACvC,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,IAAI,GAAG;YACZ,KAAK,QAAQ,GAAG,CAAC,eAAe,EAAE,SAAS,IAAI,CAAC;YAChD,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,KAAK,KAAK;YACV,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,OAAO,GAAG,CAAC,eAAe,CAAC;QAC7B,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,SAAS;QACX;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,oDAAoD;QACpD,QAAQ,GAAG,CAAC,qBAAqB;IACnC;IAEA,MAAM,mBAAmB,OAAO;QAC9B,4CAA4C;QAC5C,QAAQ,GAAG,CAAC,gBAAgB;IAC9B;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAGtD,6LAAC;wBAAE,WAAU;kCAA0C;;;;;;;;;;;;0BAMzD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6IAAA,CAAA,UAAU;oBAAC,UAAU;oBAAc,WAAW;;;;;;;;;;;YAIhD,uBACC,6LAAC;gBAAI,WAAU;0BACZ;;;;;;0BAKL,6LAAC,gJAAA,CAAA,UAAa;gBACZ,gBAAgB;gBAChB,WAAW;gBACX,UAAU;gBACV,eAAe;gBACf,cAAc;;;;;;;;;;;;AAItB;GAhGM;KAAA;uCAkGS", "debugId": null}}]}