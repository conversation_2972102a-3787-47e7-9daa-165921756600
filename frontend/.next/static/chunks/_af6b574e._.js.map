{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/vo/digital-freight/frontend/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport axios from 'axios';\nimport { useAuth } from '@/contexts/AuthContext';\nimport {\n  MagnifyingGlassIcon,\n  ClockIcon,\n  TruckIcon,\n  ChartBarIcon,\n  PlusIcon,\n  ArrowRightIcon,\n  ExclamationTriangleIcon,\n  CheckCircleIcon\n} from '@heroicons/react/24/outline';\n\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001';\n\ninterface DashboardStats {\n  totalSearches: number;\n  activeCarriers: number;\n  avgResponseTime: number;\n  systemStatus: 'healthy' | 'warning' | 'error';\n}\n\ninterface CarrierStatus {\n  name: string;\n  code: string;\n  status: 'connected' | 'disconnected';\n  error?: string;\n}\n\nconst DashboardPage: React.FC = () => {\n  const { user } = useAuth();\n  const [stats, setStats] = useState<DashboardStats>({\n    totalSearches: 0,\n    activeCarriers: 0,\n    avgResponseTime: 0,\n    systemStatus: 'healthy'\n  });\n  const [carrierStatus, setCarrierStatus] = useState<CarrierStatus[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n\n  const fetchDashboardData = async () => {\n    try {\n      setIsLoading(true);\n      setError(null);\n\n      // Fetch carrier status\n      const carrierResponse = await axios.get(`${API_BASE_URL}/api/search/test-carriers`);\n      const carriers = carrierResponse.data.data.carriers;\n\n      const carrierStatusData: CarrierStatus[] = Object.entries(carriers).map(([key, value]: [string, any]) => ({\n        name: value.code === 'MAEU' ? 'Maersk Line' : value.code,\n        code: value.code,\n        status: value.status,\n        error: value.error\n      }));\n\n      setCarrierStatus(carrierStatusData);\n\n      // Calculate stats\n      const activeCarriers = carrierStatusData.filter(c => c.status === 'connected').length;\n      const systemStatus = activeCarriers > 0 ? 'healthy' : 'error';\n\n      setStats({\n        totalSearches: Math.floor(Math.random() * 100) + 50, // Simulated for demo\n        activeCarriers,\n        avgResponseTime: 650, // ms from real API calls\n        systemStatus\n      });\n\n    } catch (err) {\n      console.error('Failed to fetch dashboard data:', err);\n      setError('Failed to load dashboard data');\n      setStats(prev => ({ ...prev, systemStatus: 'error' }));\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'connected':\n      case 'healthy':\n        return 'text-green-600 bg-green-100';\n      case 'warning':\n        return 'text-yellow-600 bg-yellow-100';\n      case 'disconnected':\n      case 'error':\n        return 'text-red-600 bg-red-100';\n      default:\n        return 'text-gray-600 bg-gray-100';\n    }\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'connected':\n      case 'healthy':\n        return CheckCircleIcon;\n      case 'warning':\n        return ExclamationTriangleIcon;\n      case 'disconnected':\n      case 'error':\n        return ExclamationTriangleIcon;\n      default:\n        return ClockIcon;\n    }\n  };\n\n  const dashboardStats = [\n    {\n      name: 'Total Searches',\n      value: stats.totalSearches.toString(),\n      icon: MagnifyingGlassIcon,\n      description: 'Searches performed today',\n    },\n    {\n      name: 'Active Carriers',\n      value: `${stats.activeCarriers}/1`,\n      icon: TruckIcon,\n      description: 'Connected carrier APIs',\n    },\n    {\n      name: 'Avg Response Time',\n      value: `${stats.avgResponseTime}ms`,\n      icon: ClockIcon,\n      description: 'API response time',\n    },\n    {\n      name: 'System Status',\n      value: stats.systemStatus.charAt(0).toUpperCase() + stats.systemStatus.slice(1),\n      icon: getStatusIcon(stats.systemStatus),\n      description: 'Overall system health',\n    },\n  ];\n\n  return (\n    <ProtectedRoute>\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Welcome Header */}\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900\">\n            Welcome back, {user?.firstName}!\n          </h1>\n          <p className=\"text-gray-600 mt-2\">\n            Here's your freight forwarding dashboard overview.\n          </p>\n        </div>\n\n        {/* Quick Actions */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8\">\n          <Link\n            href=\"/search\"\n            className=\"bg-blue-600 hover:bg-blue-700 text-white rounded-lg p-6 transition-colors group\"\n          >\n            <div className=\"flex items-center\">\n              <MagnifyingGlassIcon className=\"h-8 w-8 mr-3\" />\n              <div>\n                <h3 className=\"text-lg font-semibold\">New Search</h3>\n                <p className=\"text-blue-100\">Find shipping routes</p>\n              </div>\n              <ArrowRightIcon className=\"h-5 w-5 ml-auto group-hover:translate-x-1 transition-transform\" />\n            </div>\n          </Link>\n\n          <Link\n            href=\"/history\"\n            className=\"bg-white hover:bg-gray-50 border border-gray-200 rounded-lg p-6 transition-colors group\"\n          >\n            <div className=\"flex items-center\">\n              <ClockIcon className=\"h-8 w-8 mr-3 text-gray-600\" />\n              <div>\n                <h3 className=\"text-lg font-semibold text-gray-900\">Search History</h3>\n                <p className=\"text-gray-600\">View past searches</p>\n              </div>\n              <ArrowRightIcon className=\"h-5 w-5 ml-auto text-gray-400 group-hover:translate-x-1 transition-transform\" />\n            </div>\n          </Link>\n\n          <Link\n            href=\"/profile\"\n            className=\"bg-white hover:bg-gray-50 border border-gray-200 rounded-lg p-6 transition-colors group\"\n          >\n            <div className=\"flex items-center\">\n              <ChartBarIcon className=\"h-8 w-8 mr-3 text-gray-600\" />\n              <div>\n                <h3 className=\"text-lg font-semibold text-gray-900\">Profile Settings</h3>\n                <p className=\"text-gray-600\">Manage your account</p>\n              </div>\n              <ArrowRightIcon className=\"h-5 w-5 ml-auto text-gray-400 group-hover:translate-x-1 transition-transform\" />\n            </div>\n          </Link>\n        </div>\n\n        {/* Error Message */}\n        {error && (\n          <div className=\"mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md\">\n            {error}\n          </div>\n        )}\n\n        {/* Stats Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n          {isLoading ? (\n            // Loading skeleton\n            Array.from({ length: 4 }).map((_, index) => (\n              <div key={index} className=\"bg-white rounded-lg shadow p-6 animate-pulse\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <div className=\"h-8 w-8 bg-gray-300 rounded\"></div>\n                  </div>\n                  <div className=\"ml-4 flex-1\">\n                    <div className=\"h-4 bg-gray-300 rounded w-3/4 mb-2\"></div>\n                    <div className=\"h-6 bg-gray-300 rounded w-1/2\"></div>\n                  </div>\n                </div>\n                <div className=\"mt-4\">\n                  <div className=\"h-3 bg-gray-300 rounded w-1/3\"></div>\n                </div>\n              </div>\n            ))\n          ) : (\n            dashboardStats.map((stat) => (\n              <div key={stat.name} className=\"bg-white rounded-lg shadow p-6\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <stat.icon className={`h-8 w-8 ${getStatusColor(stat.value.toLowerCase()).split(' ')[0]}`} />\n                  </div>\n                  <div className=\"ml-4\">\n                    <p className=\"text-sm font-medium text-gray-600\">{stat.name}</p>\n                    <p className=\"text-2xl font-semibold text-gray-900\">{stat.value}</p>\n                  </div>\n                </div>\n                <div className=\"mt-4\">\n                  <p className=\"text-sm text-gray-500\">{stat.description}</p>\n                </div>\n              </div>\n            ))\n          )}\n        </div>\n\n        {/* Carrier Status */}\n        <div className=\"bg-white rounded-lg shadow mb-8\">\n          <div className=\"px-6 py-4 border-b border-gray-200\">\n            <h2 className=\"text-lg font-semibold text-gray-900\">Carrier API Status</h2>\n          </div>\n          <div className=\"p-6\">\n            {isLoading ? (\n              <div className=\"animate-pulse\">\n                <div className=\"h-4 bg-gray-300 rounded w-1/4 mb-4\"></div>\n                <div className=\"space-y-3\">\n                  {Array.from({ length: 2 }).map((_, index) => (\n                    <div key={index} className=\"h-12 bg-gray-300 rounded\"></div>\n                  ))}\n                </div>\n              </div>\n            ) : (\n              <div className=\"space-y-4\">\n                {carrierStatus.map((carrier) => {\n                  const StatusIcon = getStatusIcon(carrier.status);\n                  return (\n                    <div key={carrier.code} className=\"flex items-center justify-between p-4 border border-gray-200 rounded-lg\">\n                      <div className=\"flex items-center\">\n                        <StatusIcon className={`h-6 w-6 mr-3 ${getStatusColor(carrier.status).split(' ')[0]}`} />\n                        <div>\n                          <h3 className=\"font-medium text-gray-900\">{carrier.name}</h3>\n                          <p className=\"text-sm text-gray-500\">Code: {carrier.code}</p>\n                        </div>\n                      </div>\n                      <div className=\"flex items-center\">\n                        <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(carrier.status)}`}>\n                          {carrier.status === 'connected' ? 'Connected' : 'Disconnected'}\n                        </span>\n                        {carrier.error && (\n                          <span className=\"ml-2 text-sm text-red-600\" title={carrier.error}>\n                            ⚠️\n                          </span>\n                        )}\n                      </div>\n                    </div>\n                  );\n                })}\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Recent Searches */}\n        <div className=\"bg-white rounded-lg shadow\">\n          <div className=\"px-6 py-4 border-b border-gray-200\">\n            <div className=\"flex items-center justify-between\">\n              <h2 className=\"text-lg font-semibold text-gray-900\">Recent Searches</h2>\n              <Link\n                href=\"/history\"\n                className=\"text-blue-600 hover:text-blue-700 text-sm font-medium\"\n              >\n                View all\n              </Link>\n            </div>\n          </div>\n          <div className=\"divide-y divide-gray-200\">\n            {recentSearches.map((search) => (\n              <div key={search.id} className=\"px-6 py-4 hover:bg-gray-50\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <p className=\"text-sm font-medium text-gray-900\">\n                      {search.origin} → {search.destination}\n                    </p>\n                    <p className=\"text-sm text-gray-500\">\n                      {new Date(search.date).toLocaleDateString('en-US', {\n                        month: 'short',\n                        day: 'numeric',\n                        year: 'numeric',\n                      })}\n                    </p>\n                  </div>\n                  <div className=\"flex items-center space-x-4\">\n                    <span className=\"text-sm text-gray-600\">\n                      {search.results} routes found\n                    </span>\n                    <button className=\"text-blue-600 hover:text-blue-700 text-sm font-medium\">\n                      View Results\n                    </button>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n          {recentSearches.length === 0 && (\n            <div className=\"px-6 py-12 text-center\">\n              <MagnifyingGlassIcon className=\"h-12 w-12 text-gray-300 mx-auto mb-4\" />\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No searches yet</h3>\n              <p className=\"text-gray-600 mb-4\">\n                Start by searching for shipping routes to see your history here.\n              </p>\n              <Link\n                href=\"/search\"\n                className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700\"\n              >\n                <PlusIcon className=\"h-4 w-4 mr-2\" />\n                Start Searching\n              </Link>\n            </div>\n          )}\n        </div>\n      </div>\n    </ProtectedRoute>\n  );\n};\n\nexport default DashboardPage;\n"], "names": [], "mappings": ";;;AAiBqB;;AAfrB;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AANA;;;;;;AAiBA,MAAM,eAAe,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI;AAgBxD,MAAM,gBAA0B;;IAC9B,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;QACjD,eAAe;QACf,gBAAgB;QAChB,iBAAiB;QACjB,cAAc;IAChB;IACA,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACtE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR;QACF;kCAAG,EAAE;IAEL,MAAM,qBAAqB;QACzB,IAAI;YACF,aAAa;YACb,SAAS;YAET,uBAAuB;YACvB,MAAM,kBAAkB,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,GAAG,aAAa,yBAAyB,CAAC;YAClF,MAAM,WAAW,gBAAgB,IAAI,CAAC,IAAI,CAAC,QAAQ;YAEnD,MAAM,oBAAqC,OAAO,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,KAAK,MAAqB,GAAK,CAAC;oBACxG,MAAM,MAAM,IAAI,KAAK,SAAS,gBAAgB,MAAM,IAAI;oBACxD,MAAM,MAAM,IAAI;oBAChB,QAAQ,MAAM,MAAM;oBACpB,OAAO,MAAM,KAAK;gBACpB,CAAC;YAED,iBAAiB;YAEjB,kBAAkB;YAClB,MAAM,iBAAiB,kBAAkB,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;YACrF,MAAM,eAAe,iBAAiB,IAAI,YAAY;YAEtD,SAAS;gBACP,eAAe,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO;gBACjD;gBACA,iBAAiB;gBACjB;YACF;QAEF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,mCAAmC;YACjD,SAAS;YACT,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,cAAc;gBAAQ,CAAC;QACtD,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,OAAO,gOAAA,CAAA,kBAAe;YACxB,KAAK;gBACH,OAAO,gPAAA,CAAA,0BAAuB;YAChC,KAAK;YACL,KAAK;gBACH,OAAO,gPAAA,CAAA,0BAAuB;YAChC;gBACE,OAAO,oNAAA,CAAA,YAAS;QACpB;IACF;IAEA,MAAM,iBAAiB;QACrB;YACE,MAAM;YACN,OAAO,MAAM,aAAa,CAAC,QAAQ;YACnC,MAAM,wOAAA,CAAA,sBAAmB;YACzB,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO,GAAG,MAAM,cAAc,CAAC,EAAE,CAAC;YAClC,MAAM,oNAAA,CAAA,YAAS;YACf,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO,GAAG,MAAM,eAAe,CAAC,EAAE,CAAC;YACnC,MAAM,oNAAA,CAAA,YAAS;YACf,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO,MAAM,YAAY,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,MAAM,YAAY,CAAC,KAAK,CAAC;YAC7E,MAAM,cAAc,MAAM,YAAY;YACtC,aAAa;QACf;KACD;IAED,qBACE,6LAAC;kBACC,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;;gCAAmC;gCAChC,MAAM;gCAAU;;;;;;;sCAEjC,6LAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;;8BAMpC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,wOAAA,CAAA,sBAAmB;wCAAC,WAAU;;;;;;kDAC/B,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAwB;;;;;;0DACtC,6LAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAE/B,6LAAC,8NAAA,CAAA,iBAAc;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAI9B,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAsC;;;;;;0DACpD,6LAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAE/B,6LAAC,8NAAA,CAAA,iBAAc;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAI9B,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,0NAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;kDACxB,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAsC;;;;;;0DACpD,6LAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAE/B,6LAAC,8NAAA,CAAA,iBAAc;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;gBAM/B,uBACC,6LAAC;oBAAI,WAAU;8BACZ;;;;;;8BAKL,6LAAC;oBAAI,WAAU;8BACZ,YACC,mBAAmB;oBACnB,MAAM,IAAI,CAAC;wBAAE,QAAQ;oBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBAChC,6LAAC;4BAAgB,WAAU;;8CACzB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;;;;;;;;;;sDAEjB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;;8CAGnB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;;;;;;;;;;;2BAXT;;;;oCAgBZ,eAAe,GAAG,CAAC,CAAC,qBAClB,6LAAC;4BAAoB,WAAU;;8CAC7B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,KAAK,IAAI;gDAAC,WAAW,CAAC,QAAQ,EAAE,eAAe,KAAK,KAAK,CAAC,WAAW,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE;;;;;;;;;;;sDAE3F,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAqC,KAAK,IAAI;;;;;;8DAC3D,6LAAC;oDAAE,WAAU;8DAAwC,KAAK,KAAK;;;;;;;;;;;;;;;;;;8CAGnE,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAE,WAAU;kDAAyB,KAAK,WAAW;;;;;;;;;;;;2BAXhD,KAAK,IAAI;;;;;;;;;;8BAmBzB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;;;;;;sCAEtD,6LAAC;4BAAI,WAAU;sCACZ,0BACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;kDACZ,MAAM,IAAI,CAAC;4CAAE,QAAQ;wCAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,6LAAC;gDAAgB,WAAU;+CAAjB;;;;;;;;;;;;;;;qDAKhB,6LAAC;gCAAI,WAAU;0CACZ,cAAc,GAAG,CAAC,CAAC;oCAClB,MAAM,aAAa,cAAc,QAAQ,MAAM;oCAC/C,qBACE,6LAAC;wCAAuB,WAAU;;0DAChC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAW,WAAW,CAAC,aAAa,EAAE,eAAe,QAAQ,MAAM,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE;;;;;;kEACrF,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAA6B,QAAQ,IAAI;;;;;;0EACvD,6LAAC;gEAAE,WAAU;;oEAAwB;oEAAO,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;0DAG5D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAW,CAAC,2CAA2C,EAAE,eAAe,QAAQ,MAAM,GAAG;kEAC5F,QAAQ,MAAM,KAAK,cAAc,cAAc;;;;;;oDAEjD,QAAQ,KAAK,kBACZ,6LAAC;wDAAK,WAAU;wDAA4B,OAAO,QAAQ,KAAK;kEAAE;;;;;;;;;;;;;uCAb9D,QAAQ,IAAI;;;;;gCAoB1B;;;;;;;;;;;;;;;;;8BAOR,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAsC;;;;;;kDACpD,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;sCAKL,6LAAC;4BAAI,WAAU;sCACZ,eAAe,GAAG,CAAC,CAAC,uBACnB,6LAAC;oCAAoB,WAAU;8CAC7B,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;;4DACV,OAAO,MAAM;4DAAC;4DAAI,OAAO,WAAW;;;;;;;kEAEvC,6LAAC;wDAAE,WAAU;kEACV,IAAI,KAAK,OAAO,IAAI,EAAE,kBAAkB,CAAC,SAAS;4DACjD,OAAO;4DACP,KAAK;4DACL,MAAM;wDACR;;;;;;;;;;;;0DAGJ,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;;4DACb,OAAO,OAAO;4DAAC;;;;;;;kEAElB,6LAAC;wDAAO,WAAU;kEAAwD;;;;;;;;;;;;;;;;;;mCAlBtE,OAAO,EAAE;;;;;;;;;;wBA0BtB,eAAe,MAAM,KAAK,mBACzB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,wOAAA,CAAA,sBAAmB;oCAAC,WAAU;;;;;;8CAC/B,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,6LAAC,kNAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrD;GAnUM;;QACa,kIAAA,CAAA,UAAO;;;KADpB;uCAqUS", "debugId": null}}, {"offset": {"line": 858, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/vo/digital-freight/frontend/node_modules/%40heroicons/react/24/outline/esm/MagnifyingGlassIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction MagnifyingGlassIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(MagnifyingGlassIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,oBAAoB,EAC3B,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 900, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/vo/digital-freight/frontend/node_modules/%40heroicons/react/24/outline/esm/ClockIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction ClockIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ClockIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,UAAU,EACjB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 942, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/vo/digital-freight/frontend/node_modules/%40heroicons/react/24/outline/esm/ChartBarIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction ChartBarIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ChartBarIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,aAAa,EACpB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 984, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/vo/digital-freight/frontend/node_modules/%40heroicons/react/24/outline/esm/PlusIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction PlusIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 4.5v15m7.5-7.5h-15\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(PlusIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,SAAS,EAChB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1026, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/vo/digital-freight/frontend/node_modules/%40heroicons/react/24/outline/esm/ArrowRightIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction ArrowRightIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ArrowRightIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,eAAe,EACtB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1068, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/vo/digital-freight/frontend/node_modules/%40heroicons/react/24/outline/esm/ExclamationTriangleIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction ExclamationTriangleIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ExclamationTriangleIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,wBAAwB,EAC/B,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1110, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/vo/digital-freight/frontend/node_modules/%40heroicons/react/24/outline/esm/CheckCircleIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction CheckCircleIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(CheckCircleIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,gBAAgB,EACvB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}]}