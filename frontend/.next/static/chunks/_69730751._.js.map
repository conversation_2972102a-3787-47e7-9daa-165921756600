{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/vo/digital-freight/frontend/src/components/auth/LoginForm.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline';\n\ninterface LoginFormProps {\n  onSuccess?: () => void;\n  onSwitchToRegister?: () => void;\n}\n\nconst LoginForm: React.FC<LoginFormProps> = ({ onSuccess, onSwitchToRegister }) => {\n  const { login, isLoading } = useAuth();\n  const [formData, setFormData] = useState({\n    email: '',\n    password: '',\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [error, setError] = useState('');\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value,\n    }));\n    // Clear error when user starts typing\n    if (error) setError('');\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setError('');\n\n    // Basic validation\n    if (!formData.email || !formData.password) {\n      setError('Please fill in all fields');\n      return;\n    }\n\n    if (!formData.email.includes('@')) {\n      setError('Please enter a valid email address');\n      return;\n    }\n\n    try {\n      await login(formData.email, formData.password);\n      onSuccess?.();\n    } catch (err: any) {\n      setError(err.message || 'Login failed');\n    }\n  };\n\n  return (\n    <div className=\"w-full max-w-md mx-auto\">\n      <div className=\"bg-white shadow-lg rounded-lg p-8\">\n        <div className=\"text-center mb-8\">\n          <h2 className=\"text-3xl font-bold text-gray-900\">Welcome Back</h2>\n          <p className=\"text-gray-600 mt-2\">Sign in to your Digital Freight account</p>\n        </div>\n\n        <form onSubmit={handleSubmit} className=\"space-y-6\">\n          {error && (\n            <div className=\"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md\">\n              {error}\n            </div>\n          )}\n\n          <div>\n            <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Email Address\n            </label>\n            <input\n              type=\"email\"\n              id=\"email\"\n              name=\"email\"\n              value={formData.email}\n              onChange={handleChange}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              placeholder=\"Enter your email\"\n              disabled={isLoading}\n              required\n            />\n          </div>\n\n          <div>\n            <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Password\n            </label>\n            <div className=\"relative\">\n              <input\n                type={showPassword ? 'text' : 'password'}\n                id=\"password\"\n                name=\"password\"\n                value={formData.password}\n                onChange={handleChange}\n                className=\"w-full px-3 py-2 pr-10 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                placeholder=\"Enter your password\"\n                disabled={isLoading}\n                required\n              />\n              <button\n                type=\"button\"\n                className=\"absolute inset-y-0 right-0 pr-3 flex items-center\"\n                onClick={() => setShowPassword(!showPassword)}\n                disabled={isLoading}\n              >\n                {showPassword ? (\n                  <EyeSlashIcon className=\"h-5 w-5 text-gray-400\" />\n                ) : (\n                  <EyeIcon className=\"h-5 w-5 text-gray-400\" />\n                )}\n              </button>\n            </div>\n          </div>\n\n          <button\n            type=\"submit\"\n            disabled={isLoading}\n            className=\"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            {isLoading ? (\n              <div className=\"flex items-center\">\n                <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                Signing in...\n              </div>\n            ) : (\n              'Sign In'\n            )}\n          </button>\n        </form>\n\n        <div className=\"mt-6 text-center\">\n          <p className=\"text-sm text-gray-600\">\n            Don't have an account?{' '}\n            <button\n              type=\"button\"\n              onClick={onSwitchToRegister}\n              className=\"font-medium text-blue-600 hover:text-blue-500 focus:outline-none focus:underline\"\n              disabled={isLoading}\n            >\n              Sign up here\n            </button>\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default LoginForm;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;;;AAJA;;;;AAWA,MAAM,YAAsC,CAAC,EAAE,SAAS,EAAE,kBAAkB,EAAE;;IAC5E,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,UAAU;IACZ;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;QACD,sCAAsC;QACtC,IAAI,OAAO,SAAS;IACtB;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,SAAS;QAET,mBAAmB;QACnB,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,QAAQ,EAAE;YACzC,SAAS;YACT;QACF;QAEA,IAAI,CAAC,SAAS,KAAK,CAAC,QAAQ,CAAC,MAAM;YACjC,SAAS;YACT;QACF;QAEA,IAAI;YACF,MAAM,MAAM,SAAS,KAAK,EAAE,SAAS,QAAQ;YAC7C;QACF,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO,IAAI;QAC1B;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAmC;;;;;;sCACjD,6LAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;;8BAGpC,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;wBACrC,uBACC,6LAAC;4BAAI,WAAU;sCACZ;;;;;;sCAIL,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAQ,WAAU;8CAA+C;;;;;;8CAGhF,6LAAC;oCACC,MAAK;oCACL,IAAG;oCACH,MAAK;oCACL,OAAO,SAAS,KAAK;oCACrB,UAAU;oCACV,WAAU;oCACV,aAAY;oCACZ,UAAU;oCACV,QAAQ;;;;;;;;;;;;sCAIZ,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAW,WAAU;8CAA+C;;;;;;8CAGnF,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAM,eAAe,SAAS;4CAC9B,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,QAAQ;4CACxB,UAAU;4CACV,WAAU;4CACV,aAAY;4CACZ,UAAU;4CACV,QAAQ;;;;;;sDAEV,6LAAC;4CACC,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,gBAAgB,CAAC;4CAChC,UAAU;sDAET,6BACC,6LAAC,0NAAA,CAAA,eAAY;gDAAC,WAAU;;;;;qEAExB,6LAAC,gNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sCAM3B,6LAAC;4BACC,MAAK;4BACL,UAAU;4BACV,WAAU;sCAET,0BACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;oCAAuE;;;;;;uCAIxF;;;;;;;;;;;;8BAKN,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;;4BAAwB;4BACZ;0CACvB,6LAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;gCACV,UAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GAzIM;;QACyB,kIAAA,CAAA,UAAO;;;KADhC;uCA2IS", "debugId": null}}, {"offset": {"line": 287, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/vo/digital-freight/frontend/src/components/auth/RegisterForm.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline';\n\ninterface RegisterFormProps {\n  onSuccess?: () => void;\n  onSwitchToLogin?: () => void;\n}\n\nconst RegisterForm: React.FC<RegisterFormProps> = ({ onSuccess, onSwitchToLogin }) => {\n  const { register, isLoading } = useAuth();\n  const [formData, setFormData] = useState({\n    firstName: '',\n    lastName: '',\n    email: '',\n    companyName: '',\n    password: '',\n    confirmPassword: '',\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\n  const [error, setError] = useState('');\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value,\n    }));\n    // Clear error when user starts typing\n    if (error) setError('');\n  };\n\n  const validateForm = () => {\n    if (!formData.firstName || !formData.lastName || !formData.email || !formData.password) {\n      return 'Please fill in all required fields';\n    }\n\n    if (!formData.email.includes('@')) {\n      return 'Please enter a valid email address';\n    }\n\n    if (formData.password.length < 8) {\n      return 'Password must be at least 8 characters long';\n    }\n\n    if (formData.password !== formData.confirmPassword) {\n      return 'Passwords do not match';\n    }\n\n    return null;\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setError('');\n\n    const validationError = validateForm();\n    if (validationError) {\n      setError(validationError);\n      return;\n    }\n\n    try {\n      await register({\n        firstName: formData.firstName,\n        lastName: formData.lastName,\n        email: formData.email,\n        password: formData.password,\n        companyName: formData.companyName || undefined,\n      });\n      onSuccess?.();\n    } catch (err: any) {\n      setError(err.message || 'Registration failed');\n    }\n  };\n\n  return (\n    <div className=\"w-full max-w-md mx-auto\">\n      <div className=\"bg-white shadow-lg rounded-lg p-8\">\n        <div className=\"text-center mb-8\">\n          <h2 className=\"text-3xl font-bold text-gray-900\">Create Account</h2>\n          <p className=\"text-gray-600 mt-2\">Join Digital Freight Platform</p>\n        </div>\n\n        <form onSubmit={handleSubmit} className=\"space-y-6\">\n          {error && (\n            <div className=\"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md\">\n              {error}\n            </div>\n          )}\n\n          <div className=\"grid grid-cols-2 gap-4\">\n            <div>\n              <label htmlFor=\"firstName\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                First Name *\n              </label>\n              <input\n                type=\"text\"\n                id=\"firstName\"\n                name=\"firstName\"\n                value={formData.firstName}\n                onChange={handleChange}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                placeholder=\"John\"\n                disabled={isLoading}\n                required\n              />\n            </div>\n\n            <div>\n              <label htmlFor=\"lastName\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Last Name *\n              </label>\n              <input\n                type=\"text\"\n                id=\"lastName\"\n                name=\"lastName\"\n                value={formData.lastName}\n                onChange={handleChange}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                placeholder=\"Doe\"\n                disabled={isLoading}\n                required\n              />\n            </div>\n          </div>\n\n          <div>\n            <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Email Address *\n            </label>\n            <input\n              type=\"email\"\n              id=\"email\"\n              name=\"email\"\n              value={formData.email}\n              onChange={handleChange}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              placeholder=\"<EMAIL>\"\n              disabled={isLoading}\n              required\n            />\n          </div>\n\n          <div>\n            <label htmlFor=\"companyName\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Company Name\n            </label>\n            <input\n              type=\"text\"\n              id=\"companyName\"\n              name=\"companyName\"\n              value={formData.companyName}\n              onChange={handleChange}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              placeholder=\"Your freight company\"\n              disabled={isLoading}\n            />\n          </div>\n\n          <div>\n            <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Password *\n            </label>\n            <div className=\"relative\">\n              <input\n                type={showPassword ? 'text' : 'password'}\n                id=\"password\"\n                name=\"password\"\n                value={formData.password}\n                onChange={handleChange}\n                className=\"w-full px-3 py-2 pr-10 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                placeholder=\"At least 8 characters\"\n                disabled={isLoading}\n                required\n              />\n              <button\n                type=\"button\"\n                className=\"absolute inset-y-0 right-0 pr-3 flex items-center\"\n                onClick={() => setShowPassword(!showPassword)}\n                disabled={isLoading}\n              >\n                {showPassword ? (\n                  <EyeSlashIcon className=\"h-5 w-5 text-gray-400\" />\n                ) : (\n                  <EyeIcon className=\"h-5 w-5 text-gray-400\" />\n                )}\n              </button>\n            </div>\n          </div>\n\n          <div>\n            <label htmlFor=\"confirmPassword\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Confirm Password *\n            </label>\n            <div className=\"relative\">\n              <input\n                type={showConfirmPassword ? 'text' : 'password'}\n                id=\"confirmPassword\"\n                name=\"confirmPassword\"\n                value={formData.confirmPassword}\n                onChange={handleChange}\n                className=\"w-full px-3 py-2 pr-10 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                placeholder=\"Confirm your password\"\n                disabled={isLoading}\n                required\n              />\n              <button\n                type=\"button\"\n                className=\"absolute inset-y-0 right-0 pr-3 flex items-center\"\n                onClick={() => setShowConfirmPassword(!showConfirmPassword)}\n                disabled={isLoading}\n              >\n                {showConfirmPassword ? (\n                  <EyeSlashIcon className=\"h-5 w-5 text-gray-400\" />\n                ) : (\n                  <EyeIcon className=\"h-5 w-5 text-gray-400\" />\n                )}\n              </button>\n            </div>\n          </div>\n\n          <button\n            type=\"submit\"\n            disabled={isLoading}\n            className=\"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            {isLoading ? (\n              <div className=\"flex items-center\">\n                <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                Creating account...\n              </div>\n            ) : (\n              'Create Account'\n            )}\n          </button>\n        </form>\n\n        <div className=\"mt-6 text-center\">\n          <p className=\"text-sm text-gray-600\">\n            Already have an account?{' '}\n            <button\n              type=\"button\"\n              onClick={onSwitchToLogin}\n              className=\"font-medium text-blue-600 hover:text-blue-500 focus:outline-none focus:underline\"\n              disabled={isLoading}\n            >\n              Sign in here\n            </button>\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default RegisterForm;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;;;AAJA;;;;AAWA,MAAM,eAA4C,CAAC,EAAE,SAAS,EAAE,eAAe,EAAE;;IAC/E,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACtC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,WAAW;QACX,UAAU;QACV,OAAO;QACP,aAAa;QACb,UAAU;QACV,iBAAiB;IACnB;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;QACD,sCAAsC;QACtC,IAAI,OAAO,SAAS;IACtB;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,SAAS,SAAS,IAAI,CAAC,SAAS,QAAQ,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,QAAQ,EAAE;YACtF,OAAO;QACT;QAEA,IAAI,CAAC,SAAS,KAAK,CAAC,QAAQ,CAAC,MAAM;YACjC,OAAO;QACT;QAEA,IAAI,SAAS,QAAQ,CAAC,MAAM,GAAG,GAAG;YAChC,OAAO;QACT;QAEA,IAAI,SAAS,QAAQ,KAAK,SAAS,eAAe,EAAE;YAClD,OAAO;QACT;QAEA,OAAO;IACT;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,SAAS;QAET,MAAM,kBAAkB;QACxB,IAAI,iBAAiB;YACnB,SAAS;YACT;QACF;QAEA,IAAI;YACF,MAAM,SAAS;gBACb,WAAW,SAAS,SAAS;gBAC7B,UAAU,SAAS,QAAQ;gBAC3B,OAAO,SAAS,KAAK;gBACrB,UAAU,SAAS,QAAQ;gBAC3B,aAAa,SAAS,WAAW,IAAI;YACvC;YACA;QACF,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO,IAAI;QAC1B;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAmC;;;;;;sCACjD,6LAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;;8BAGpC,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;wBACrC,uBACC,6LAAC;4BAAI,WAAU;sCACZ;;;;;;sCAIL,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAY,WAAU;sDAA+C;;;;;;sDAGpF,6LAAC;4CACC,MAAK;4CACL,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,SAAS;4CACzB,UAAU;4CACV,WAAU;4CACV,aAAY;4CACZ,UAAU;4CACV,QAAQ;;;;;;;;;;;;8CAIZ,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAA+C;;;;;;sDAGnF,6LAAC;4CACC,MAAK;4CACL,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,QAAQ;4CACxB,UAAU;4CACV,WAAU;4CACV,aAAY;4CACZ,UAAU;4CACV,QAAQ;;;;;;;;;;;;;;;;;;sCAKd,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAQ,WAAU;8CAA+C;;;;;;8CAGhF,6LAAC;oCACC,MAAK;oCACL,IAAG;oCACH,MAAK;oCACL,OAAO,SAAS,KAAK;oCACrB,UAAU;oCACV,WAAU;oCACV,aAAY;oCACZ,UAAU;oCACV,QAAQ;;;;;;;;;;;;sCAIZ,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAc,WAAU;8CAA+C;;;;;;8CAGtF,6LAAC;oCACC,MAAK;oCACL,IAAG;oCACH,MAAK;oCACL,OAAO,SAAS,WAAW;oCAC3B,UAAU;oCACV,WAAU;oCACV,aAAY;oCACZ,UAAU;;;;;;;;;;;;sCAId,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAW,WAAU;8CAA+C;;;;;;8CAGnF,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAM,eAAe,SAAS;4CAC9B,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,QAAQ;4CACxB,UAAU;4CACV,WAAU;4CACV,aAAY;4CACZ,UAAU;4CACV,QAAQ;;;;;;sDAEV,6LAAC;4CACC,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,gBAAgB,CAAC;4CAChC,UAAU;sDAET,6BACC,6LAAC,0NAAA,CAAA,eAAY;gDAAC,WAAU;;;;;qEAExB,6LAAC,gNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sCAM3B,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAkB,WAAU;8CAA+C;;;;;;8CAG1F,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAM,sBAAsB,SAAS;4CACrC,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,eAAe;4CAC/B,UAAU;4CACV,WAAU;4CACV,aAAY;4CACZ,UAAU;4CACV,QAAQ;;;;;;sDAEV,6LAAC;4CACC,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,uBAAuB,CAAC;4CACvC,UAAU;sDAET,oCACC,6LAAC,0NAAA,CAAA,eAAY;gDAAC,WAAU;;;;;qEAExB,6LAAC,gNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sCAM3B,6LAAC;4BACC,MAAK;4BACL,UAAU;4BACV,WAAU;sCAET,0BACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;oCAAuE;;;;;;uCAIxF;;;;;;;;;;;;8BAKN,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;;4BAAwB;4BACV;0CACzB,6LAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;gCACV,UAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GAtPM;;QAC4B,kIAAA,CAAA,UAAO;;;KADnC;uCAwPS", "debugId": null}}, {"offset": {"line": 757, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/vo/digital-freight/frontend/src/app/auth/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useRouter, useSearchParams } from 'next/navigation';\nimport { useAuth } from '@/contexts/AuthContext';\nimport LoginForm from '@/components/auth/LoginForm';\nimport RegisterForm from '@/components/auth/RegisterForm';\n\nconst AuthPage: React.FC = () => {\n  const router = useRouter();\n  const searchParams = useSearchParams();\n  const { isAuthenticated } = useAuth();\n  \n  const [mode, setMode] = useState<'login' | 'register'>('login');\n\n  // Check URL params for mode\n  useEffect(() => {\n    const modeParam = searchParams.get('mode');\n    if (modeParam === 'register') {\n      setMode('register');\n    }\n  }, [searchParams]);\n\n  // Redirect if already authenticated\n  useEffect(() => {\n    if (isAuthenticated) {\n      router.push('/dashboard');\n    }\n  }, [isAuthenticated, router]);\n\n  const handleAuthSuccess = () => {\n    router.push('/dashboard');\n  };\n\n  const switchToLogin = () => {\n    setMode('login');\n    router.replace('/auth');\n  };\n\n  const switchToRegister = () => {\n    setMode('register');\n    router.replace('/auth?mode=register');\n  };\n\n  if (isAuthenticated) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-md w-full space-y-8\">\n        {mode === 'login' ? (\n          <LoginForm\n            onSuccess={handleAuthSuccess}\n            onSwitchToRegister={switchToRegister}\n          />\n        ) : (\n          <RegisterForm\n            onSuccess={handleAuthSuccess}\n            onSwitchToLogin={switchToLogin}\n          />\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default AuthPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAQA,MAAM,WAAqB;;IACzB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAElC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IAEvD,4BAA4B;IAC5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,MAAM,YAAY,aAAa,GAAG,CAAC;YACnC,IAAI,cAAc,YAAY;gBAC5B,QAAQ;YACV;QACF;6BAAG;QAAC;KAAa;IAEjB,oCAAoC;IACpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,IAAI,iBAAiB;gBACnB,OAAO,IAAI,CAAC;YACd;QACF;6BAAG;QAAC;QAAiB;KAAO;IAE5B,MAAM,oBAAoB;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,gBAAgB;QACpB,QAAQ;QACR,OAAO,OAAO,CAAC;IACjB;IAEA,MAAM,mBAAmB;QACvB,QAAQ;QACR,OAAO,OAAO,CAAC;IACjB;IAEA,IAAI,iBAAiB;QACnB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACZ,SAAS,wBACR,6LAAC,0IAAA,CAAA,UAAS;gBACR,WAAW;gBACX,oBAAoB;;;;;qCAGtB,6LAAC,6IAAA,CAAA,UAAY;gBACX,WAAW;gBACX,iBAAiB;;;;;;;;;;;;;;;;AAM7B;GA7DM;;QACW,qIAAA,CAAA,YAAS;QACH,qIAAA,CAAA,kBAAe;QACR,kIAAA,CAAA,UAAO;;;KAH/B;uCA+DS", "debugId": null}}, {"offset": {"line": 878, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/vo/digital-freight/frontend/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 885, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/vo/digital-freight/frontend/node_modules/%40heroicons/react/24/outline/esm/EyeIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction EyeIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(EyeIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,QAAQ,EACf,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL,IAAI,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QAC3C,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 931, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/vo/digital-freight/frontend/node_modules/%40heroicons/react/24/outline/esm/EyeSlashIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction EyeSlashIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(EyeSlashIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,aAAa,EACpB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}]}