(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[105],{2511:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>x});var t=a(5155),r=a(2115),l=a(6874),n=a.n(l),i=a(3464),d=a(8046),c=a(3930),o=a(2771);let h=r.forwardRef(function(e,s){let{title:a,titleId:t,...l}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},l),a?r.createElement("title",{id:t},a):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z"}))}),m=a(9509).env.NEXT_PUBLIC_API_URL||"http://localhost:5001",x=()=>{var e,s,a,l,x;let[g,u]=(0,r.useState)(!0),[v,p]=(0,r.useState)(null),[j,f]=(0,r.useState)(null);(0,r.useEffect)(()=>{N()},[]);let N=async()=>{try{u(!0),p(null);let e=await i.A.get("".concat(m,"/api/search/test-carriers"));f(e.data.data.carriers)}catch(e){console.error("Failed to fetch dashboard data:",e),p("Failed to load dashboard data")}finally{u(!1)}},w=[{name:"Total Searches",value:"24",icon:d.A,change:"+12%",changeType:"increase"},{name:"Active Carriers",value:(null==j||null==(e=j.maersk)?void 0:e.status)==="connected"?"1":"0",icon:c.A,change:"Maersk API",changeType:"neutral"},{name:"Avg Response Time",value:"650ms",icon:o.A,change:"Real-time",changeType:"neutral"},{name:"System Status",value:(null==j||null==(s=j.maersk)?void 0:s.status)==="connected"?"Healthy":"Offline",icon:h,change:(null==j||null==(a=j.maersk)?void 0:a.status)==="connected"?"Online":"Check API",changeType:(null==j||null==(l=j.maersk)?void 0:l.status)==="connected"?"increase":"decrease"}];return(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Digital Freight Platform Dashboard"}),(0,t.jsx)("p",{className:"text-gray-600 mt-2",children:"Real-time shipping route search and carrier API monitoring."})]}),v&&(0,t.jsx)("div",{className:"mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md",children:v}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:g?Array.from({length:4}).map((e,s)=>(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 animate-pulse",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("div",{className:"h-8 w-8 bg-gray-300 rounded"})}),(0,t.jsxs)("div",{className:"ml-4 flex-1",children:[(0,t.jsx)("div",{className:"h-4 bg-gray-300 rounded w-3/4 mb-2"}),(0,t.jsx)("div",{className:"h-6 bg-gray-300 rounded w-1/2"})]})]}),(0,t.jsx)("div",{className:"mt-4",children:(0,t.jsx)("div",{className:"h-3 bg-gray-300 rounded w-1/3"})})]},s)):w.map(e=>(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)(e.icon,{className:"h-8 w-8 text-gray-600"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:e.name}),(0,t.jsx)("p",{className:"text-2xl font-semibold text-gray-900",children:e.value})]})]}),(0,t.jsx)("div",{className:"mt-4",children:(0,t.jsx)("span",{className:"text-sm font-medium ".concat("increase"===e.changeType?"text-green-600":"decrease"===e.changeType?"text-red-600":"text-gray-600"),children:e.change})})]},e.name))}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,t.jsx)(n(),{href:"/search",className:"bg-white rounded-lg shadow p-6 hover:shadow-md transition-shadow",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)(d.A,{className:"h-8 w-8 text-blue-600"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Search Routes"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Find shipping schedules"})]})]})}),(0,t.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)(c.A,{className:"h-8 w-8 text-green-600"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Carrier Status"}),(0,t.jsxs)("p",{className:"text-gray-600",children:["Maersk API: ",(null==j||null==(x=j.maersk)?void 0:x.status)==="connected"?"Connected":"Offline"]})]})]})}),(0,t.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)(o.A,{className:"h-8 w-8 text-purple-600"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Real-time Data"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Live shipping schedules"})]})]})})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,t.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,t.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Get Started"})}),(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:"Welcome to the Digital Freight Platform. Start by searching for shipping routes between any two ports worldwide."}),(0,t.jsxs)(n(),{href:"/search",className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:[(0,t.jsx)(d.A,{className:"h-4 w-4 mr-2"}),"Start Searching"]})]})]})]})}},2771:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});var t=a(2115);let r=t.forwardRef(function(e,s){let{title:a,titleId:r,...l}=e;return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":r},l),a?t.createElement("title",{id:r},a):null,t.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},3930:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});var t=a(2115);let r=t.forwardRef(function(e,s){let{title:a,titleId:r,...l}=e;return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":r},l),a?t.createElement("title",{id:r},a):null,t.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8.25 18.75a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h6m-9 0H3.375a1.125 1.125 0 0 1-1.125-1.125V14.25m17.25 4.5a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h1.125c.621 0 1.129-.504 1.09-1.124a17.902 17.902 0 0 0-3.213-9.193 2.056 2.056 0 0 0-1.58-.86H14.25M16.5 18.75h-2.25m0-11.177v-.958c0-.568-.422-1.048-.987-1.106a48.554 48.554 0 0 0-10.026 0 1.106 1.106 0 0 0-.987 1.106v7.635m12-6.677v6.677m0 4.5v-4.5m0 0h-12"}))})},4743:(e,s,a)=>{Promise.resolve().then(a.bind(a,2511))},8046:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});var t=a(2115);let r=t.forwardRef(function(e,s){let{title:a,titleId:r,...l}=e;return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":r},l),a?t.createElement("title",{id:r},a):null,t.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"}))})}},e=>{var s=s=>e(e.s=s);e.O(0,[464,874,441,684,358],()=>s(4743)),_N_E=e.O()}]);