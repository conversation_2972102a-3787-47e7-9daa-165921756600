(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{283:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>d,h:()=>h});var a=r(5155),s=r(2115),l=r(3464),o=r(9509);let n=(0,s.createContext)(void 0),i=o.env.NEXT_PUBLIC_API_URL||"http://localhost:5001",c=l.A.create({baseURL:i,headers:{"Content-Type":"application/json"}}),d=e=>{let{children:t}=e,[r,l]=(0,s.useState)(null),[o,i]=(0,s.useState)(null),[d,h]=(0,s.useState)(!0);(0,s.useEffect)(()=>{let e=c.interceptors.request.use(e=>(o&&(e.headers.Authorization="Bearer ".concat(o)),e));return()=>c.interceptors.request.eject(e)},[o]),(0,s.useEffect)(()=>{(async()=>{try{let e=localStorage.getItem("auth_token"),t=localStorage.getItem("auth_user");if(e&&t){i(e),l(JSON.parse(t));try{let t=await c.get("/api/auth/profile",{headers:{Authorization:"Bearer ".concat(e)}});l(t.data.data.user)}catch(e){localStorage.removeItem("auth_token"),localStorage.removeItem("auth_user"),i(null),l(null)}}}catch(e){console.error("Error loading user:",e)}finally{h(!1)}})()},[]);let m=async(e,t)=>{try{h(!0);let{user:r,token:a}=(await c.post("/api/auth/login",{email:e,password:t})).data.data;l(r),i(a),localStorage.setItem("auth_token",a),localStorage.setItem("auth_user",JSON.stringify(r))}catch(e){var r,a;throw Error((null==(a=e.response)||null==(r=a.data)?void 0:r.message)||"Login failed")}finally{h(!1)}},u=async e=>{try{h(!0);let{user:t,token:r}=(await c.post("/api/auth/register",e)).data.data;l(t),i(r),localStorage.setItem("auth_token",r),localStorage.setItem("auth_user",JSON.stringify(t))}catch(e){var t,r;throw Error((null==(r=e.response)||null==(t=r.data)?void 0:t.message)||"Registration failed")}finally{h(!1)}},x=async e=>{try{h(!0);let t=(await c.put("/api/auth/profile",e)).data.data.user;l(t),localStorage.setItem("auth_user",JSON.stringify(t))}catch(e){var t,r;throw Error((null==(r=e.response)||null==(t=r.data)?void 0:t.message)||"Profile update failed")}finally{h(!1)}};return(0,a.jsx)(n.Provider,{value:{user:r,token:o,isLoading:d,isAuthenticated:!!r&&!!o,login:m,register:u,logout:()=>{l(null),i(null),localStorage.removeItem("auth_token"),localStorage.removeItem("auth_user"),o&&c.post("/api/auth/logout").catch(()=>{})},updateProfile:x},children:t})},h=()=>{let e=(0,s.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},347:()=>{},3930:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(2115);let s=a.forwardRef(function(e,t){let{title:r,titleId:s,...l}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},l),r?a.createElement("title",{id:s},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8.25 18.75a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h6m-9 0H3.375a1.125 1.125 0 0 1-1.125-1.125V14.25m17.25 4.5a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h1.125c.621 0 1.129-.504 1.09-1.124a17.902 17.902 0 0 0-3.213-9.193 2.056 2.056 0 0 0-1.58-.86H14.25M16.5 18.75h-2.25m0-11.177v-.958c0-.568-.422-1.048-.987-1.106a48.554 48.554 0 0 0-10.026 0 1.106 1.106 0 0 0-.987 1.106v7.635m12-6.677v6.677m0 4.5v-4.5m0 0h-12"}))})},4500:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(2115);let s=a.forwardRef(function(e,t){let{title:r,titleId:s,...l}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},l),r?a.createElement("title",{id:s},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18 18 6M6 6l12 12"}))})},6284:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,8346,23)),Promise.resolve().then(r.t.bind(r,347,23)),Promise.resolve().then(r.bind(r,7817)),Promise.resolve().then(r.bind(r,283))},7817:(e,t,r)=>{"use strict";r.d(t,{default:()=>x});var a=r(5155),s=r(2115),l=r(6874),o=r.n(l),n=r(283),i=r(3930);let c=s.forwardRef(function(e,t){let{title:r,titleId:a,...l}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},l),r?s.createElement("title",{id:a},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"}))}),d=s.forwardRef(function(e,t){let{title:r,titleId:a,...l}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},l),r?s.createElement("title",{id:a},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a7.723 7.723 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.47 6.47 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z"}),s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))}),h=s.forwardRef(function(e,t){let{title:r,titleId:a,...l}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},l),r?s.createElement("title",{id:a},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 9V5.25A2.25 2.25 0 0 0 13.5 3h-6a2.25 2.25 0 0 0-2.25 2.25v13.5A2.25 2.25 0 0 0 7.5 21h6a2.25 2.25 0 0 0 2.25-2.25V15m3 0 3-3m0 0-3-3m3 3H9"}))});var m=r(4500);let u=s.forwardRef(function(e,t){let{title:r,titleId:a,...l}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},l),r?s.createElement("title",{id:a},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"}))}),x=()=>{let{user:e,isAuthenticated:t,logout:r}=(0,n.h)(),[l,x]=(0,s.useState)(!1),[g,f]=(0,s.useState)(!1),p=()=>{r(),f(!1)};return(0,a.jsxs)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:[(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsxs)(o(),{href:"/",className:"flex items-center space-x-2",children:[(0,a.jsx)(i.A,{className:"h-8 w-8 text-blue-600"}),(0,a.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"Digital Freight"})]})}),(0,a.jsxs)("nav",{className:"hidden md:flex items-center space-x-8",children:[(0,a.jsx)(o(),{href:"/search",className:"text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors",children:"Search Routes"}),t&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o(),{href:"/dashboard",className:"text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors",children:"Dashboard"}),(0,a.jsx)(o(),{href:"/history",className:"text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors",children:"Search History"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[t&&e?(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("button",{onClick:()=>f(!g),className:"flex items-center space-x-2 text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors",children:[(0,a.jsx)(c,{className:"h-5 w-5"}),(0,a.jsx)("span",{className:"hidden sm:block",children:e.firstName})]}),g&&(0,a.jsxs)("div",{className:"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200",children:[(0,a.jsxs)("div",{className:"px-4 py-2 text-sm text-gray-500 border-b border-gray-100",children:[(0,a.jsxs)("div",{className:"font-medium text-gray-900",children:[e.firstName," ",e.lastName]}),(0,a.jsx)("div",{className:"truncate",children:e.email}),e.companyName&&(0,a.jsx)("div",{className:"text-xs text-gray-400",children:e.companyName})]}),(0,a.jsxs)(o(),{href:"/profile",className:"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",onClick:()=>f(!1),children:[(0,a.jsx)(d,{className:"h-4 w-4 mr-2"}),"Profile Settings"]}),(0,a.jsxs)("button",{onClick:p,className:"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[(0,a.jsx)(h,{className:"h-4 w-4 mr-2"}),"Sign Out"]})]})]}):(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(o(),{href:"/auth",className:"text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors",children:"Sign In"}),(0,a.jsx)(o(),{href:"/auth?mode=register",className:"bg-blue-600 text-white hover:bg-blue-700 px-4 py-2 rounded-md text-sm font-medium transition-colors",children:"Get Started"})]}),(0,a.jsx)("button",{onClick:()=>x(!l),className:"md:hidden p-2 rounded-md text-gray-700 hover:text-blue-600 hover:bg-gray-100",children:l?(0,a.jsx)(m.A,{className:"h-6 w-6"}):(0,a.jsx)(u,{className:"h-6 w-6"})})]})]}),l&&(0,a.jsx)("div",{className:"md:hidden border-t border-gray-200 py-4",children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(o(),{href:"/search",className:"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md",onClick:()=>x(!1),children:"Search Routes"}),t&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o(),{href:"/dashboard",className:"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md",onClick:()=>x(!1),children:"Dashboard"}),(0,a.jsx)(o(),{href:"/history",className:"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md",onClick:()=>x(!1),children:"Search History"}),(0,a.jsx)(o(),{href:"/profile",className:"block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md",onClick:()=>x(!1),children:"Profile Settings"}),(0,a.jsx)("button",{onClick:()=>{p(),x(!1)},className:"block w-full text-left px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md",children:"Sign Out"})]})]})})]}),g&&(0,a.jsx)("div",{className:"fixed inset-0 z-40",onClick:()=>f(!1)})]})}},8346:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c"}}},e=>{var t=t=>e(e.s=t);e.O(0,[838,464,874,441,684,358],()=>t(6284)),_N_E=e.O()}]);