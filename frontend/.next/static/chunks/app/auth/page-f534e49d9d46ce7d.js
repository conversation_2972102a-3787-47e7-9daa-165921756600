(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[365],{184:(e,a,s)=>{"use strict";s.d(a,{A:()=>r});var t=s(2115);let r=t.forwardRef(function(e,a){let{title:s,titleId:r,...l}=e;return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},l),s?t.createElement("title",{id:r},s):null,t.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 ************.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),t.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))})},283:(e,a,s)=>{"use strict";s.d(a,{AuthProvider:()=>c,h:()=>u});var t=s(5155),r=s(2115),l=s(3464),o=s(9509);let n=(0,r.createContext)(void 0),i=o.env.NEXT_PUBLIC_API_URL||"http://localhost:5001",d=l.A.create({baseURL:i,headers:{"Content-Type":"application/json"}}),c=e=>{let{children:a}=e,[s,l]=(0,r.useState)(null),[o,i]=(0,r.useState)(null),[c,u]=(0,r.useState)(!0);(0,r.useEffect)(()=>{let e=d.interceptors.request.use(e=>(o&&(e.headers.Authorization="Bearer ".concat(o)),e));return()=>d.interceptors.request.eject(e)},[o]),(0,r.useEffect)(()=>{(async()=>{try{let e=localStorage.getItem("auth_token"),a=localStorage.getItem("auth_user");if(e&&a){i(e),l(JSON.parse(a));try{let a=await d.get("/api/auth/profile",{headers:{Authorization:"Bearer ".concat(e)}});l(a.data.data.user)}catch(e){localStorage.removeItem("auth_token"),localStorage.removeItem("auth_user"),i(null),l(null)}}}catch(e){console.error("Error loading user:",e)}finally{u(!1)}})()},[]);let m=async(e,a)=>{try{u(!0);let{user:s,token:t}=(await d.post("/api/auth/login",{email:e,password:a})).data.data;l(s),i(t),localStorage.setItem("auth_token",t),localStorage.setItem("auth_user",JSON.stringify(s))}catch(e){var s,t;throw Error((null==(t=e.response)||null==(s=t.data)?void 0:s.message)||"Login failed")}finally{u(!1)}},h=async e=>{try{u(!0);let{user:a,token:s}=(await d.post("/api/auth/register",e)).data.data;l(a),i(s),localStorage.setItem("auth_token",s),localStorage.setItem("auth_user",JSON.stringify(a))}catch(e){var a,s;throw Error((null==(s=e.response)||null==(a=s.data)?void 0:a.message)||"Registration failed")}finally{u(!1)}},x=async e=>{try{u(!0);let a=(await d.put("/api/auth/profile",e)).data.data.user;l(a),localStorage.setItem("auth_user",JSON.stringify(a))}catch(e){var a,s;throw Error((null==(s=e.response)||null==(a=s.data)?void 0:a.message)||"Profile update failed")}finally{u(!1)}};return(0,t.jsx)(n.Provider,{value:{user:s,token:o,isLoading:c,isAuthenticated:!!s&&!!o,login:m,register:h,logout:()=>{l(null),i(null),localStorage.removeItem("auth_token"),localStorage.removeItem("auth_user"),o&&d.post("/api/auth/logout").catch(()=>{})},updateProfile:x},children:a})},u=()=>{let e=(0,r.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},6447:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>m});var t=s(5155),r=s(2115),l=s(8999),o=s(283);let n=r.forwardRef(function(e,a){let{title:s,titleId:t,...l}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},l),s?r.createElement("title",{id:t},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88"}))});var i=s(184);let d=e=>{let{onSuccess:a,onSwitchToRegister:s}=e,{login:l,isLoading:d}=(0,o.h)(),[c,u]=(0,r.useState)({email:"",password:""}),[m,h]=(0,r.useState)(!1),[x,f]=(0,r.useState)(""),b=e=>{let{name:a,value:s}=e.target;u(e=>({...e,[a]:s})),x&&f("")},p=async e=>{if(e.preventDefault(),f(""),!c.email||!c.password)return void f("Please fill in all fields");if(!c.email.includes("@"))return void f("Please enter a valid email address");try{await l(c.email,c.password),null==a||a()}catch(e){f(e.message||"Login failed")}};return(0,t.jsx)("div",{className:"w-full max-w-md mx-auto",children:(0,t.jsxs)("div",{className:"bg-white shadow-lg rounded-lg p-8",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("h2",{className:"text-3xl font-bold text-gray-900",children:"Welcome Back"}),(0,t.jsx)("p",{className:"text-gray-600 mt-2",children:"Sign in to your Digital Freight account"})]}),(0,t.jsxs)("form",{onSubmit:p,className:"space-y-6",children:[x&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md",children:x}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address"}),(0,t.jsx)("input",{type:"email",id:"email",name:"email",value:c.email,onChange:b,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter your email",disabled:d,required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-2",children:"Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{type:m?"text":"password",id:"password",name:"password",value:c.password,onChange:b,className:"w-full px-3 py-2 pr-10 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter your password",disabled:d,required:!0}),(0,t.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>h(!m),disabled:d,children:m?(0,t.jsx)(n,{className:"h-5 w-5 text-gray-400"}):(0,t.jsx)(i.A,{className:"h-5 w-5 text-gray-400"})})]})]}),(0,t.jsx)("button",{type:"submit",disabled:d,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:d?(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Signing in..."]}):"Sign In"})]}),(0,t.jsx)("div",{className:"mt-6 text-center",children:(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["Don't have an account?"," ",(0,t.jsx)("button",{type:"button",onClick:s,className:"font-medium text-blue-600 hover:text-blue-500 focus:outline-none focus:underline",disabled:d,children:"Sign up here"})]})})]})})},c=e=>{let{onSuccess:a,onSwitchToLogin:s}=e,{register:l,isLoading:d}=(0,o.h)(),[c,u]=(0,r.useState)({firstName:"",lastName:"",email:"",companyName:"",password:"",confirmPassword:""}),[m,h]=(0,r.useState)(!1),[x,f]=(0,r.useState)(!1),[b,p]=(0,r.useState)(""),g=e=>{let{name:a,value:s}=e.target;u(e=>({...e,[a]:s})),b&&p("")},y=()=>c.firstName&&c.lastName&&c.email&&c.password?c.email.includes("@")?c.password.length<8?"Password must be at least 8 characters long":c.password!==c.confirmPassword?"Passwords do not match":null:"Please enter a valid email address":"Please fill in all required fields",w=async e=>{e.preventDefault(),p("");let s=y();if(s)return void p(s);try{await l({firstName:c.firstName,lastName:c.lastName,email:c.email,password:c.password,companyName:c.companyName||void 0}),null==a||a()}catch(e){p(e.message||"Registration failed")}};return(0,t.jsx)("div",{className:"w-full max-w-md mx-auto",children:(0,t.jsxs)("div",{className:"bg-white shadow-lg rounded-lg p-8",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("h2",{className:"text-3xl font-bold text-gray-900",children:"Create Account"}),(0,t.jsx)("p",{className:"text-gray-600 mt-2",children:"Join Digital Freight Platform"})]}),(0,t.jsxs)("form",{onSubmit:w,className:"space-y-6",children:[b&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md",children:b}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"firstName",className:"block text-sm font-medium text-gray-700 mb-2",children:"First Name *"}),(0,t.jsx)("input",{type:"text",id:"firstName",name:"firstName",value:c.firstName,onChange:g,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"John",disabled:d,required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"lastName",className:"block text-sm font-medium text-gray-700 mb-2",children:"Last Name *"}),(0,t.jsx)("input",{type:"text",id:"lastName",name:"lastName",value:c.lastName,onChange:g,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Doe",disabled:d,required:!0})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address *"}),(0,t.jsx)("input",{type:"email",id:"email",name:"email",value:c.email,onChange:g,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"<EMAIL>",disabled:d,required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"companyName",className:"block text-sm font-medium text-gray-700 mb-2",children:"Company Name"}),(0,t.jsx)("input",{type:"text",id:"companyName",name:"companyName",value:c.companyName,onChange:g,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Your freight company",disabled:d})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-2",children:"Password *"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{type:m?"text":"password",id:"password",name:"password",value:c.password,onChange:g,className:"w-full px-3 py-2 pr-10 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"At least 8 characters",disabled:d,required:!0}),(0,t.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>h(!m),disabled:d,children:m?(0,t.jsx)(n,{className:"h-5 w-5 text-gray-400"}):(0,t.jsx)(i.A,{className:"h-5 w-5 text-gray-400"})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 mb-2",children:"Confirm Password *"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{type:x?"text":"password",id:"confirmPassword",name:"confirmPassword",value:c.confirmPassword,onChange:g,className:"w-full px-3 py-2 pr-10 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Confirm your password",disabled:d,required:!0}),(0,t.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>f(!x),disabled:d,children:x?(0,t.jsx)(n,{className:"h-5 w-5 text-gray-400"}):(0,t.jsx)(i.A,{className:"h-5 w-5 text-gray-400"})})]})]}),(0,t.jsx)("button",{type:"submit",disabled:d,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:d?(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Creating account..."]}):"Create Account"})]}),(0,t.jsx)("div",{className:"mt-6 text-center",children:(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["Already have an account?"," ",(0,t.jsx)("button",{type:"button",onClick:s,className:"font-medium text-blue-600 hover:text-blue-500 focus:outline-none focus:underline",disabled:d,children:"Sign in here"})]})})]})})},u=()=>{let e=(0,l.useRouter)(),a=(0,l.useSearchParams)(),{isAuthenticated:s}=(0,o.h)(),[n,i]=(0,r.useState)("login");(0,r.useEffect)(()=>{"register"===a.get("mode")&&i("register")},[a]),(0,r.useEffect)(()=>{s&&e.push("/dashboard")},[s,e]);let u=()=>{e.push("/dashboard")};return s?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,t.jsx)("div",{className:"max-w-md w-full space-y-8",children:"login"===n?(0,t.jsx)(d,{onSuccess:u,onSwitchToRegister:()=>{i("register"),e.replace("/auth?mode=register")}}):(0,t.jsx)(c,{onSuccess:u,onSwitchToLogin:()=>{i("login"),e.replace("/auth")}})})})},m=()=>(0,t.jsx)(r.Suspense,{fallback:(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:"Loading..."}),children:(0,t.jsx)(u,{})})},8759:(e,a,s)=>{Promise.resolve().then(s.bind(s,6447))}},e=>{var a=a=>e(e.s=a);e.O(0,[464,441,684,358],()=>a(8759)),_N_E=e.O()}]);