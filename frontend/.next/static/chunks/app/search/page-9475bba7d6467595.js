(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[959],{1137:(e,t,s)=>{Promise.resolve().then(s.bind(s,3191))},3191:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>y});var a=s(5155),r=s(2115),i=s(3464),l=s(928),n=s(2227),d=s(8046),o=s(7208);let c=s(9509).env.NEXT_PUBLIC_API_URL||"http://localhost:5001",m=e=>{let{label:t,placeholder:s="Search for a port...",value:l,onChange:n,error:m,disabled:x=!1,required:h=!1}=e,[u,g]=(0,r.useState)(""),[p,b]=(0,r.useState)([]),[j,N]=(0,r.useState)(!1),[y,f]=(0,r.useState)(!1),[v,w]=(0,r.useState)([]),D=(0,r.useRef)(null),P=(0,r.useRef)(null);(0,r.useEffect)(()=>{(async()=>{try{let e=await i.A.get("".concat(c,"/api/ports/popular?limit=8"));w(e.data.data.ports)}catch(e){console.error("Error loading popular ports:",e)}})()},[]),(0,r.useEffect)(()=>{let e=setTimeout(async()=>{if(u.length<2)return void b([]);N(!0);try{let e=await i.A.get("".concat(c,"/api/ports/search?q=").concat(encodeURIComponent(u),"&limit=10"));b(e.data.data.ports)}catch(e){console.error("Error searching ports:",e),b([])}finally{N(!1)}},300);return()=>clearTimeout(e)},[u]);let S=e=>{var t;g(e.displayName),n(e),f(!1),null==(t=D.current)||t.blur()};(0,r.useEffect)(()=>{let e=e=>{P.current&&!P.current.contains(e.target)&&f(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]),(0,r.useEffect)(()=>{l?g(l.displayName):y||g("")},[l,y]);let A=u.length<2?v:p;return(0,a.jsxs)("div",{className:"relative",ref:P,children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[t,h&&(0,a.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)(d.A,{className:"h-5 w-5 text-gray-400"})}),(0,a.jsx)("input",{ref:D,type:"text",value:u,onChange:e=>{let t=e.target.value;g(t),f(!0),!l||t.toLowerCase().includes(l.code.toLowerCase())||t.toLowerCase().includes(l.name.toLowerCase())||n(null)},onFocus:()=>{f(!0),!u&&v.length>0&&b(v)},placeholder:s,disabled:x,className:"w-full pl-10 pr-3 py-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 bg-white placeholder-gray-500 ".concat(m?"border-red-300":"border-gray-300"," ").concat(x?"bg-gray-50 cursor-not-allowed text-gray-500":"")}),j&&(0,a.jsx)("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"})})]}),m&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:m}),y&&(0,a.jsxs)("div",{className:"absolute z-10 mt-1 w-full bg-white shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none",children:[0===A.length&&!j&&u.length>=2&&(0,a.jsxs)("div",{className:"px-4 py-2 text-sm text-gray-500",children:['No ports found for "',u,'"']}),0===A.length&&!j&&u.length<2&&(0,a.jsx)("div",{className:"px-4 py-2 text-sm text-gray-500",children:"Type at least 2 characters to search"}),u.length<2&&v.length>0&&(0,a.jsx)("div",{className:"px-4 py-2 text-xs font-medium text-gray-400 uppercase tracking-wide",children:"Popular Ports"}),A.map(e=>(0,a.jsx)("button",{onClick:()=>S(e),className:"w-full text-left px-4 py-2 hover:bg-blue-50 focus:bg-blue-50 focus:outline-none",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(o.A,{className:"h-4 w-4 text-gray-400 mr-2 flex-shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"text-sm font-medium text-gray-900",children:[e.code," - ",e.name]}),(0,a.jsx)("div",{className:"text-xs text-gray-500",children:e.countryName})]})]})},e.id))]})]})};var x=s(4239);s(5279);let h=e=>{let{onSearch:t,isLoading:s=!1}=e,[i,o]=(0,r.useState)({originPort:null,destinationPort:null,departureDate:null}),[c,h]=(0,r.useState)({}),u=()=>{o(e=>({...e,originPort:e.destinationPort,destinationPort:e.originPort}))},g=()=>{let e={};return i.originPort||(e.originPort="Origin port is required"),i.destinationPort||(e.destinationPort="Destination port is required"),i.departureDate?i.departureDate<new Date&&(e.departureDate="Departure date cannot be in the past"):e.departureDate="Departure date is required",i.originPort&&i.destinationPort&&i.originPort.id===i.destinationPort.id&&(e.destinationPort="Destination port must be different from origin port"),h(e),0===Object.keys(e).length},p=new Date,b=new Date;return b.setMonth(b.getMonth()+6),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Search Shipping Routes"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Find the best shipping options across multiple carriers"})]}),(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),g()&&t(i)},className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 relative",children:[(0,a.jsx)("div",{children:(0,a.jsx)(m,{label:"Origin Port",placeholder:"Search origin port (e.g., Shanghai, SHG)",value:i.originPort,onChange:e=>{o(t=>({...t,originPort:e})),c.originPort&&h(e=>({...e,originPort:""}))},error:c.originPort,disabled:s,required:!0})}),(0,a.jsx)("div",{className:"absolute left-1/2 top-8 transform -translate-x-1/2 z-20 hidden md:block",children:(0,a.jsx)("button",{type:"button",onClick:u,disabled:s,className:"p-3 bg-white border-2 border-blue-200 text-blue-600 hover:text-blue-800 hover:bg-blue-50 hover:border-blue-300 rounded-full shadow-md transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed",title:"Swap origin and destination",children:(0,a.jsx)(l.A,{className:"h-5 w-5"})})}),(0,a.jsx)("div",{className:"md:hidden flex justify-center -my-2",children:(0,a.jsx)("button",{type:"button",onClick:u,disabled:s,className:"p-2 bg-blue-50 border border-blue-200 text-blue-600 hover:text-blue-800 hover:bg-blue-100 rounded-full transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed",title:"Swap origin and destination",children:(0,a.jsx)(l.A,{className:"h-4 w-4"})})}),(0,a.jsx)("div",{children:(0,a.jsx)(m,{label:"Destination Port",placeholder:"Search destination port (e.g., Rotterdam, RTM)",value:i.destinationPort,onChange:e=>{o(t=>({...t,destinationPort:e})),c.destinationPort&&h(e=>({...e,destinationPort:""}))},error:c.destinationPort,disabled:s,required:!0})})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Departure Date",(0,a.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)(n.A,{className:"h-5 w-5 text-gray-400"})}),(0,a.jsx)(x.Ay,{selected:i.departureDate,onChange:e=>{o(t=>({...t,departureDate:e})),c.departureDate&&h(e=>({...e,departureDate:""}))},minDate:p,maxDate:b,placeholderText:"Select departure date",disabled:s,className:"w-full pl-10 pr-3 py-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 bg-white ".concat(c.departureDate?"border-red-300":"border-gray-300"," ").concat(s?"bg-gray-50 cursor-not-allowed":""),dateFormat:"MMM dd, yyyy",showPopperArrow:!1})]}),c.departureDate&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:c.departureDate})]}),(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsx)("button",{type:"submit",disabled:s,className:"flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed min-w-[200px] justify-center",children:s?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"}),"Searching..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d.A,{className:"h-5 w-5 mr-2"}),"Search Routes"]})})})]})]})};var u=s(3930),g=s(184),p=s(2771),b=s(4500);let j=e=>{let{searchResponse:t,isLoading:s,onViewDetails:i}=e,[l,n]=(0,r.useState)(null),d=e=>new Date(e).toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"}),c=e=>{n(e),null==i||i(e)},m=e=>({MAEU:"bg-blue-600 text-white",HLCU:"bg-orange-600 text-white",CMDU:"bg-green-600 text-white",MSCU:"bg-purple-600 text-white"})[e]||"bg-gray-600 text-white";return s?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Searching across multiple carriers..."}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:"This may take a few moments"})]}):t?0===t.results.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(u.A,{className:"h-16 w-16 text-gray-300 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Routes Found"}),(0,a.jsx)("p",{className:"text-gray-600",children:"No shipping schedules found for your search criteria. Try different ports or dates."})]}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsxs)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:["Search Results (",t.totalResults,")"]}),(0,a.jsxs)("p",{className:"text-gray-600 mb-1",children:[(0,a.jsx)("span",{className:"font-medium",children:t.metadata.originPort.name})," (",t.metadata.originPort.code,") → "," ",(0,a.jsx)("span",{className:"font-medium",children:t.metadata.destinationPort.name})," (",t.metadata.destinationPort.code,")"]}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Departure: ",d(t.metadata.departureDate)," • Search completed in ",(e=>e<1e3?"".concat(e,"ms"):e<6e4?"".concat((e/1e3).toFixed(1),"s"):"".concat((e/6e4).toFixed(1),"min"))(t.searchDurationMs)," •",t.carriers.length," carrier(s)"]})]})}),(0,a.jsx)("div",{className:"space-y-4",children:t.results.map(e=>(0,a.jsx)("div",{className:"bg-white rounded-lg shadow hover:shadow-md transition-shadow duration-200",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,a.jsx)("span",{className:"px-3 py-1 rounded-full text-sm font-medium ".concat(m(e.carrier.code)),children:e.carrier.name}),e.serviceName&&(0,a.jsx)("span",{className:"px-2 py-1 bg-gray-100 text-gray-700 rounded text-sm",children:e.serviceName})]}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-1",children:e.vesselName}),(0,a.jsxs)("p",{className:"text-gray-600",children:["Voyage: ",e.voyageNumber]})]}),(0,a.jsxs)("button",{onClick:()=>c(e),className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200",children:[(0,a.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"View Details"]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(o.A,{className:"h-5 w-5 text-gray-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Departure"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:d(e.departureDate)})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(o.A,{className:"h-5 w-5 text-gray-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Arrival"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:d(e.arrivalDate)})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(p.A,{className:"h-5 w-5 text-gray-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Transit Time"}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[e.transitDays," days"]})]})]})]})]})},e.id))}),l&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:(0,a.jsx)("div",{className:"bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Route Details"}),(0,a.jsx)("button",{onClick:()=>{n(null)},className:"p-2 hover:bg-gray-100 rounded-full transition-colors duration-200",children:(0,a.jsx)(b.A,{className:"h-6 w-6 text-gray-500"})})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Vessel Information"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-700",children:"Vessel Name"}),(0,a.jsx)("p",{className:"text-lg text-gray-900",children:l.vesselName})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-700",children:"Voyage Number"}),(0,a.jsx)("p",{className:"text-lg text-gray-900",children:l.voyageNumber})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-700",children:"Carrier"}),(0,a.jsx)("p",{className:"text-lg text-gray-900",children:l.carrier.name})]}),l.serviceName&&(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-700",children:"Service"}),(0,a.jsx)("p",{className:"text-lg text-gray-900",children:l.serviceName})]})]})]}),(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Schedule"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-700",children:"Departure Date"}),(0,a.jsx)("p",{className:"text-lg text-gray-900",children:d(l.departureDate)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-700",children:"Arrival Date"}),(0,a.jsx)("p",{className:"text-lg text-gray-900",children:d(l.arrivalDate)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-700",children:"Transit Time"}),(0,a.jsxs)("p",{className:"text-lg text-gray-900",children:[l.transitDays," days"]})]})]})]}),l.portRotation&&l.portRotation.length>0&&(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Port Rotation"}),(0,a.jsx)("div",{className:"space-y-3",children:l.portRotation.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-white rounded border",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-gray-900",children:e.portName}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[e.portCode," • ",e.country]})]}),(0,a.jsxs)("div",{className:"text-right",children:[e.arrivalDate&&(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Arrival: ",d(e.arrivalDate)]}),e.departureDate&&(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Departure: ",d(e.departureDate)]})]})]},t))})]})]})]})})})]}):(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(u.A,{className:"h-16 w-16 text-gray-300 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Ready to Search"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Enter your search criteria above to find shipping routes."})]})},N=s(9509).env.NEXT_PUBLIC_API_URL||"http://localhost:5001",y=()=>{let[e,t]=(0,r.useState)(null),[s,l]=(0,r.useState)(!1),[n,d]=(0,r.useState)(""),o=async e=>{if(!e.originPort||!e.destinationPort||!e.departureDate)return void d("Please fill in all search fields");l(!0),d("");try{let s=await i.A.post("".concat(N,"/api/search/routes"),{originPortId:e.originPort.id,destinationPortId:e.destinationPort.id,departureDate:e.departureDate.toISOString().split("T")[0],carriers:["MAEU"]});t(s.data.data)}catch(t){let e="Search failed. Please try again.";if(t&&"object"==typeof t&&"response"in t){var s,a;(null==(a=t.response)||null==(s=a.data)?void 0:s.message)?e=t.response.data.message:"NETWORK_ERROR"===t.code?e="Network error. Please check your internet connection.":"ECONNABORTED"===t.code&&(e="Search request timed out. Please try again.")}d(e)}finally{l(!1)}};return(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Find Your Perfect Shipping Route"}),(0,a.jsx)("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:"Search across multiple carriers to find the best shipping options for your freight forwarding needs."})]}),(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsx)(h,{onSearch:o,isLoading:s})}),n&&(0,a.jsx)("div",{className:"mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md",children:n}),(0,a.jsx)(j,{searchResponse:e,isLoading:s,onViewDetails:e=>{console.log("View details for:",e)}})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[302,20,579,464,823,441,684,358],()=>t(1137)),_N_E=e.O()}]);