[{"/home/<USER>/vo/digital-freight/frontend/src/app/auth/page.tsx": "1", "/home/<USER>/vo/digital-freight/frontend/src/app/dashboard/page.tsx": "2", "/home/<USER>/vo/digital-freight/frontend/src/app/layout.tsx": "3", "/home/<USER>/vo/digital-freight/frontend/src/app/page.tsx": "4", "/home/<USER>/vo/digital-freight/frontend/src/app/search/page.tsx": "5", "/home/<USER>/vo/digital-freight/frontend/src/components/auth/LoginForm.tsx": "6", "/home/<USER>/vo/digital-freight/frontend/src/components/auth/ProtectedRoute.tsx": "7", "/home/<USER>/vo/digital-freight/frontend/src/components/auth/RegisterForm.tsx": "8", "/home/<USER>/vo/digital-freight/frontend/src/components/layout/Header.tsx": "9", "/home/<USER>/vo/digital-freight/frontend/src/components/search/PortAutocomplete.tsx": "10", "/home/<USER>/vo/digital-freight/frontend/src/components/search/SearchForm.tsx": "11", "/home/<USER>/vo/digital-freight/frontend/src/components/search/SearchResults.tsx": "12", "/home/<USER>/vo/digital-freight/frontend/src/contexts/AuthContext.tsx": "13"}, {"size": 2088, "mtime": 1751285014950, "results": "14", "hashOfConfig": "15"}, {"size": 6932, "mtime": 1751284798743, "results": "16", "hashOfConfig": "15"}, {"size": 862, "mtime": 1751275820478, "results": "17", "hashOfConfig": "15"}, {"size": 4740, "mtime": 1751276015634, "results": "18", "hashOfConfig": "15"}, {"size": 3354, "mtime": 1751284952866, "results": "19", "hashOfConfig": "15"}, {"size": 5010, "mtime": 1751275568406, "results": "20", "hashOfConfig": "15"}, {"size": 1149, "mtime": 1751275697184, "results": "21", "hashOfConfig": "15"}, {"size": 9073, "mtime": 1751275608930, "results": "22", "hashOfConfig": "15"}, {"size": 7321, "mtime": 1751275745064, "results": "23", "hashOfConfig": "15"}, {"size": 6620, "mtime": 1751282940854, "results": "24", "hashOfConfig": "15"}, {"size": 7631, "mtime": 1751282912117, "results": "25", "hashOfConfig": "15"}, {"size": 11917, "mtime": 1751283997716, "results": "26", "hashOfConfig": "15"}, {"size": 5634, "mtime": 1751277018956, "results": "27", "hashOfConfig": "15"}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1f994di", {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/home/<USER>/vo/digital-freight/frontend/src/app/auth/page.tsx", [], [], "/home/<USER>/vo/digital-freight/frontend/src/app/dashboard/page.tsx", [], [], "/home/<USER>/vo/digital-freight/frontend/src/app/layout.tsx", [], [], "/home/<USER>/vo/digital-freight/frontend/src/app/page.tsx", [], [], "/home/<USER>/vo/digital-freight/frontend/src/app/search/page.tsx", [], [], "/home/<USER>/vo/digital-freight/frontend/src/components/auth/LoginForm.tsx", [], [], "/home/<USER>/vo/digital-freight/frontend/src/components/auth/ProtectedRoute.tsx", [], [], "/home/<USER>/vo/digital-freight/frontend/src/components/auth/RegisterForm.tsx", [], [], "/home/<USER>/vo/digital-freight/frontend/src/components/layout/Header.tsx", [], [], "/home/<USER>/vo/digital-freight/frontend/src/components/search/PortAutocomplete.tsx", [], [], "/home/<USER>/vo/digital-freight/frontend/src/components/search/SearchForm.tsx", [], [], "/home/<USER>/vo/digital-freight/frontend/src/components/search/SearchResults.tsx", [], [], "/home/<USER>/vo/digital-freight/frontend/src/contexts/AuthContext.tsx", [], []]