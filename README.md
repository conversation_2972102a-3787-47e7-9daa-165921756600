# Digital Freight Platform

A SaaS platform for freight forwarders that aggregates carrier shipping schedules from multiple carriers (Maersk, Hapag-Lloyd, CMA CGM, etc.) into a single unified search interface.

## 🚀 Project Overview

**Purpose**: Save freight forwarders hours of manual work by providing a unified search interface instead of checking each carrier portal individually.

**Core Functionality**: 
- Input: Origin port, destination port, departure date
- Output: Structured list of carrier shipping rotations with vessel details, port rotations, dates, and transit times

## 🏗️ Architecture

- **Frontend**: Next.js + React + TypeScript + TailwindCSS
- **Backend**: Node.js + Express + TypeScript
- **Database**: PostgreSQL (planned)
- **Authentication**: NextAuth.js (planned)
- **Caching**: Redis (optional)

## 📁 Project Structure

```
digital-freight/
├── frontend/          # Next.js React application
│   ├── src/
│   ├── public/
│   └── package.json
├── backend/           # Node.js Express API
│   ├── src/
│   │   ├── controllers/
│   │   ├── middleware/
│   │   ├── models/
│   │   ├── routes/
│   │   ├── services/
│   │   ├── types/
│   │   ├── utils/
│   │   └── config/
│   └── package.json
└── README.md
```

## 🛠️ Development Setup

### Prerequisites
- Node.js (v18 or higher)
- npm or yarn
- PostgreSQL (for production)

### Frontend Setup
```bash
cd frontend
npm install
npm run dev
```
Frontend will run on http://localhost:3000

### Backend Setup
```bash
cd backend
npm install
cp .env.example .env
# Edit .env with your configuration
npm run dev
```
Backend will run on http://localhost:5000

## 🎯 Features (Planned)

### Frontend Features
- [x] Project setup with Next.js + TypeScript + TailwindCSS
- [ ] Search form with port selection and date picker
- [ ] Results table with sorting and filtering
- [ ] Export functionality (CSV/PDF)
- [ ] User authentication and dashboard
- [ ] Responsive design

### Backend Features
- [x] Express server with TypeScript
- [x] Security middleware (helmet, CORS, rate limiting)
- [x] Environment configuration
- [ ] Database integration (PostgreSQL)
- [ ] Authentication system
- [ ] Carrier API integration
- [ ] Response normalization
- [ ] Caching layer

## 🔧 Available Scripts

### Frontend
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

### Backend
- `npm run dev` - Start development server with nodemon
- `npm run build` - Compile TypeScript
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run format` - Format code with Prettier

## 🌟 Next Steps

1. Database setup and schema design
2. Authentication system implementation
3. Port data management
4. Search interface development
5. Results display components
6. Backend API development
7. Carrier API integration
8. Testing and optimization

## 📝 License

ISC
